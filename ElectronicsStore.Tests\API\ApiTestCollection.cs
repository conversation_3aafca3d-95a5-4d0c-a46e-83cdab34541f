using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using FluentAssertions;
using System.Net.Http.Json;
using System.Net;
using System.Text.Json;
using ElectronicsStore.Application.DTOs;
using ElectronicsStore.Persistence;
using ElectronicsStore.Domain.Entities;

namespace ElectronicsStore.Tests.API
{
    public class ApiTestCollection : IClassFixture<WebApplicationFactory<Program>>
    {
        private readonly WebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;
        private readonly JsonSerializerOptions _jsonOptions;

        public ApiTestCollection(WebApplicationFactory<Program> factory)
        {
            _factory = factory.WithWebHostBuilder(builder =>
            {
                builder.ConfigureServices(services =>
                {
                    var descriptor = services.SingleOrDefault(d => d.ServiceType == typeof(DbContextOptions<ElectronicsDbContext>));
                    if (descriptor != null)
                        services.Remove(descriptor);

                    services.AddDbContext<ElectronicsDbContext>(options =>
                    {
                        options.UseInMemoryDatabase($"TestDb_{Guid.NewGuid()}");
                    });
                });
            });

            _client = _factory.CreateClient();
            _jsonOptions = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
        }

        [Fact]
        public async Task AuthEndpoints_ShouldWorkCorrectly()
        {
            // Arrange
            await SeedTestData();

            // Test Login
            var loginDto = new { Username = "admin", Password = "password" };
            var loginResponse = await _client.PostAsJsonAsync("/api/auth/login", loginDto);
            
            loginResponse.StatusCode.Should().Be(HttpStatusCode.OK);
            var loginResult = await loginResponse.Content.ReadAsStringAsync();
            loginResult.Should().Contain("token");

            // Test Register
            var registerDto = new 
            { 
                Username = "newuser", 
                Email = "<EMAIL>", 
                Password = "NewPassword123!",
                RoleId = 3
            };
            var registerResponse = await _client.PostAsJsonAsync("/api/auth/register", registerDto);
            registerResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        }

        [Fact]
        public async Task CategoryEndpoints_ShouldWorkCorrectly()
        {
            // Arrange
            await SeedTestData();
            await AuthenticateAsAdmin();

            // Test GET all categories
            var getAllResponse = await _client.GetAsync("/api/categories");
            getAllResponse.StatusCode.Should().Be(HttpStatusCode.OK);

            // Test GET category by ID
            var getByIdResponse = await _client.GetAsync("/api/categories/1");
            getByIdResponse.StatusCode.Should().Be(HttpStatusCode.OK);

            // Test POST create category
            var createDto = new CreateCategoryDto { Name = "API Test Category" };
            var createResponse = await _client.PostAsJsonAsync("/api/categories", createDto);
            createResponse.StatusCode.Should().Be(HttpStatusCode.Created);

            // Test PUT update category
            var updateDto = new UpdateCategoryDto { Id = 1, Name = "Updated API Category" };
            var updateResponse = await _client.PutAsJsonAsync("/api/categories/1", updateDto);
            updateResponse.StatusCode.Should().Be(HttpStatusCode.OK);

            // Test GET search categories
            var searchResponse = await _client.GetAsync("/api/categories/search?searchTerm=API");
            searchResponse.StatusCode.Should().Be(HttpStatusCode.OK);

            // Test GET paged categories
            var pagedResponse = await _client.GetAsync("/api/categories/paged?pageNumber=1&pageSize=10");
            pagedResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        }

        [Fact]
        public async Task ProductEndpoints_ShouldWorkCorrectly()
        {
            // Arrange
            await SeedTestData();
            await AuthenticateAsAdmin();

            // Test GET all products
            var getAllResponse = await _client.GetAsync("/api/products");
            getAllResponse.StatusCode.Should().Be(HttpStatusCode.OK);

            // Test GET product by ID
            var getByIdResponse = await _client.GetAsync("/api/products/1");
            getByIdResponse.StatusCode.Should().Be(HttpStatusCode.OK);

            // Test POST create product
            var createDto = new CreateProductDto 
            { 
                Name = "API Test Product",
                CategoryId = 1,
                Price = 99.99m,
                Quantity = 50,
                Barcode = "API123456789"
            };
            var createResponse = await _client.PostAsJsonAsync("/api/products", createDto);
            createResponse.StatusCode.Should().Be(HttpStatusCode.Created);
        }

        [Fact]
        public async Task SupplierEndpoints_ShouldWorkCorrectly()
        {
            // Arrange
            await SeedTestData();
            await AuthenticateAsAdmin();

            // Test GET all suppliers
            var getAllResponse = await _client.GetAsync("/api/suppliers");
            getAllResponse.StatusCode.Should().Be(HttpStatusCode.OK);

            // Test POST create supplier
            var createDto = new CreateSupplierDto 
            { 
                Name = "API Test Supplier",
                Email = "<EMAIL>",
                Phone = "1234567890",
                Address = "API Test Address"
            };
            var createResponse = await _client.PostAsJsonAsync("/api/suppliers", createDto);
            createResponse.StatusCode.Should().Be(HttpStatusCode.Created);
        }

        [Fact]
        public async Task SalesEndpoints_ShouldWorkCorrectly()
        {
            // Arrange
            await SeedTestData();
            await AuthenticateAsAdmin();

            // Test GET all sales
            var getAllResponse = await _client.GetAsync("/api/sales");
            getAllResponse.StatusCode.Should().Be(HttpStatusCode.OK);

            // Test POST create sale
            var createDto = new CreateSalesInvoiceDto
            {
                CustomerId = 1,
                Items = new List<CreateSalesInvoiceItemDto>
                {
                    new CreateSalesInvoiceItemDto { ProductId = 1, Quantity = 2, UnitPrice = 500 }
                }
            };
            var createResponse = await _client.PostAsJsonAsync("/api/sales", createDto);
            createResponse.StatusCode.Should().Be(HttpStatusCode.Created);
        }

        [Fact]
        public async Task PurchaseEndpoints_ShouldWorkCorrectly()
        {
            // Arrange
            await SeedTestData();
            await AuthenticateAsAdmin();

            // Test GET all purchases
            var getAllResponse = await _client.GetAsync("/api/purchases");
            getAllResponse.StatusCode.Should().Be(HttpStatusCode.OK);

            // Test POST create purchase
            var createDto = new CreatePurchaseInvoiceDto
            {
                SupplierId = 1,
                Items = new List<CreatePurchaseInvoiceItemDto>
                {
                    new CreatePurchaseInvoiceItemDto { ProductId = 1, Quantity = 10, UnitCost = 400 }
                }
            };
            var createResponse = await _client.PostAsJsonAsync("/api/purchases", createDto);
            createResponse.StatusCode.Should().Be(HttpStatusCode.Created);
        }

        [Fact]
        public async Task InventoryEndpoints_ShouldWorkCorrectly()
        {
            // Arrange
            await SeedTestData();
            await AuthenticateAsAdmin();

            // Test GET inventory summary
            var summaryResponse = await _client.GetAsync("/api/inventory/summary");
            summaryResponse.StatusCode.Should().Be(HttpStatusCode.OK);

            // Test GET low stock products
            var lowStockResponse = await _client.GetAsync("/api/inventory/low-stock");
            lowStockResponse.StatusCode.Should().Be(HttpStatusCode.OK);

            // Test GET inventory movements
            var movementsResponse = await _client.GetAsync("/api/inventory/movements");
            movementsResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        }

        [Fact]
        public async Task PerformanceEndpoints_ShouldWorkCorrectly()
        {
            // Arrange
            await SeedTestData();
            await AuthenticateAsAdmin();

            // Test GET performance statistics
            var statsResponse = await _client.GetAsync("/api/performance/statistics");
            statsResponse.StatusCode.Should().Be(HttpStatusCode.OK);

            // Test GET system health
            var healthResponse = await _client.GetAsync("/api/performance/health");
            healthResponse.StatusCode.Should().Be(HttpStatusCode.OK);

            // Test GET performance trends
            var trendsResponse = await _client.GetAsync("/api/performance/trends");
            trendsResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        }

        [Fact]
        public async Task ErrorHandling_ShouldWorkCorrectly()
        {
            // Arrange
            await SeedTestData();
            await AuthenticateAsAdmin();

            // Test 404 Not Found
            var notFoundResponse = await _client.GetAsync("/api/categories/999");
            notFoundResponse.StatusCode.Should().Be(HttpStatusCode.NotFound);

            // Test 400 Bad Request with invalid data
            var invalidDto = new CreateCategoryDto { Name = "" }; // Empty name
            var badRequestResponse = await _client.PostAsJsonAsync("/api/categories", invalidDto);
            badRequestResponse.StatusCode.Should().Be(HttpStatusCode.BadRequest);

            // Test 401 Unauthorized
            _client.DefaultRequestHeaders.Authorization = null;
            var unauthorizedResponse = await _client.GetAsync("/api/categories");
            unauthorizedResponse.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
        }

        [Fact]
        public async Task AuthorizationRoles_ShouldWorkCorrectly()
        {
            // Arrange
            await SeedTestData();

            // Test Admin access
            await AuthenticateAsAdmin();
            var adminResponse = await _client.DeleteAsync("/api/categories/2");
            adminResponse.StatusCode.Should().BeOneOf(HttpStatusCode.NoContent, HttpStatusCode.BadRequest);

            // Test Cashier restrictions
            await AuthenticateAsCashier();
            var cashierResponse = await _client.DeleteAsync("/api/categories/2");
            cashierResponse.StatusCode.Should().Be(HttpStatusCode.Forbidden);
        }

        private async Task SeedTestData()
        {
            using var scope = _factory.Services.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ElectronicsDbContext>();
            
            await context.Database.EnsureDeletedAsync();
            await context.Database.EnsureCreatedAsync();

            // Seed all necessary test data
            var roles = new List<Role>
            {
                new Role { Id = 1, Name = "admin" },
                new Role { Id = 2, Name = "manager" },
                new Role { Id = 3, Name = "cashier" }
            };
            context.Roles.AddRange(roles);

            var users = new List<User>
            {
                new User { Id = 1, Username = "admin", Email = "<EMAIL>", PasswordHash = BCrypt.Net.BCrypt.HashPassword("password"), RoleId = 1 },
                new User { Id = 2, Username = "cashier", Email = "<EMAIL>", PasswordHash = BCrypt.Net.BCrypt.HashPassword("password"), RoleId = 3 }
            };
            context.Users.AddRange(users);

            var categories = new List<Category>
            {
                new Category { Id = 1, Name = "Electronics" },
                new Category { Id = 2, Name = "Clothing" }
            };
            context.Categories.AddRange(categories);

            var suppliers = new List<Supplier>
            {
                new Supplier { Id = 1, Name = "Tech Supplier", Email = "<EMAIL>", Phone = "1234567890", Address = "Tech Street" }
            };
            context.Suppliers.AddRange(suppliers);

            var customers = new List<Customer>
            {
                new Customer { Id = 1, Name = "Test Customer", Email = "<EMAIL>", Phone = "0987654321", Address = "Customer Street" }
            };
            context.Customers.AddRange(customers);

            var products = new List<Product>
            {
                new Product { Id = 1, Name = "Laptop", CategoryId = 1, Price = 1000, Quantity = 10, Barcode = "123456789" }
            };
            context.Products.AddRange(products);

            await context.SaveChangesAsync();
        }

        private async Task AuthenticateAsAdmin()
        {
            var loginDto = new { Username = "admin", Password = "password" };
            var response = await _client.PostAsJsonAsync("/api/auth/login", loginDto);
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var loginResponse = JsonSerializer.Deserialize<JsonElement>(content);
                var token = loginResponse.GetProperty("token").GetString();
                _client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            }
        }

        private async Task AuthenticateAsCashier()
        {
            var loginDto = new { Username = "cashier", Password = "password" };
            var response = await _client.PostAsJsonAsync("/api/auth/login", loginDto);
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var loginResponse = JsonSerializer.Deserialize<JsonElement>(content);
                var token = loginResponse.GetProperty("token").GetString();
                _client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            }
        }
    }
}
