using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ElectronicsStore.Application.DTOs;
using ElectronicsStore.Application.Services;
using System.Security.Claims;

namespace ElectronicsStore.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class PurchasesController : ControllerBase
    {
        private readonly IPurchaseService _purchaseService;

        public PurchasesController(IPurchaseService purchaseService)
        {
            _purchaseService = purchaseService;
        }

        /// <summary>
        /// Get all purchase invoices
        /// </summary>
        /// <returns>List of purchase invoices</returns>
        [HttpGet]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<PurchaseInvoiceDto>>> GetPurchaseInvoices()
        {
            var invoices = await _purchaseService.GetAllPurchaseInvoicesAsync();
            return Ok(invoices);
        }

        /// <summary>
        /// Get purchase invoice by ID
        /// </summary>
        /// <param name="id">Purchase invoice ID</param>
        /// <returns>Purchase invoice details</returns>
        [HttpGet("{id}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<PurchaseInvoiceDto>> GetPurchaseInvoice(int id)
        {
            var invoice = await _purchaseService.GetPurchaseInvoiceByIdAsync(id);
            if (invoice == null)
                return NotFound($"Purchase invoice with ID {id} not found.");

            return Ok(invoice);
        }

        /// <summary>
        /// Create a new purchase invoice
        /// </summary>
        /// <param name="createDto">Purchase invoice creation data</param>
        /// <returns>Created purchase invoice</returns>
        [HttpPost]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<PurchaseInvoiceDto>> CreatePurchaseInvoice(CreatePurchaseInvoiceDto createDto)
        {
            try
            {
                var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
                var invoice = await _purchaseService.CreatePurchaseInvoiceAsync(createDto, userId);
                return CreatedAtAction(nameof(GetPurchaseInvoice), new { id = invoice.Id }, invoice);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Update an existing purchase invoice
        /// </summary>
        /// <param name="id">Purchase invoice ID</param>
        /// <param name="updateDto">Purchase invoice update data</param>
        /// <returns>Updated purchase invoice</returns>
        [HttpPut("{id}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<PurchaseInvoiceDto>> UpdatePurchaseInvoice(int id, UpdatePurchaseInvoiceDto updateDto)
        {
            if (id != updateDto.Id)
                return BadRequest("ID mismatch between route and body.");

            try
            {
                var invoice = await _purchaseService.UpdatePurchaseInvoiceAsync(updateDto);
                return Ok(invoice);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Delete a purchase invoice
        /// </summary>
        /// <param name="id">Purchase invoice ID</param>
        /// <returns>Success status</returns>
        [HttpDelete("{id}")]
        [Authorize(Roles = "admin")]
        public async Task<ActionResult> DeletePurchaseInvoice(int id)
        {
            try
            {
                var result = await _purchaseService.DeletePurchaseInvoiceAsync(id);
                if (!result)
                    return NotFound($"Purchase invoice with ID {id} not found.");

                return NoContent();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Search purchase invoices
        /// </summary>
        /// <param name="searchDto">Search criteria</param>
        /// <returns>List of matching purchase invoices</returns>
        [HttpGet("search")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<PurchaseInvoiceDto>>> SearchPurchaseInvoices([FromQuery] PurchaseSearchDto searchDto)
        {
            var invoices = await _purchaseService.SearchPurchaseInvoicesAsync(searchDto);
            return Ok(invoices);
        }

        /// <summary>
        /// Get purchase invoice by invoice number
        /// </summary>
        /// <param name="invoiceNumber">Invoice number</param>
        /// <returns>Purchase invoice details</returns>
        [HttpGet("invoice/{invoiceNumber}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<PurchaseInvoiceDto>> GetPurchaseInvoiceByNumber(string invoiceNumber)
        {
            var invoice = await _purchaseService.GetPurchaseInvoiceByNumberAsync(invoiceNumber);
            if (invoice == null)
                return NotFound($"Purchase invoice with number '{invoiceNumber}' not found.");

            return Ok(invoice);
        }

        /// <summary>
        /// Get purchase invoices by supplier
        /// </summary>
        /// <param name="supplierId">Supplier ID</param>
        /// <returns>List of purchase invoices for the supplier</returns>
        [HttpGet("supplier/{supplierId}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<PurchaseInvoiceDto>>> GetPurchaseInvoicesBySupplier(int supplierId)
        {
            var invoices = await _purchaseService.GetPurchaseInvoicesBySupplierAsync(supplierId);
            return Ok(invoices);
        }

        /// <summary>
        /// Get purchase invoices by user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>List of purchase invoices created by the user</returns>
        [HttpGet("user/{userId}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<PurchaseInvoiceDto>>> GetPurchaseInvoicesByUser(int userId)
        {
            var invoices = await _purchaseService.GetPurchaseInvoicesByUserAsync(userId);
            return Ok(invoices);
        }

        /// <summary>
        /// Get purchase invoices by date range
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <returns>List of purchase invoices in the date range</returns>
        [HttpGet("date-range")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<PurchaseInvoiceDto>>> GetPurchaseInvoicesByDateRange(
            [FromQuery] DateTime fromDate, [FromQuery] DateTime toDate)
        {
            var invoices = await _purchaseService.GetPurchaseInvoicesByDateRangeAsync(fromDate, toDate);
            return Ok(invoices);
        }

        /// <summary>
        /// Get today's purchases
        /// </summary>
        /// <returns>List of today's purchase invoices</returns>
        [HttpGet("today")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<PurchaseInvoiceDto>>> GetTodayPurchases()
        {
            var invoices = await _purchaseService.GetTodayPurchasesAsync();
            return Ok(invoices);
        }

        /// <summary>
        /// Get today's purchases total
        /// </summary>
        /// <returns>Total amount of today's purchases</returns>
        [HttpGet("today/total")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<object>> GetTodayPurchasesTotal()
        {
            var total = await _purchaseService.GetTodayPurchasesTotalAsync();
            return Ok(new { Date = DateTime.Today, TotalAmount = total });
        }

        /// <summary>
        /// Get daily purchase report
        /// </summary>
        /// <param name="date">Report date</param>
        /// <returns>Daily purchase report</returns>
        [HttpGet("reports/daily")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<PurchaseReportDto>>> GetDailyPurchaseReport([FromQuery] DateTime date)
        {
            var report = await _purchaseService.GetDailyPurchaseReportAsync(date);
            return Ok(report);
        }

        /// <summary>
        /// Get monthly purchase report
        /// </summary>
        /// <param name="year">Report year</param>
        /// <param name="month">Report month</param>
        /// <returns>Monthly purchase report</returns>
        [HttpGet("reports/monthly")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<PurchaseReportDto>>> GetMonthlyPurchaseReport(
            [FromQuery] int year, [FromQuery] int month)
        {
            var report = await _purchaseService.GetMonthlyPurchaseReportAsync(year, month);
            return Ok(report);
        }

        /// <summary>
        /// Get yearly purchase report
        /// </summary>
        /// <param name="year">Report year</param>
        /// <returns>Yearly purchase report</returns>
        [HttpGet("reports/yearly")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<PurchaseReportDto>>> GetYearlyPurchaseReport([FromQuery] int year)
        {
            var report = await _purchaseService.GetYearlyPurchaseReportAsync(year);
            return Ok(report);
        }

        /// <summary>
        /// Get purchase statistics
        /// </summary>
        /// <returns>Purchase statistics</returns>
        [HttpGet("statistics")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<PurchaseStatisticsDto>> GetPurchaseStatistics()
        {
            var statistics = await _purchaseService.GetPurchaseStatisticsAsync();
            return Ok(statistics);
        }

        /// <summary>
        /// Get top suppliers by purchase amount
        /// </summary>
        /// <param name="count">Number of suppliers to return</param>
        /// <returns>List of top suppliers</returns>
        [HttpGet("top-suppliers")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<object>>> GetTopSuppliers([FromQuery] int count = 10)
        {
            var suppliers = await _purchaseService.GetTopSuppliersAsync(count);
            return Ok(suppliers);
        }

        /// <summary>
        /// Get top products by purchase amount
        /// </summary>
        /// <param name="count">Number of products to return</param>
        /// <returns>List of top products</returns>
        [HttpGet("top-products")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<object>>> GetTopProducts([FromQuery] int count = 10)
        {
            var products = await _purchaseService.GetTopProductsAsync(count);
            return Ok(products);
        }

        /// <summary>
        /// Get paged purchase invoices
        /// </summary>
        /// <param name="pageNumber">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="searchDto">Search criteria (optional)</param>
        /// <returns>Paged list of purchase invoices</returns>
        [HttpGet("paged")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<object>> GetPagedPurchaseInvoices(
            [FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10, [FromQuery] PurchaseSearchDto? searchDto = null)
        {
            var (invoices, totalCount) = await _purchaseService.GetPagedPurchaseInvoicesAsync(pageNumber, pageSize, searchDto);

            return Ok(new
            {
                Invoices = invoices,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            });
        }

        /// <summary>
        /// Get purchase invoice details
        /// </summary>
        /// <param name="invoiceId">Purchase invoice ID</param>
        /// <returns>List of invoice details</returns>
        [HttpGet("{invoiceId}/details")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<PurchaseInvoiceDetailDto>>> GetPurchaseInvoiceDetails(int invoiceId)
        {
            var details = await _purchaseService.GetPurchaseInvoiceDetailsAsync(invoiceId);
            return Ok(details);
        }

        /// <summary>
        /// Get purchase invoice detail by ID
        /// </summary>
        /// <param name="detailId">Detail ID</param>
        /// <returns>Purchase invoice detail</returns>
        [HttpGet("details/{detailId}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<PurchaseInvoiceDetailDto>> GetPurchaseInvoiceDetail(int detailId)
        {
            var detail = await _purchaseService.GetPurchaseInvoiceDetailByIdAsync(detailId);
            if (detail == null)
                return NotFound($"Purchase invoice detail with ID {detailId} not found.");

            return Ok(detail);
        }

        /// <summary>
        /// Check if invoice number exists
        /// </summary>
        /// <param name="invoiceNumber">Invoice number to check</param>
        /// <param name="excludeId">Invoice ID to exclude from check (for updates)</param>
        /// <returns>Availability status</returns>
        [HttpGet("check-invoice-number/{invoiceNumber}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<object>> CheckInvoiceNumber(string invoiceNumber, [FromQuery] int? excludeId = null)
        {
            var exists = await _purchaseService.InvoiceNumberExistsAsync(invoiceNumber, excludeId);
            return Ok(new { InvoiceNumber = invoiceNumber, Exists = exists, Available = !exists });
        }

        /// <summary>
        /// Check if purchase invoice can be deleted
        /// </summary>
        /// <param name="id">Purchase invoice ID</param>
        /// <returns>Deletion status</returns>
        [HttpGet("{id}/can-delete")]
        [Authorize(Roles = "admin")]
        public async Task<ActionResult<object>> CanDeletePurchaseInvoice(int id)
        {
            var canDelete = await _purchaseService.CanDeletePurchaseInvoiceAsync(id);
            return Ok(new { InvoiceId = id, CanDelete = canDelete });
        }
    }
}
