﻿// <auto-generated />
using System;
using ElectronicsStore.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace ElectronicsStore.Persistence.Migrations
{
    [DbContext(typeof(ElectronicsDbContext))]
    partial class ElectronicsDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.8")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.Category", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("name");

                    b.HasKey("Id");

                    b.ToTable("categories");
                });

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.CogsView", b =>
                {
                    b.Property<int>("SalesInvoiceId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("sales_invoice_id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SalesInvoiceId"));

                    b.Property<decimal>("CostOfGoodsSold")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("cost_of_goods_sold");

                    b.Property<string>("InvoiceNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("invoice_number");

                    b.HasKey("SalesInvoiceId");

                    b.ToTable("cogs_view");

                    b.ToView("cogs_view", (string)null);
                });

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.Expense", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(12,2)")
                        .HasColumnName("amount");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_at");

                    b.Property<string>("ExpenseType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("expense_type");

                    b.Property<string>("Note")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("note");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("user_id");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("expenses");
                });

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.InventoryLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_at");

                    b.Property<string>("MovementType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("movement_type");

                    b.Property<string>("Note")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("note");

                    b.Property<int>("ProductId")
                        .HasColumnType("int")
                        .HasColumnName("product_id");

                    b.Property<int>("Quantity")
                        .HasColumnType("int")
                        .HasColumnName("quantity");

                    b.Property<int>("ReferenceId")
                        .HasColumnType("int")
                        .HasColumnName("reference_id");

                    b.Property<string>("ReferenceTable")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("reference_tbl");

                    b.Property<decimal>("UnitCost")
                        .HasColumnType("decimal(10,2)")
                        .HasColumnName("unit_cost");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("user_id");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("UserId");

                    b.ToTable("inventory_logs");
                });

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.InventoryValuationView", b =>
                {
                    b.Property<int>("ProductId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("product_id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ProductId"));

                    b.Property<string>("ProductName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("product_name");

                    b.Property<decimal>("TotalValue")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("total_value");

                    b.HasKey("ProductId");

                    b.ToTable("inventory_valuation_view");

                    b.ToView("inventory_valuation_view", (string)null);
                });

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.InventoryView", b =>
                {
                    b.Property<int>("ProductId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("product_id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ProductId"));

                    b.Property<int>("CurrentQuantity")
                        .HasColumnType("int")
                        .HasColumnName("current_quantity");

                    b.Property<string>("ProductName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("product_name");

                    b.HasKey("ProductId");

                    b.ToTable("inventory_view");

                    b.ToView("inventory_view", (string)null);
                });

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.Permission", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("description");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("name");

                    b.HasKey("Id");

                    b.ToTable("permissions");
                });

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.Product", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Barcode")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("barcode");

                    b.Property<int>("CategoryId")
                        .HasColumnType("int")
                        .HasColumnName("category_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_at");

                    b.Property<decimal>("DefaultCostPrice")
                        .HasColumnType("decimal(10,2)")
                        .HasColumnName("default_cost_price");

                    b.Property<decimal>("DefaultSellingPrice")
                        .HasColumnType("decimal(10,2)")
                        .HasColumnName("default_selling_price");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("description");

                    b.Property<decimal>("MinSellingPrice")
                        .HasColumnType("decimal(10,2)")
                        .HasColumnName("min_selling_price");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)")
                        .HasColumnName("name");

                    b.Property<int?>("SupplierId")
                        .HasColumnType("int")
                        .HasColumnName("supplier_id");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.HasIndex("SupplierId");

                    b.ToTable("products");
                });

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.PurchaseInvoice", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("InvoiceDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("invoice_date");

                    b.Property<string>("InvoiceNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("invoice_number");

                    b.Property<int>("SupplierId")
                        .HasColumnType("int")
                        .HasColumnName("supplier_id");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(14,2)")
                        .HasColumnName("total_amount");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("user_id");

                    b.HasKey("Id");

                    b.HasIndex("SupplierId");

                    b.HasIndex("UserId");

                    b.ToTable("purchase_invoices");
                });

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.PurchaseInvoiceDetail", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("LineTotal")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("decimal(10,2)")
                        .HasColumnName("line_total")
                        .HasComputedColumnSql("[quantity] * [unit_cost]");

                    b.Property<int>("ProductId")
                        .HasColumnType("int")
                        .HasColumnName("product_id");

                    b.Property<int>("PurchaseInvoiceId")
                        .HasColumnType("int")
                        .HasColumnName("purchase_invoice_id");

                    b.Property<int>("Quantity")
                        .HasColumnType("int")
                        .HasColumnName("quantity");

                    b.Property<decimal>("UnitCost")
                        .HasColumnType("decimal(10,2)")
                        .HasColumnName("unit_cost");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("PurchaseInvoiceId");

                    b.ToTable("purchase_invoice_details");

                    b.HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.PurchaseReturn", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_at");

                    b.Property<int>("ProductId")
                        .HasColumnType("int")
                        .HasColumnName("product_id");

                    b.Property<int>("PurchaseInvoiceId")
                        .HasColumnType("int")
                        .HasColumnName("purchase_invoice_id");

                    b.Property<int>("Quantity")
                        .HasColumnType("int")
                        .HasColumnName("quantity");

                    b.Property<string>("Reason")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("reason");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("user_id");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("PurchaseInvoiceId");

                    b.HasIndex("UserId");

                    b.ToTable("purchase_returns");
                });

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.Role", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("name");

                    b.HasKey("Id");

                    b.ToTable("roles");
                });

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.RolePermission", b =>
                {
                    b.Property<int>("RoleId")
                        .HasColumnType("int")
                        .HasColumnName("role_id");

                    b.Property<int>("PermissionId")
                        .HasColumnType("int")
                        .HasColumnName("permission_id");

                    b.HasKey("RoleId", "PermissionId");

                    b.HasIndex("PermissionId");

                    b.ToTable("role_permissions");
                });

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.SalesInvoice", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CustomerName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("customer_name");

                    b.Property<decimal>("DiscountTotal")
                        .HasColumnType("decimal(12,2)")
                        .HasColumnName("discount_total");

                    b.Property<DateTime>("InvoiceDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("invoice_date");

                    b.Property<string>("InvoiceNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("invoice_number");

                    b.Property<int?>("OverrideByUserId")
                        .HasColumnType("int")
                        .HasColumnName("override_by_user_id");

                    b.Property<DateTime?>("OverrideDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("override_date");

                    b.Property<string>("PaymentMethod")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("payment_method");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(14,2)")
                        .HasColumnName("total_amount");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("user_id");

                    b.HasKey("Id");

                    b.HasIndex("OverrideByUserId");

                    b.HasIndex("UserId");

                    b.ToTable("sales_invoices");
                });

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.SalesInvoiceDetail", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("DiscountAmount")
                        .HasColumnType("decimal(10,2)")
                        .HasColumnName("discount_amount");

                    b.Property<decimal>("LineTotal")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("decimal(10,2)")
                        .HasColumnName("line_total")
                        .HasComputedColumnSql("([unit_price] - [discount_amount]) * [quantity]");

                    b.Property<int>("ProductId")
                        .HasColumnType("int")
                        .HasColumnName("product_id");

                    b.Property<int>("Quantity")
                        .HasColumnType("int")
                        .HasColumnName("quantity");

                    b.Property<int>("SalesInvoiceId")
                        .HasColumnType("int")
                        .HasColumnName("sales_invoice_id");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("decimal(10,2)")
                        .HasColumnName("unit_price");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("SalesInvoiceId");

                    b.ToTable("sales_invoice_details");

                    b.HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.SalesReturn", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_at");

                    b.Property<int>("ProductId")
                        .HasColumnType("int")
                        .HasColumnName("product_id");

                    b.Property<int>("Quantity")
                        .HasColumnType("int")
                        .HasColumnName("quantity");

                    b.Property<string>("Reason")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("reason");

                    b.Property<int>("SalesInvoiceId")
                        .HasColumnType("int")
                        .HasColumnName("sales_invoice_id");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("user_id");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("SalesInvoiceId");

                    b.HasIndex("UserId");

                    b.ToTable("sales_returns");
                });

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.Supplier", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Address")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("address");

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("email");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("name");

                    b.Property<string>("Phone")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("phone");

                    b.HasKey("Id");

                    b.ToTable("suppliers");
                });

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_at");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("password");

                    b.Property<int>("RoleId")
                        .HasColumnType("int")
                        .HasColumnName("role_id");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("username");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("users");
                });

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.Expense", b =>
                {
                    b.HasOne("ElectronicsStore.Domain.Entities.User", "User")
                        .WithMany("Expenses")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.InventoryLog", b =>
                {
                    b.HasOne("ElectronicsStore.Domain.Entities.Product", "Product")
                        .WithMany("InventoryLogs")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ElectronicsStore.Domain.Entities.User", "User")
                        .WithMany("InventoryLogs")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("User");
                });

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.Product", b =>
                {
                    b.HasOne("ElectronicsStore.Domain.Entities.Category", "Category")
                        .WithMany("Products")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ElectronicsStore.Domain.Entities.Supplier", "Supplier")
                        .WithMany("Products")
                        .HasForeignKey("SupplierId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Category");

                    b.Navigation("Supplier");
                });

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.PurchaseInvoice", b =>
                {
                    b.HasOne("ElectronicsStore.Domain.Entities.Supplier", "Supplier")
                        .WithMany("PurchaseInvoices")
                        .HasForeignKey("SupplierId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ElectronicsStore.Domain.Entities.User", "User")
                        .WithMany("PurchaseInvoices")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Supplier");

                    b.Navigation("User");
                });

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.PurchaseInvoiceDetail", b =>
                {
                    b.HasOne("ElectronicsStore.Domain.Entities.Product", "Product")
                        .WithMany("PurchaseInvoiceDetails")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ElectronicsStore.Domain.Entities.PurchaseInvoice", "PurchaseInvoice")
                        .WithMany("PurchaseInvoiceDetails")
                        .HasForeignKey("PurchaseInvoiceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("PurchaseInvoice");
                });

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.PurchaseReturn", b =>
                {
                    b.HasOne("ElectronicsStore.Domain.Entities.Product", "Product")
                        .WithMany("PurchaseReturns")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ElectronicsStore.Domain.Entities.PurchaseInvoice", "PurchaseInvoice")
                        .WithMany("PurchaseReturns")
                        .HasForeignKey("PurchaseInvoiceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ElectronicsStore.Domain.Entities.User", "User")
                        .WithMany("PurchaseReturns")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("PurchaseInvoice");

                    b.Navigation("User");
                });

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.RolePermission", b =>
                {
                    b.HasOne("ElectronicsStore.Domain.Entities.Permission", "Permission")
                        .WithMany("RolePermissions")
                        .HasForeignKey("PermissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ElectronicsStore.Domain.Entities.Role", "Role")
                        .WithMany("RolePermissions")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Permission");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.SalesInvoice", b =>
                {
                    b.HasOne("ElectronicsStore.Domain.Entities.User", "OverrideByUser")
                        .WithMany("OverriddenSalesInvoices")
                        .HasForeignKey("OverrideByUserId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ElectronicsStore.Domain.Entities.User", "User")
                        .WithMany("SalesInvoices")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("OverrideByUser");

                    b.Navigation("User");
                });

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.SalesInvoiceDetail", b =>
                {
                    b.HasOne("ElectronicsStore.Domain.Entities.Product", "Product")
                        .WithMany("SalesInvoiceDetails")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ElectronicsStore.Domain.Entities.SalesInvoice", "SalesInvoice")
                        .WithMany("SalesInvoiceDetails")
                        .HasForeignKey("SalesInvoiceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("SalesInvoice");
                });

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.SalesReturn", b =>
                {
                    b.HasOne("ElectronicsStore.Domain.Entities.Product", "Product")
                        .WithMany("SalesReturns")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ElectronicsStore.Domain.Entities.SalesInvoice", "SalesInvoice")
                        .WithMany("SalesReturns")
                        .HasForeignKey("SalesInvoiceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ElectronicsStore.Domain.Entities.User", "User")
                        .WithMany("SalesReturns")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("SalesInvoice");

                    b.Navigation("User");
                });

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.User", b =>
                {
                    b.HasOne("ElectronicsStore.Domain.Entities.Role", "Role")
                        .WithMany("Users")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Role");
                });

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.Category", b =>
                {
                    b.Navigation("Products");
                });

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.Permission", b =>
                {
                    b.Navigation("RolePermissions");
                });

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.Product", b =>
                {
                    b.Navigation("InventoryLogs");

                    b.Navigation("PurchaseInvoiceDetails");

                    b.Navigation("PurchaseReturns");

                    b.Navigation("SalesInvoiceDetails");

                    b.Navigation("SalesReturns");
                });

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.PurchaseInvoice", b =>
                {
                    b.Navigation("PurchaseInvoiceDetails");

                    b.Navigation("PurchaseReturns");
                });

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.Role", b =>
                {
                    b.Navigation("RolePermissions");

                    b.Navigation("Users");
                });

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.SalesInvoice", b =>
                {
                    b.Navigation("SalesInvoiceDetails");

                    b.Navigation("SalesReturns");
                });

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.Supplier", b =>
                {
                    b.Navigation("Products");

                    b.Navigation("PurchaseInvoices");
                });

            modelBuilder.Entity("ElectronicsStore.Domain.Entities.User", b =>
                {
                    b.Navigation("Expenses");

                    b.Navigation("InventoryLogs");

                    b.Navigation("OverriddenSalesInvoices");

                    b.Navigation("PurchaseInvoices");

                    b.Navigation("PurchaseReturns");

                    b.Navigation("SalesInvoices");

                    b.Navigation("SalesReturns");
                });
#pragma warning restore 612, 618
        }
    }
}
