using Microsoft.EntityFrameworkCore;
using ElectronicsStore.Application.Interfaces;
using ElectronicsStore.Domain.Entities;

namespace ElectronicsStore.Persistence.Repositories
{
    public class InventoryRepository : GenericRepository<InventoryLog>, IInventoryRepository
    {
        public InventoryRepository(ElectronicsDbContext context) : base(context)
        {
        }

        public async Task<IEnumerable<InventoryLog>> GetByProductAsync(int productId)
        {
            return await _dbSet
                .Include(il => il.Product)
                .Include(il => il.User)
                .Where(il => il.ProductId == productId)
                .OrderByDescending(il => il.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<InventoryLog>> GetByMovementTypeAsync(string movementType)
        {
            return await _dbSet
                .Include(il => il.Product)
                .Include(il => il.User)
                .Where(il => il.MovementType == movementType)
                .OrderByDescending(il => il.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<InventoryLog>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate)
        {
            return await _dbSet
                .Include(il => il.Product)
                .Include(il => il.User)
                .Where(il => il.CreatedAt >= fromDate && il.CreatedAt <= toDate)
                .OrderByDescending(il => il.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<InventoryLog>> GetByUserAsync(int userId)
        {
            return await _dbSet
                .Include(il => il.Product)
                .Include(il => il.User)
                .Where(il => il.UserId == userId)
                .OrderByDescending(il => il.CreatedAt)
                .ToListAsync();
        }

        public async Task<int> GetCurrentStockAsync(int productId)
        {
            return await _dbSet
                .Where(il => il.ProductId == productId)
                .SumAsync(il => il.Quantity);
        }

        public async Task<Dictionary<int, int>> GetCurrentStockForProductsAsync(IEnumerable<int> productIds)
        {
            return await _dbSet
                .Where(il => productIds.Contains(il.ProductId))
                .GroupBy(il => il.ProductId)
                .ToDictionaryAsync(g => g.Key, g => g.Sum(il => il.Quantity));
        }

        public async Task<IEnumerable<object>> GetStockSummaryAsync()
        {
            return await _dbSet
                .Include(il => il.Product)
                .GroupBy(il => new { il.ProductId, il.Product.Name })
                .Select(g => new
                {
                    ProductId = g.Key.ProductId,
                    ProductName = g.Key.Name,
                    CurrentStock = g.Sum(il => il.Quantity),
                    TotalIn = g.Where(il => il.Quantity > 0).Sum(il => il.Quantity),
                    TotalOut = g.Where(il => il.Quantity < 0).Sum(il => Math.Abs(il.Quantity)),
                    LastMovement = g.Max(il => il.CreatedAt)
                })
                .OrderBy(x => x.ProductName)
                .ToListAsync();
        }

        public async Task<IEnumerable<InventoryLog>> GetRecentMovementsAsync(int count = 50)
        {
            return await _dbSet
                .Include(il => il.Product)
                .Include(il => il.User)
                .OrderByDescending(il => il.CreatedAt)
                .Take(count)
                .ToListAsync();
        }

        public async Task<IEnumerable<object>> GetMovementsByTypeAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _dbSet.AsQueryable();
            
            if (fromDate.HasValue)
                query = query.Where(il => il.CreatedAt >= fromDate.Value);
            
            if (toDate.HasValue)
                query = query.Where(il => il.CreatedAt <= toDate.Value);

            return await query
                .GroupBy(il => il.MovementType)
                .Select(g => new
                {
                    MovementType = g.Key,
                    Count = g.Count(),
                    TotalQuantity = g.Sum(il => Math.Abs(il.Quantity)),
                    TotalValue = g.Sum(il => Math.Abs(il.Quantity) * il.UnitCost)
                })
                .ToListAsync();
        }

        public async Task<IEnumerable<object>> GetDailyMovementsAsync(DateTime fromDate, DateTime toDate)
        {
            return await _dbSet
                .Where(il => il.CreatedAt >= fromDate && il.CreatedAt <= toDate)
                .GroupBy(il => il.CreatedAt.Date)
                .Select(g => new
                {
                    Date = g.Key,
                    TotalMovements = g.Count(),
                    TotalIn = g.Where(il => il.Quantity > 0).Sum(il => il.Quantity),
                    TotalOut = g.Where(il => il.Quantity < 0).Sum(il => Math.Abs(il.Quantity)),
                    TotalValue = g.Sum(il => Math.Abs(il.Quantity) * il.UnitCost)
                })
                .OrderBy(x => x.Date)
                .ToListAsync();
        }

        public async Task<decimal> GetInventoryValueAsync(int? productId = null)
        {
            var query = _dbSet.AsQueryable();
            
            if (productId.HasValue)
                query = query.Where(il => il.ProductId == productId.Value);

            // Calculate current stock value using FIFO method (simplified)
            return await query
                .Where(il => il.Quantity > 0) // Only positive movements (purchases)
                .SumAsync(il => il.Quantity * il.UnitCost);
        }

        public async Task<IEnumerable<object>> GetInventoryValuationReportAsync()
        {
            return await _dbSet
                .Include(il => il.Product)
                .Where(il => il.Quantity > 0) // Only positive movements
                .GroupBy(il => new { il.ProductId, il.Product.Name })
                .Select(g => new
                {
                    ProductId = g.Key.ProductId,
                    ProductName = g.Key.Name,
                    CurrentStock = g.Sum(il => il.Quantity),
                    AverageCost = g.Average(il => il.UnitCost),
                    TotalValue = g.Sum(il => il.Quantity * il.UnitCost)
                })
                .Where(x => x.CurrentStock > 0)
                .OrderByDescending(x => x.TotalValue)
                .ToListAsync();
        }

        public async Task<IEnumerable<object>> GetLowStockAlertsAsync(int threshold = 10)
        {
            return await _dbSet
                .Include(il => il.Product)
                .ThenInclude(p => p.Category)
                .GroupBy(il => new { il.ProductId, ProductName = il.Product.Name, CategoryName = il.Product.Category.Name })
                .Select(g => new
                {
                    ProductId = g.Key.ProductId,
                    ProductName = g.Key.ProductName,
                    CategoryName = g.Key.CategoryName,
                    CurrentStock = g.Sum(il => il.Quantity),
                    LastMovement = g.Max(il => il.CreatedAt)
                })
                .Where(x => x.CurrentStock <= threshold && x.CurrentStock > 0)
                .OrderBy(x => x.CurrentStock)
                .ToListAsync();
        }

        public async Task<IEnumerable<object>> GetOutOfStockProductsAsync()
        {
            return await _dbSet
                .Include(il => il.Product)
                .ThenInclude(p => p.Category)
                .GroupBy(il => new { il.ProductId, ProductName = il.Product.Name, CategoryName = il.Product.Category.Name })
                .Select(g => new
                {
                    ProductId = g.Key.ProductId,
                    ProductName = g.Key.ProductName,
                    CategoryName = g.Key.CategoryName,
                    CurrentStock = g.Sum(il => il.Quantity),
                    LastMovement = g.Max(il => il.CreatedAt)
                })
                .Where(x => x.CurrentStock <= 0)
                .OrderBy(x => x.ProductName)
                .ToListAsync();
        }

        public async Task<IEnumerable<InventoryLog>> GetMovementsWithDetailsAsync(
            int? productId = null, string? movementType = null, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _dbSet
                .Include(il => il.Product)
                .ThenInclude(p => p.Category)
                .Include(il => il.User)
                .AsQueryable();

            if (productId.HasValue)
                query = query.Where(il => il.ProductId == productId.Value);

            if (!string.IsNullOrEmpty(movementType))
                query = query.Where(il => il.MovementType == movementType);

            if (fromDate.HasValue)
                query = query.Where(il => il.CreatedAt >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(il => il.CreatedAt <= toDate.Value);

            return await query
                .OrderByDescending(il => il.CreatedAt)
                .ToListAsync();
        }

        public async Task<(IEnumerable<InventoryLog> Movements, int TotalCount)> GetPagedMovementsAsync(
            int pageNumber, int pageSize, int? productId = null, string? movementType = null, 
            DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _dbSet
                .Include(il => il.Product)
                .Include(il => il.User)
                .AsQueryable();

            if (productId.HasValue)
                query = query.Where(il => il.ProductId == productId.Value);

            if (!string.IsNullOrEmpty(movementType))
                query = query.Where(il => il.MovementType == movementType);

            if (fromDate.HasValue)
                query = query.Where(il => il.CreatedAt >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(il => il.CreatedAt <= toDate.Value);

            var totalCount = await query.CountAsync();
            var movements = await query
                .OrderByDescending(il => il.CreatedAt)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return (movements, totalCount);
        }
    }
}
