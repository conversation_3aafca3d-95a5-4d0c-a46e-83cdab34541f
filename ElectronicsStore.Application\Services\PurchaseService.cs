using ElectronicsStore.Application.DTOs;
using ElectronicsStore.Application.Interfaces;
using ElectronicsStore.Domain.Entities;

namespace ElectronicsStore.Application.Services
{
    public class PurchaseService : IPurchaseService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IInventoryService _inventoryService;

        public PurchaseService(IUnitOfWork unitOfWork, IInventoryService inventoryService)
        {
            _unitOfWork = unitOfWork;
            _inventoryService = inventoryService;
        }

        public async Task<PurchaseInvoiceDto?> GetPurchaseInvoiceByIdAsync(int id)
        {
            var invoice = await _unitOfWork.PurchaseInvoices.GetByIdAsync(id);
            if (invoice == null)
                return null;

            var details = await _unitOfWork.PurchaseInvoiceDetails.FindAsync(d => d.PurchaseInvoiceId == id);
            var supplier = await _unitOfWork.Suppliers.GetByIdAsync(invoice.SupplierId);
            var user = await _unitOfWork.Users.GetByIdAsync(invoice.UserId);

            return await MapToPurchaseInvoiceDto(invoice, details, supplier, user);
        }

        public async Task<IEnumerable<PurchaseInvoiceDto>> GetAllPurchaseInvoicesAsync()
        {
            var invoices = await _unitOfWork.PurchaseInvoices.GetAllAsync();
            var invoiceDtos = new List<PurchaseInvoiceDto>();

            foreach (var invoice in invoices)
            {
                var details = await _unitOfWork.PurchaseInvoiceDetails.FindAsync(d => d.PurchaseInvoiceId == invoice.Id);
                var supplier = await _unitOfWork.Suppliers.GetByIdAsync(invoice.SupplierId);
                var user = await _unitOfWork.Users.GetByIdAsync(invoice.UserId);

                invoiceDtos.Add(await MapToPurchaseInvoiceDto(invoice, details, supplier, user));
            }

            return invoiceDtos.OrderByDescending(i => i.InvoiceDate);
        }

        public async Task<PurchaseInvoiceDto> CreatePurchaseInvoiceAsync(CreatePurchaseInvoiceDto createDto, int userId)
        {
            // Validate supplier exists
            var supplier = await _unitOfWork.Suppliers.GetByIdAsync(createDto.SupplierId);
            if (supplier == null)
                throw new InvalidOperationException($"Supplier with ID {createDto.SupplierId} not found.");

            // Check if invoice number already exists
            var existingInvoice = await _unitOfWork.PurchaseInvoices.FirstOrDefaultAsync(i => i.InvoiceNumber == createDto.InvoiceNumber);
            if (existingInvoice != null)
                throw new InvalidOperationException($"Invoice number '{createDto.InvoiceNumber}' already exists.");

            // Validate products and calculate total
            decimal totalAmount = 0;
            var validatedDetails = new List<CreatePurchaseInvoiceDetailDto>();

            foreach (var detail in createDto.Details)
            {
                var product = await _unitOfWork.Products.GetByIdAsync(detail.ProductId);
                if (product == null)
                    throw new InvalidOperationException($"Product with ID {detail.ProductId} not found.");

                if (detail.Quantity <= 0)
                    throw new InvalidOperationException("Quantity must be greater than zero.");

                if (detail.UnitCost < 0)
                    throw new InvalidOperationException("Unit cost cannot be negative.");

                totalAmount += detail.Quantity * detail.UnitCost;
                validatedDetails.Add(detail);
            }

            // Create invoice
            var invoice = new PurchaseInvoice
            {
                InvoiceNumber = createDto.InvoiceNumber,
                SupplierId = createDto.SupplierId,
                InvoiceDate = createDto.InvoiceDate,
                UserId = userId,
                TotalAmount = totalAmount
            };

            await _unitOfWork.PurchaseInvoices.AddAsync(invoice);
            await _unitOfWork.SaveChangesAsync();

            // Create invoice details and update inventory
            foreach (var detailDto in validatedDetails)
            {
                var detail = new PurchaseInvoiceDetail
                {
                    PurchaseInvoiceId = invoice.Id,
                    ProductId = detailDto.ProductId,
                    Quantity = detailDto.Quantity,
                    UnitCost = detailDto.UnitCost
                };

                await _unitOfWork.PurchaseInvoiceDetails.AddAsync(detail);

                // Update inventory
                await _inventoryService.CreateInventoryLogAsync(new CreateInventoryLogDto
                {
                    ProductId = detailDto.ProductId,
                    MovementType = "purchase",
                    Quantity = detailDto.Quantity,
                    UnitCost = detailDto.UnitCost,
                    ReferenceTable = "purchase_invoices",
                    ReferenceId = invoice.Id,
                    Note = $"Purchase from invoice {invoice.InvoiceNumber}",
                    UserId = userId
                });
            }

            await _unitOfWork.SaveChangesAsync();

            // Return created invoice
            var details = await _unitOfWork.PurchaseInvoiceDetails.FindAsync(d => d.PurchaseInvoiceId == invoice.Id);
            var user = await _unitOfWork.Users.GetByIdAsync(userId);

            return await MapToPurchaseInvoiceDto(invoice, details, supplier, user);
        }

        public async Task<PurchaseInvoiceDto> UpdatePurchaseInvoiceAsync(UpdatePurchaseInvoiceDto updateDto)
        {
            var invoice = await _unitOfWork.PurchaseInvoices.GetByIdAsync(updateDto.Id);
            if (invoice == null)
                throw new InvalidOperationException($"Purchase invoice with ID {updateDto.Id} not found.");

            // Validate supplier exists
            var supplier = await _unitOfWork.Suppliers.GetByIdAsync(updateDto.SupplierId);
            if (supplier == null)
                throw new InvalidOperationException($"Supplier with ID {updateDto.SupplierId} not found.");

            // Check if new invoice number conflicts with existing invoice
            var existingInvoice = await _unitOfWork.PurchaseInvoices.FirstOrDefaultAsync(
                i => i.InvoiceNumber == updateDto.InvoiceNumber && i.Id != updateDto.Id);
            if (existingInvoice != null)
                throw new InvalidOperationException($"Invoice number '{updateDto.InvoiceNumber}' already exists.");

            // Get existing details to reverse inventory movements
            var existingDetails = await _unitOfWork.PurchaseInvoiceDetails.FindAsync(d => d.PurchaseInvoiceId == updateDto.Id);

            // Reverse existing inventory movements
            foreach (var existingDetail in existingDetails)
            {
                await _inventoryService.CreateInventoryLogAsync(new CreateInventoryLogDto
                {
                    ProductId = existingDetail.ProductId,
                    MovementType = "adjust",
                    Quantity = -existingDetail.Quantity, // Negative to reverse
                    UnitCost = existingDetail.UnitCost,
                    ReferenceTable = "purchase_invoices",
                    ReferenceId = invoice.Id,
                    Note = $"Reversed purchase from invoice {invoice.InvoiceNumber} (update)",
                    UserId = invoice.UserId
                });
            }

            // Remove existing details
            foreach (var detail in existingDetails)
            {
                _unitOfWork.PurchaseInvoiceDetails.Remove(detail);
            }

            // Validate new products and calculate total
            decimal totalAmount = 0;
            var validatedDetails = new List<UpdatePurchaseInvoiceDetailDto>();

            foreach (var detail in updateDto.Details)
            {
                var product = await _unitOfWork.Products.GetByIdAsync(detail.ProductId);
                if (product == null)
                    throw new InvalidOperationException($"Product with ID {detail.ProductId} not found.");

                if (detail.Quantity <= 0)
                    throw new InvalidOperationException("Quantity must be greater than zero.");

                if (detail.UnitCost < 0)
                    throw new InvalidOperationException("Unit cost cannot be negative.");

                totalAmount += detail.Quantity * detail.UnitCost;
                validatedDetails.Add(detail);
            }

            // Update invoice
            invoice.InvoiceNumber = updateDto.InvoiceNumber;
            invoice.SupplierId = updateDto.SupplierId;
            invoice.InvoiceDate = updateDto.InvoiceDate;
            invoice.TotalAmount = totalAmount;

            _unitOfWork.PurchaseInvoices.Update(invoice);

            // Create new details and update inventory
            foreach (var detailDto in validatedDetails)
            {
                var detail = new PurchaseInvoiceDetail
                {
                    PurchaseInvoiceId = invoice.Id,
                    ProductId = detailDto.ProductId,
                    Quantity = detailDto.Quantity,
                    UnitCost = detailDto.UnitCost
                };

                await _unitOfWork.PurchaseInvoiceDetails.AddAsync(detail);

                // Update inventory
                await _inventoryService.CreateInventoryLogAsync(new CreateInventoryLogDto
                {
                    ProductId = detailDto.ProductId,
                    MovementType = "purchase",
                    Quantity = detailDto.Quantity,
                    UnitCost = detailDto.UnitCost,
                    ReferenceTable = "purchase_invoices",
                    ReferenceId = invoice.Id,
                    Note = $"Updated purchase from invoice {invoice.InvoiceNumber}",
                    UserId = invoice.UserId
                });
            }

            await _unitOfWork.SaveChangesAsync();

            // Return updated invoice
            var newDetails = await _unitOfWork.PurchaseInvoiceDetails.FindAsync(d => d.PurchaseInvoiceId == invoice.Id);
            var user = await _unitOfWork.Users.GetByIdAsync(invoice.UserId);

            return await MapToPurchaseInvoiceDto(invoice, newDetails, supplier, user);
        }

        public async Task<bool> DeletePurchaseInvoiceAsync(int id)
        {
            var invoice = await _unitOfWork.PurchaseInvoices.GetByIdAsync(id);
            if (invoice == null)
                return false;

            // Get invoice details to reverse inventory movements
            var details = await _unitOfWork.PurchaseInvoiceDetails.FindAsync(d => d.PurchaseInvoiceId == id);

            // Reverse inventory movements
            foreach (var detail in details)
            {
                await _inventoryService.CreateInventoryLogAsync(new CreateInventoryLogDto
                {
                    ProductId = detail.ProductId,
                    MovementType = "adjust",
                    Quantity = -detail.Quantity, // Negative to reverse
                    UnitCost = detail.UnitCost,
                    ReferenceTable = "purchase_invoices",
                    ReferenceId = invoice.Id,
                    Note = $"Reversed purchase from deleted invoice {invoice.InvoiceNumber}",
                    UserId = invoice.UserId
                });
            }

            // Remove details first (foreign key constraint)
            foreach (var detail in details)
            {
                _unitOfWork.PurchaseInvoiceDetails.Remove(detail);
            }

            // Remove invoice
            _unitOfWork.PurchaseInvoices.Remove(invoice);
            await _unitOfWork.SaveChangesAsync();

            return true;
        }

        public async Task<IEnumerable<PurchaseInvoiceDto>> SearchPurchaseInvoicesAsync(PurchaseSearchDto searchDto)
        {
            var invoices = await _unitOfWork.PurchaseInvoices.GetAllAsync();

            // Apply filters
            if (!string.IsNullOrEmpty(searchDto.InvoiceNumber))
            {
                invoices = invoices.Where(i => i.InvoiceNumber.Contains(searchDto.InvoiceNumber, StringComparison.OrdinalIgnoreCase));
            }

            if (searchDto.SupplierId.HasValue)
            {
                invoices = invoices.Where(i => i.SupplierId == searchDto.SupplierId.Value);
            }

            if (searchDto.FromDate.HasValue)
            {
                invoices = invoices.Where(i => i.InvoiceDate.Date >= searchDto.FromDate.Value.Date);
            }

            if (searchDto.ToDate.HasValue)
            {
                invoices = invoices.Where(i => i.InvoiceDate.Date <= searchDto.ToDate.Value.Date);
            }

            if (searchDto.MinAmount.HasValue)
            {
                invoices = invoices.Where(i => i.TotalAmount >= searchDto.MinAmount.Value);
            }

            if (searchDto.MaxAmount.HasValue)
            {
                invoices = invoices.Where(i => i.TotalAmount <= searchDto.MaxAmount.Value);
            }

            if (searchDto.UserId.HasValue)
            {
                invoices = invoices.Where(i => i.UserId == searchDto.UserId.Value);
            }

            var invoiceDtos = new List<PurchaseInvoiceDto>();
            foreach (var invoice in invoices.OrderByDescending(i => i.InvoiceDate))
            {
                var details = await _unitOfWork.PurchaseInvoiceDetails.FindAsync(d => d.PurchaseInvoiceId == invoice.Id);
                var supplier = await _unitOfWork.Suppliers.GetByIdAsync(invoice.SupplierId);
                var user = await _unitOfWork.Users.GetByIdAsync(invoice.UserId);

                invoiceDtos.Add(await MapToPurchaseInvoiceDto(invoice, details, supplier, user));
            }

            return invoiceDtos;
        }

        public async Task<PurchaseInvoiceDto?> GetPurchaseInvoiceByNumberAsync(string invoiceNumber)
        {
            var invoice = await _unitOfWork.PurchaseInvoices.FirstOrDefaultAsync(i => i.InvoiceNumber == invoiceNumber);
            if (invoice == null)
                return null;

            var details = await _unitOfWork.PurchaseInvoiceDetails.FindAsync(d => d.PurchaseInvoiceId == invoice.Id);
            var supplier = await _unitOfWork.Suppliers.GetByIdAsync(invoice.SupplierId);
            var user = await _unitOfWork.Users.GetByIdAsync(invoice.UserId);

            return await MapToPurchaseInvoiceDto(invoice, details, supplier, user);
        }

        public async Task<IEnumerable<PurchaseInvoiceDto>> GetPurchaseInvoicesBySupplierAsync(int supplierId)
        {
            var invoices = await _unitOfWork.PurchaseInvoices.FindAsync(i => i.SupplierId == supplierId);
            var invoiceDtos = new List<PurchaseInvoiceDto>();

            foreach (var invoice in invoices.OrderByDescending(i => i.InvoiceDate))
            {
                var details = await _unitOfWork.PurchaseInvoiceDetails.FindAsync(d => d.PurchaseInvoiceId == invoice.Id);
                var supplier = await _unitOfWork.Suppliers.GetByIdAsync(invoice.SupplierId);
                var user = await _unitOfWork.Users.GetByIdAsync(invoice.UserId);

                invoiceDtos.Add(await MapToPurchaseInvoiceDto(invoice, details, supplier, user));
            }

            return invoiceDtos;
        }

        public async Task<IEnumerable<PurchaseInvoiceDto>> GetPurchaseInvoicesByUserAsync(int userId)
        {
            var invoices = await _unitOfWork.PurchaseInvoices.FindAsync(i => i.UserId == userId);
            var invoiceDtos = new List<PurchaseInvoiceDto>();

            foreach (var invoice in invoices.OrderByDescending(i => i.InvoiceDate))
            {
                var details = await _unitOfWork.PurchaseInvoiceDetails.FindAsync(d => d.PurchaseInvoiceId == invoice.Id);
                var supplier = await _unitOfWork.Suppliers.GetByIdAsync(invoice.SupplierId);
                var user = await _unitOfWork.Users.GetByIdAsync(invoice.UserId);

                invoiceDtos.Add(await MapToPurchaseInvoiceDto(invoice, details, supplier, user));
            }

            return invoiceDtos;
        }

        public async Task<IEnumerable<PurchaseInvoiceDto>> GetPurchaseInvoicesByDateRangeAsync(DateTime fromDate, DateTime toDate)
        {
            var invoices = await _unitOfWork.PurchaseInvoices.FindAsync(i =>
                i.InvoiceDate.Date >= fromDate.Date && i.InvoiceDate.Date <= toDate.Date);

            var invoiceDtos = new List<PurchaseInvoiceDto>();

            foreach (var invoice in invoices.OrderByDescending(i => i.InvoiceDate))
            {
                var details = await _unitOfWork.PurchaseInvoiceDetails.FindAsync(d => d.PurchaseInvoiceId == invoice.Id);
                var supplier = await _unitOfWork.Suppliers.GetByIdAsync(invoice.SupplierId);
                var user = await _unitOfWork.Users.GetByIdAsync(invoice.UserId);

                invoiceDtos.Add(await MapToPurchaseInvoiceDto(invoice, details, supplier, user));
            }

            return invoiceDtos;
        }

        public async Task<IEnumerable<PurchaseReportDto>> GetDailyPurchaseReportAsync(DateTime date)
        {
            var invoices = await _unitOfWork.PurchaseInvoices.FindAsync(i => i.InvoiceDate.Date == date.Date);

            var report = new List<PurchaseReportDto>();
            if (invoices.Any())
            {
                var totalItems = 0;
                foreach (var invoice in invoices)
                {
                    var details = await _unitOfWork.PurchaseInvoiceDetails.FindAsync(d => d.PurchaseInvoiceId == invoice.Id);
                    totalItems += details.Sum(d => d.Quantity);
                }

                report.Add(new PurchaseReportDto
                {
                    Date = date.Date,
                    InvoiceCount = invoices.Count(),
                    TotalAmount = invoices.Sum(i => i.TotalAmount),
                    TotalItems = totalItems,
                    AverageInvoiceAmount = invoices.Average(i => i.TotalAmount)
                });
            }

            return report;
        }

        public async Task<IEnumerable<PurchaseReportDto>> GetMonthlyPurchaseReportAsync(int year, int month)
        {
            var startDate = new DateTime(year, month, 1);
            var endDate = startDate.AddMonths(1).AddDays(-1);

            var invoices = await _unitOfWork.PurchaseInvoices.FindAsync(i =>
                i.InvoiceDate.Date >= startDate && i.InvoiceDate.Date <= endDate);

            var dailyReports = invoices
                .GroupBy(i => i.InvoiceDate.Date)
                .Select(g => new PurchaseReportDto
                {
                    Date = g.Key,
                    InvoiceCount = g.Count(),
                    TotalAmount = g.Sum(i => i.TotalAmount),
                    AverageInvoiceAmount = g.Average(i => i.TotalAmount)
                })
                .OrderBy(r => r.Date);

            // Calculate total items for each day
            var reports = new List<PurchaseReportDto>();
            foreach (var report in dailyReports)
            {
                var dayInvoices = invoices.Where(i => i.InvoiceDate.Date == report.Date);
                var totalItems = 0;

                foreach (var invoice in dayInvoices)
                {
                    var details = await _unitOfWork.PurchaseInvoiceDetails.FindAsync(d => d.PurchaseInvoiceId == invoice.Id);
                    totalItems += details.Sum(d => d.Quantity);
                }

                report.TotalItems = totalItems;
                reports.Add(report);
            }

            return reports;
        }

        public async Task<IEnumerable<PurchaseReportDto>> GetYearlyPurchaseReportAsync(int year)
        {
            var startDate = new DateTime(year, 1, 1);
            var endDate = new DateTime(year, 12, 31);

            var invoices = await _unitOfWork.PurchaseInvoices.FindAsync(i =>
                i.InvoiceDate.Date >= startDate && i.InvoiceDate.Date <= endDate);

            var monthlyReports = invoices
                .GroupBy(i => new { i.InvoiceDate.Year, i.InvoiceDate.Month })
                .Select(g => new PurchaseReportDto
                {
                    Date = new DateTime(g.Key.Year, g.Key.Month, 1),
                    InvoiceCount = g.Count(),
                    TotalAmount = g.Sum(i => i.TotalAmount),
                    AverageInvoiceAmount = g.Average(i => i.TotalAmount)
                })
                .OrderBy(r => r.Date);

            var reports = new List<PurchaseReportDto>();
            foreach (var report in monthlyReports)
            {
                var monthInvoices = invoices.Where(i => i.InvoiceDate.Year == report.Date.Year && i.InvoiceDate.Month == report.Date.Month);
                var totalItems = 0;

                foreach (var invoice in monthInvoices)
                {
                    var details = await _unitOfWork.PurchaseInvoiceDetails.FindAsync(d => d.PurchaseInvoiceId == invoice.Id);
                    totalItems += details.Sum(d => d.Quantity);
                }

                report.TotalItems = totalItems;
                reports.Add(report);
            }

            return reports;
        }

        public async Task<PurchaseStatisticsDto> GetPurchaseStatisticsAsync()
        {
            var allInvoices = await _unitOfWork.PurchaseInvoices.GetAllAsync();
            var today = DateTime.Today;
            var startOfMonth = new DateTime(today.Year, today.Month, 1);
            var startOfYear = new DateTime(today.Year, 1, 1);

            var todayInvoices = allInvoices.Where(i => i.InvoiceDate.Date == today);
            var monthInvoices = allInvoices.Where(i => i.InvoiceDate.Date >= startOfMonth);
            var yearInvoices = allInvoices.Where(i => i.InvoiceDate.Date >= startOfYear);

            var totalItems = 0;
            foreach (var invoice in allInvoices)
            {
                var details = await _unitOfWork.PurchaseInvoiceDetails.FindAsync(d => d.PurchaseInvoiceId == invoice.Id);
                totalItems += details.Sum(d => d.Quantity);
            }

            var uniqueSuppliers = allInvoices.Select(i => i.SupplierId).Distinct().Count();

            return new PurchaseStatisticsDto
            {
                TotalInvoices = allInvoices.Count(),
                TotalAmount = allInvoices.Sum(i => i.TotalAmount),
                TotalItems = totalItems,
                AverageInvoiceAmount = allInvoices.Any() ? allInvoices.Average(i => i.TotalAmount) : 0,
                TotalSuppliers = uniqueSuppliers,
                TodayAmount = todayInvoices.Sum(i => i.TotalAmount),
                MonthAmount = monthInvoices.Sum(i => i.TotalAmount),
                YearAmount = yearInvoices.Sum(i => i.TotalAmount)
            };
        }

        public async Task<IEnumerable<object>> GetTopSuppliersAsync(int count = 10)
        {
            var invoices = await _unitOfWork.PurchaseInvoices.GetAllAsync();
            var supplierStats = invoices
                .GroupBy(i => i.SupplierId)
                .Select(g => new
                {
                    SupplierId = g.Key,
                    InvoiceCount = g.Count(),
                    TotalAmount = g.Sum(i => i.TotalAmount),
                    AverageAmount = g.Average(i => i.TotalAmount),
                    LastPurchaseDate = g.Max(i => i.InvoiceDate)
                })
                .OrderByDescending(s => s.TotalAmount)
                .Take(count);

            var result = new List<object>();
            foreach (var stat in supplierStats)
            {
                var supplier = await _unitOfWork.Suppliers.GetByIdAsync(stat.SupplierId);
                result.Add(new
                {
                    SupplierId = stat.SupplierId,
                    SupplierName = supplier?.Name ?? "Unknown",
                    InvoiceCount = stat.InvoiceCount,
                    TotalAmount = stat.TotalAmount,
                    AverageAmount = stat.AverageAmount,
                    LastPurchaseDate = stat.LastPurchaseDate
                });
            }

            return result;
        }

        public async Task<IEnumerable<object>> GetTopProductsAsync(int count = 10)
        {
            var allDetails = await _unitOfWork.PurchaseInvoiceDetails.GetAllAsync();
            var productStats = allDetails
                .GroupBy(d => d.ProductId)
                .Select(g => new
                {
                    ProductId = g.Key,
                    TotalQuantity = g.Sum(d => d.Quantity),
                    TotalAmount = g.Sum(d => d.Quantity * d.UnitCost),
                    AverageUnitCost = g.Average(d => d.UnitCost),
                    PurchaseCount = g.Count()
                })
                .OrderByDescending(p => p.TotalAmount)
                .Take(count);

            var result = new List<object>();
            foreach (var stat in productStats)
            {
                var product = await _unitOfWork.Products.GetByIdAsync(stat.ProductId);
                result.Add(new
                {
                    ProductId = stat.ProductId,
                    ProductName = product?.Name ?? "Unknown",
                    ProductBarcode = product?.Barcode,
                    TotalQuantity = stat.TotalQuantity,
                    TotalAmount = stat.TotalAmount,
                    AverageUnitCost = stat.AverageUnitCost,
                    PurchaseCount = stat.PurchaseCount
                });
            }

            return result;
        }

        public async Task<IEnumerable<PurchaseInvoiceDto>> GetTodayPurchasesAsync()
        {
            var today = DateTime.Today;
            return await GetPurchaseInvoicesByDateRangeAsync(today, today);
        }

        public async Task<decimal> GetTodayPurchasesTotalAsync()
        {
            var today = DateTime.Today;
            var todayInvoices = await _unitOfWork.PurchaseInvoices.FindAsync(i => i.InvoiceDate.Date == today);
            return todayInvoices.Sum(i => i.TotalAmount);
        }

        public async Task<bool> PurchaseInvoiceExistsAsync(int id)
        {
            return await _unitOfWork.PurchaseInvoices.AnyAsync(i => i.Id == id);
        }

        public async Task<bool> InvoiceNumberExistsAsync(string invoiceNumber, int? excludeId = null)
        {
            if (excludeId.HasValue)
                return await _unitOfWork.PurchaseInvoices.AnyAsync(i => i.InvoiceNumber == invoiceNumber && i.Id != excludeId.Value);

            return await _unitOfWork.PurchaseInvoices.AnyAsync(i => i.InvoiceNumber == invoiceNumber);
        }

        public async Task<bool> CanDeletePurchaseInvoiceAsync(int id)
        {
            // For now, allow deletion of any purchase invoice
            // In the future, you might want to add business rules here
            return await PurchaseInvoiceExistsAsync(id);
        }

        public async Task<(IEnumerable<PurchaseInvoiceDto> Invoices, int TotalCount)> GetPagedPurchaseInvoicesAsync(
            int pageNumber, int pageSize, PurchaseSearchDto? searchDto = null)
        {
            var invoices = await _unitOfWork.PurchaseInvoices.GetAllAsync();

            // Apply search filters if provided
            if (searchDto != null)
            {
                if (!string.IsNullOrEmpty(searchDto.InvoiceNumber))
                {
                    invoices = invoices.Where(i => i.InvoiceNumber.Contains(searchDto.InvoiceNumber, StringComparison.OrdinalIgnoreCase));
                }

                if (searchDto.SupplierId.HasValue)
                {
                    invoices = invoices.Where(i => i.SupplierId == searchDto.SupplierId.Value);
                }

                if (searchDto.FromDate.HasValue)
                {
                    invoices = invoices.Where(i => i.InvoiceDate.Date >= searchDto.FromDate.Value.Date);
                }

                if (searchDto.ToDate.HasValue)
                {
                    invoices = invoices.Where(i => i.InvoiceDate.Date <= searchDto.ToDate.Value.Date);
                }

                if (searchDto.MinAmount.HasValue)
                {
                    invoices = invoices.Where(i => i.TotalAmount >= searchDto.MinAmount.Value);
                }

                if (searchDto.MaxAmount.HasValue)
                {
                    invoices = invoices.Where(i => i.TotalAmount <= searchDto.MaxAmount.Value);
                }

                if (searchDto.UserId.HasValue)
                {
                    invoices = invoices.Where(i => i.UserId == searchDto.UserId.Value);
                }
            }

            var totalCount = invoices.Count();
            var pagedInvoices = invoices
                .OrderByDescending(i => i.InvoiceDate)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize);

            var invoiceDtos = new List<PurchaseInvoiceDto>();
            foreach (var invoice in pagedInvoices)
            {
                var details = await _unitOfWork.PurchaseInvoiceDetails.FindAsync(d => d.PurchaseInvoiceId == invoice.Id);
                var supplier = await _unitOfWork.Suppliers.GetByIdAsync(invoice.SupplierId);
                var user = await _unitOfWork.Users.GetByIdAsync(invoice.UserId);

                invoiceDtos.Add(await MapToPurchaseInvoiceDto(invoice, details, supplier, user));
            }

            return (invoiceDtos, totalCount);
        }

        public async Task<IEnumerable<PurchaseInvoiceDetailDto>> GetPurchaseInvoiceDetailsAsync(int invoiceId)
        {
            var details = await _unitOfWork.PurchaseInvoiceDetails.FindAsync(d => d.PurchaseInvoiceId == invoiceId);
            var detailDtos = new List<PurchaseInvoiceDetailDto>();

            foreach (var detail in details)
            {
                var product = await _unitOfWork.Products.GetByIdAsync(detail.ProductId);
                detailDtos.Add(new PurchaseInvoiceDetailDto
                {
                    Id = detail.Id,
                    PurchaseInvoiceId = detail.PurchaseInvoiceId,
                    ProductId = detail.ProductId,
                    ProductName = product?.Name ?? "Unknown",
                    ProductBarcode = product?.Barcode,
                    Quantity = detail.Quantity,
                    UnitCost = detail.UnitCost,
                    LineTotal = detail.Quantity * detail.UnitCost
                });
            }

            return detailDtos;
        }

        public async Task<PurchaseInvoiceDetailDto?> GetPurchaseInvoiceDetailByIdAsync(int detailId)
        {
            var detail = await _unitOfWork.PurchaseInvoiceDetails.GetByIdAsync(detailId);
            if (detail == null)
                return null;

            var product = await _unitOfWork.Products.GetByIdAsync(detail.ProductId);
            return new PurchaseInvoiceDetailDto
            {
                Id = detail.Id,
                PurchaseInvoiceId = detail.PurchaseInvoiceId,
                ProductId = detail.ProductId,
                ProductName = product?.Name ?? "Unknown",
                ProductBarcode = product?.Barcode,
                Quantity = detail.Quantity,
                UnitCost = detail.UnitCost,
                LineTotal = detail.Quantity * detail.UnitCost
            };
        }

        private async Task<PurchaseInvoiceDto> MapToPurchaseInvoiceDto(
            PurchaseInvoice invoice,
            IEnumerable<PurchaseInvoiceDetail> details,
            Supplier? supplier,
            User? user)
        {
            var detailDtos = new List<PurchaseInvoiceDetailDto>();

            foreach (var detail in details)
            {
                var product = await _unitOfWork.Products.GetByIdAsync(detail.ProductId);
                detailDtos.Add(new PurchaseInvoiceDetailDto
                {
                    Id = detail.Id,
                    PurchaseInvoiceId = detail.PurchaseInvoiceId,
                    ProductId = detail.ProductId,
                    ProductName = product?.Name ?? "Unknown",
                    ProductBarcode = product?.Barcode,
                    Quantity = detail.Quantity,
                    UnitCost = detail.UnitCost,
                    LineTotal = detail.Quantity * detail.UnitCost
                });
            }

            return new PurchaseInvoiceDto
            {
                Id = invoice.Id,
                InvoiceNumber = invoice.InvoiceNumber,
                SupplierId = invoice.SupplierId,
                SupplierName = supplier?.Name ?? "Unknown",
                InvoiceDate = invoice.InvoiceDate,
                UserId = invoice.UserId,
                UserName = user?.Username ?? "Unknown",
                TotalAmount = invoice.TotalAmount,
                Details = detailDtos
            };
        }
    }
}
