using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ElectronicsStore.Application.Common;
using ElectronicsStore.API.Attributes;
using System.Security.Claims;

namespace ElectronicsStore.API.Controllers
{
    /// <summary>
    /// Base controller containing common patterns used across all controllers
    /// </summary>
    [ApiController]
    [Authorize]
    [ValidateInput]
    [SanitizeInput]
    [ValidateFormat]
    public abstract class BaseController : ControllerBase
    {
        #region Common Response Methods

        /// <summary>
        /// Create a standardized success response
        /// </summary>
        protected ActionResult<ApiResponse<T>> CreateSuccessResponse<T>(T data, string message = "Operation completed successfully.")
        {
            return Ok(ApiResponse<T>.SuccessResult(data, message));
        }

        /// <summary>
        /// Create a standardized error response
        /// </summary>
        protected ActionResult<ApiResponse<T>> CreateErrorResponse<T>(string message, List<string>? errors = null)
        {
            return BadRequest(ApiResponse<T>.ErrorResult(message, errors));
        }

        /// <summary>
        /// Create a standardized not found response
        /// </summary>
        protected ActionResult<ApiResponse<T>> CreateNotFoundResponse<T>(string message)
        {
            return NotFound(ApiResponse<T>.ErrorResult(message));
        }

        /// <summary>
        /// Create a standardized paged response
        /// </summary>
        protected ActionResult<PagedResponse<T>> CreatePagedResponse<T>(
            IEnumerable<T> data, int pageNumber, int pageSize, int totalCount)
        {
            return Ok(PagedResponse<T>.Create(data, pageNumber, pageSize, totalCount));
        }

        /// <summary>
        /// Create a standardized validation error response
        /// </summary>
        protected ActionResult<ValidationResponse> CreateValidationErrorResponse(Dictionary<string, List<string>> errors)
        {
            return BadRequest(ValidationResponse.Create(errors));
        }

        /// <summary>
        /// Create a standardized validation error response for single field
        /// </summary>
        protected ActionResult<ValidationResponse> CreateValidationErrorResponse(string field, string error)
        {
            return BadRequest(ValidationResponse.Create(field, error));
        }

        #endregion

        #region Common User Information Methods

        /// <summary>
        /// Get current user ID from JWT token
        /// </summary>
        protected int GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? User.FindFirst("UserId")?.Value;
            if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
                throw new UnauthorizedAccessException("Invalid user token.");
            
            return userId;
        }

        /// <summary>
        /// Get current user role from JWT token
        /// </summary>
        protected string GetCurrentUserRole()
        {
            var roleClaim = User.FindFirst(ClaimTypes.Role)?.Value;
            if (string.IsNullOrEmpty(roleClaim))
                throw new UnauthorizedAccessException("User role not found in token.");
            
            return roleClaim;
        }

        /// <summary>
        /// Get current username from JWT token
        /// </summary>
        protected string GetCurrentUsername()
        {
            var usernameClaim = User.FindFirst(ClaimTypes.Name)?.Value;
            if (string.IsNullOrEmpty(usernameClaim))
                throw new UnauthorizedAccessException("Username not found in token.");
            
            return usernameClaim;
        }

        /// <summary>
        /// Check if current user has specific role
        /// </summary>
        protected bool IsInRole(string role)
        {
            return User.IsInRole(role);
        }

        /// <summary>
        /// Check if current user has any of the specified roles
        /// </summary>
        protected bool IsInAnyRole(params string[] roles)
        {
            return roles.Any(role => User.IsInRole(role));
        }

        #endregion

        #region Common Validation Methods

        /// <summary>
        /// Validate that ID is positive
        /// </summary>
        protected bool IsValidId(int id)
        {
            return id > 0;
        }

        /// <summary>
        /// Validate pagination parameters
        /// </summary>
        protected bool IsValidPaginationParameters(int pageNumber, int pageSize)
        {
            return pageNumber > 0 && pageSize > 0 && pageSize <= 100;
        }

        /// <summary>
        /// Validate ID and return error response if invalid
        /// </summary>
        protected ActionResult? ValidateId(int id, string entityName = "Entity")
        {
            if (!IsValidId(id))
                return BadRequest(ApiResponse.ErrorResult($"Invalid {entityName} ID. ID must be greater than zero."));
            
            return null;
        }

        /// <summary>
        /// Validate pagination parameters and return error response if invalid
        /// </summary>
        protected ActionResult? ValidatePaginationParameters(int pageNumber, int pageSize)
        {
            if (!IsValidPaginationParameters(pageNumber, pageSize))
                return BadRequest(ApiResponse.ErrorResult("Invalid pagination parameters. Page number must be > 0 and page size must be between 1 and 100."));
            
            return null;
        }

        /// <summary>
        /// Validate ID mismatch between route and body
        /// </summary>
        protected ActionResult? ValidateIdMismatch(int routeId, int bodyId)
        {
            if (routeId != bodyId)
                return BadRequest(ApiResponse.ErrorResult("ID mismatch between route and request body."));
            
            return null;
        }

        #endregion

        #region Common Error Handling

        /// <summary>
        /// Execute operation with standardized error handling
        /// </summary>
        protected async Task<ActionResult<ApiResponse<T>>> ExecuteWithErrorHandling<T>(Func<Task<T>> operation)
        {
            try
            {
                var result = await operation();
                return CreateSuccessResponse(result);
            }
            catch (UnauthorizedAccessException ex)
            {
                return Unauthorized(ApiResponse<T>.ErrorResult("Unauthorized access.", ex.Message));
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ApiResponse<T>.ErrorResult("Invalid operation.", ex.Message));
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ApiResponse<T>.ErrorResult("Invalid argument.", ex.Message));
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ApiResponse<T>.ErrorResult("Resource not found.", ex.Message));
            }
            catch (Exception ex)
            {
                // Log the exception here if logging is available
                return StatusCode(500, ApiResponse<T>.ErrorResult("An internal server error occurred."));
            }
        }

        /// <summary>
        /// Execute operation with standardized error handling (void return)
        /// </summary>
        protected async Task<ActionResult<ApiResponse>> ExecuteWithErrorHandling(Func<Task> operation)
        {
            try
            {
                await operation();
                return Ok(ApiResponse.SuccessResult());
            }
            catch (UnauthorizedAccessException ex)
            {
                return Unauthorized(ApiResponse.ErrorResult("Unauthorized access.", ex.Message));
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ApiResponse.ErrorResult("Invalid operation.", ex.Message));
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ApiResponse.ErrorResult("Invalid argument.", ex.Message));
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ApiResponse.ErrorResult("Resource not found.", ex.Message));
            }
            catch (Exception ex)
            {
                // Log the exception here if logging is available
                return StatusCode(500, ApiResponse.ErrorResult("An internal server error occurred."));
            }
        }

        #endregion

        #region Common CRUD Patterns

        /// <summary>
        /// Standard pattern for GET by ID operations
        /// </summary>
        protected async Task<ActionResult<ApiResponse<T>>> GetByIdAsync<T>(int id, Func<int, Task<T?>> getOperation, string entityName = "Entity") where T : class
        {
            var validationResult = ValidateId(id, entityName);
            if (validationResult != null) return validationResult;

            return await ExecuteWithErrorHandling(async () =>
            {
                var entity = await getOperation(id);
                if (entity == null)
                    throw new KeyNotFoundException($"{entityName} with ID {id} not found.");
                
                return entity;
            });
        }

        /// <summary>
        /// Standard pattern for GET all operations
        /// </summary>
        protected async Task<ActionResult<ApiResponse<IEnumerable<T>>>> GetAllAsync<T>(Func<Task<IEnumerable<T>>> getAllOperation)
        {
            return await ExecuteWithErrorHandling(getAllOperation);
        }

        /// <summary>
        /// Standard pattern for CREATE operations
        /// </summary>
        protected async Task<ActionResult<ApiResponse<T>>> CreateAsync<T>(Func<Task<T>> createOperation, Func<T, object> getRouteValues, string actionName = "Get")
        {
            return await ExecuteWithErrorHandling(async () =>
            {
                var entity = await createOperation();
                // Note: CreatedAtAction would need to be handled differently in a real implementation
                return entity;
            });
        }

        /// <summary>
        /// Standard pattern for UPDATE operations
        /// </summary>
        protected async Task<ActionResult<ApiResponse<T>>> UpdateAsync<T>(int id, int bodyId, Func<Task<T>> updateOperation)
        {
            var idValidation = ValidateId(id);
            if (idValidation != null) return idValidation;

            var mismatchValidation = ValidateIdMismatch(id, bodyId);
            if (mismatchValidation != null) return mismatchValidation;

            return await ExecuteWithErrorHandling(updateOperation);
        }

        /// <summary>
        /// Standard pattern for DELETE operations
        /// </summary>
        protected async Task<ActionResult<ApiResponse>> DeleteAsync(int id, Func<int, Task<bool>> deleteOperation, string entityName = "Entity")
        {
            var validationResult = ValidateId(id, entityName);
            if (validationResult != null) return validationResult;

            return await ExecuteWithErrorHandling(async () =>
            {
                var result = await deleteOperation(id);
                if (!result)
                    throw new KeyNotFoundException($"{entityName} with ID {id} not found.");
            });
        }

        #endregion

        #region Exception Handling

        /// <summary>
        /// Handle exceptions and return appropriate error response
        /// </summary>
        protected ActionResult HandleException(Exception ex, string message = "An error occurred")
        {
            // Log the exception here if logging is available
            if (ex is ArgumentException || ex is InvalidOperationException)
            {
                return BadRequest(ApiResponse.ErrorResult(message, ex.Message));
            }

            if (ex is KeyNotFoundException)
            {
                return NotFound(ApiResponse.ErrorResult(message, ex.Message));
            }

            return StatusCode(500, ApiResponse.ErrorResult(message, "An internal server error occurred."));
        }

        #endregion
    }
}
