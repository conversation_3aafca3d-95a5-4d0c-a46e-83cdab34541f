using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using ElectronicsStore.Application.Interfaces;
using System.Text.Json;

namespace ElectronicsStore.Infrastructure.BackgroundServices
{
    public class ReportGenerationService : BackgroundService
    {
        private readonly ILogger<ReportGenerationService> _logger;
        private readonly IServiceProvider _serviceProvider;

        public ReportGenerationService(ILogger<ReportGenerationService> logger, IServiceProvider serviceProvider)
        {
            _logger = logger;
            _serviceProvider = serviceProvider;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    _logger.LogInformation("Report generation service started at: {Time}", DateTimeOffset.Now);

                    using (var scope = _serviceProvider.CreateScope())
                    {
                        var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();

                        // Generate daily sales report
                        await GenerateDailySalesReport(unitOfWork);

                        // Generate weekly inventory report
                        if (DateTime.Now.DayOfWeek == DayOfWeek.Monday)
                        {
                            await GenerateWeeklyInventoryReport(unitOfWork);
                        }

                        // Generate monthly financial report
                        if (DateTime.Now.Day == 1)
                        {
                            await GenerateMonthlyFinancialReport(unitOfWork);
                        }
                    }

                    _logger.LogInformation("Report generation service completed at: {Time}", DateTimeOffset.Now);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred during report generation");
                }

                // Run every day at 2 AM
                var now = DateTime.Now;
                var nextRun = now.Date.AddDays(1).AddHours(2);
                var delay = nextRun - now;
                
                if (delay.TotalMilliseconds > 0)
                {
                    await Task.Delay(delay, stoppingToken);
                }
                else
                {
                    await Task.Delay(TimeSpan.FromHours(1), stoppingToken); // Fallback delay
                }
            }
        }

        private async Task GenerateDailySalesReport(IUnitOfWork unitOfWork)
        {
            try
            {
                var yesterday = DateTime.Today.AddDays(-1);
                var salesInvoices = await unitOfWork.SalesInvoices
                    .FindAsync(si => si.InvoiceDate.Date == yesterday);

                var report = new
                {
                    Date = yesterday,
                    TotalSales = salesInvoices.Count(),
                    TotalRevenue = salesInvoices.Sum(si => si.TotalAmount),
                    TopProducts = salesInvoices
                        .SelectMany(si => si.Items)
                        .GroupBy(item => item.ProductId)
                        .Select(g => new
                        {
                            ProductId = g.Key,
                            TotalQuantity = g.Sum(item => item.Quantity),
                            TotalRevenue = g.Sum(item => item.Quantity * item.UnitPrice)
                        })
                        .OrderByDescending(p => p.TotalRevenue)
                        .Take(5)
                        .ToList()
                };

                // Save report to file or database
                var reportJson = JsonSerializer.Serialize(report, new JsonSerializerOptions { WriteIndented = true });
                var fileName = $"daily_sales_report_{yesterday:yyyy-MM-dd}.json";
                var filePath = Path.Combine("Reports", "Daily", fileName);
                
                Directory.CreateDirectory(Path.GetDirectoryName(filePath)!);
                await File.WriteAllTextAsync(filePath, reportJson);

                _logger.LogInformation("Daily sales report generated for {Date}: {TotalSales} sales, {TotalRevenue:C} revenue",
                    yesterday, report.TotalSales, report.TotalRevenue);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating daily sales report");
            }
        }

        private async Task GenerateWeeklyInventoryReport(IUnitOfWork unitOfWork)
        {
            try
            {
                var products = await unitOfWork.Products.GetAllAsync();
                var categories = await unitOfWork.Categories.GetAllAsync();

                var report = new
                {
                    WeekStarting = DateTime.Today.AddDays(-7),
                    TotalProducts = products.Count(),
                    LowStockProducts = products.Where(p => p.Quantity < 10).Select(p => new
                    {
                        p.Id,
                        p.Name,
                        p.Quantity,
                        CategoryName = categories.FirstOrDefault(c => c.Id == p.CategoryId)?.Name
                    }).ToList(),
                    OutOfStockProducts = products.Where(p => p.Quantity == 0).Select(p => new
                    {
                        p.Id,
                        p.Name,
                        CategoryName = categories.FirstOrDefault(c => c.Id == p.CategoryId)?.Name
                    }).ToList(),
                    InventoryValue = products.Sum(p => p.Quantity * p.Price)
                };

                var reportJson = JsonSerializer.Serialize(report, new JsonSerializerOptions { WriteIndented = true });
                var fileName = $"weekly_inventory_report_{DateTime.Today:yyyy-MM-dd}.json";
                var filePath = Path.Combine("Reports", "Weekly", fileName);
                
                Directory.CreateDirectory(Path.GetDirectoryName(filePath)!);
                await File.WriteAllTextAsync(filePath, reportJson);

                _logger.LogInformation("Weekly inventory report generated: {TotalProducts} products, {LowStock} low stock, {OutOfStock} out of stock",
                    report.TotalProducts, report.LowStockProducts.Count, report.OutOfStockProducts.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating weekly inventory report");
            }
        }

        private async Task GenerateMonthlyFinancialReport(IUnitOfWork unitOfWork)
        {
            try
            {
                var lastMonth = DateTime.Today.AddMonths(-1);
                var startOfMonth = new DateTime(lastMonth.Year, lastMonth.Month, 1);
                var endOfMonth = startOfMonth.AddMonths(1).AddDays(-1);

                var salesInvoices = await unitOfWork.SalesInvoices
                    .FindAsync(si => si.InvoiceDate >= startOfMonth && si.InvoiceDate <= endOfMonth);

                var purchaseInvoices = await unitOfWork.PurchaseInvoices
                    .FindAsync(pi => pi.InvoiceDate >= startOfMonth && pi.InvoiceDate <= endOfMonth);

                var report = new
                {
                    Month = lastMonth.ToString("yyyy-MM"),
                    Sales = new
                    {
                        TotalInvoices = salesInvoices.Count(),
                        TotalRevenue = salesInvoices.Sum(si => si.TotalAmount),
                        AverageInvoiceValue = salesInvoices.Any() ? salesInvoices.Average(si => si.TotalAmount) : 0
                    },
                    Purchases = new
                    {
                        TotalInvoices = purchaseInvoices.Count(),
                        TotalCost = purchaseInvoices.Sum(pi => pi.TotalAmount),
                        AverageInvoiceValue = purchaseInvoices.Any() ? purchaseInvoices.Average(pi => pi.TotalAmount) : 0
                    },
                    Profit = salesInvoices.Sum(si => si.TotalAmount) - purchaseInvoices.Sum(pi => pi.TotalAmount)
                };

                var reportJson = JsonSerializer.Serialize(report, new JsonSerializerOptions { WriteIndented = true });
                var fileName = $"monthly_financial_report_{lastMonth:yyyy-MM}.json";
                var filePath = Path.Combine("Reports", "Monthly", fileName);
                
                Directory.CreateDirectory(Path.GetDirectoryName(filePath)!);
                await File.WriteAllTextAsync(filePath, reportJson);

                _logger.LogInformation("Monthly financial report generated for {Month}: {Revenue:C} revenue, {Cost:C} costs, {Profit:C} profit",
                    report.Month, report.Sales.TotalRevenue, report.Purchases.TotalCost, report.Profit);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating monthly financial report");
            }
        }
    }
}
