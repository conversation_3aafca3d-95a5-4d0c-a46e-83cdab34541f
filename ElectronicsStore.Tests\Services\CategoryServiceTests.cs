using Moq;
using FluentAssertions;
using ElectronicsStore.Application.Services;
using ElectronicsStore.Application.Interfaces;
using ElectronicsStore.Application.DTOs;
using ElectronicsStore.Domain.Entities;

namespace ElectronicsStore.Tests.Services
{
    public class CategoryServiceTests
    {
        private readonly Mock<IUnitOfWork> _mockUnitOfWork;
        private readonly Mock<IGenericRepository<Category>> _mockCategoryRepository;
        private readonly Mock<IGenericRepository<Product>> _mockProductRepository;
        private readonly CategoryService _categoryService;

        public CategoryServiceTests()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockCategoryRepository = new Mock<IGenericRepository<Category>>();
            _mockProductRepository = new Mock<IGenericRepository<Product>>();
            
            _mockUnitOfWork.Setup(u => u.Categories).Returns(_mockCategoryRepository.Object);
            _mockUnitOfWork.Setup(u => u.Products).Returns(_mockProductRepository.Object);
            
            _categoryService = new CategoryService(_mockUnitOfWork.Object);
        }

        [Fact]
        public async Task GetAllCategoriesAsync_ShouldReturnAllCategories()
        {
            // Arrange
            var categories = new List<Category>
            {
                new Category { Id = 1, Name = "Electronics" },
                new Category { Id = 2, Name = "Clothing" }
            };
            _mockCategoryRepository.Setup(r => r.GetAllAsync()).ReturnsAsync(categories);

            // Act
            var result = await _categoryService.GetAllCategoriesAsync();

            // Assert
            result.Should().HaveCount(2);
            result.Should().Contain(c => c.Name == "Electronics");
            result.Should().Contain(c => c.Name == "Clothing");
        }

        [Fact]
        public async Task GetCategoryByIdAsync_WithValidId_ShouldReturnCategory()
        {
            // Arrange
            var category = new Category { Id = 1, Name = "Electronics" };
            _mockCategoryRepository.Setup(r => r.GetByIdAsync(1)).ReturnsAsync(category);

            // Act
            var result = await _categoryService.GetCategoryByIdAsync(1);

            // Assert
            result.Should().NotBeNull();
            result!.Id.Should().Be(1);
            result.Name.Should().Be("Electronics");
        }

        [Fact]
        public async Task GetCategoryByIdAsync_WithInvalidId_ShouldReturnNull()
        {
            // Arrange
            _mockCategoryRepository.Setup(r => r.GetByIdAsync(999)).ReturnsAsync((Category?)null);

            // Act
            var result = await _categoryService.GetCategoryByIdAsync(999);

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public async Task CreateCategoryAsync_WithValidData_ShouldCreateCategory()
        {
            // Arrange
            var createDto = new CreateCategoryDto { Name = "New Category" };
            var category = new Category { Id = 1, Name = "New Category" };
            
            _mockCategoryRepository.Setup(r => r.GetAllAsync()).ReturnsAsync(new List<Category>());
            _mockCategoryRepository.Setup(r => r.AddAsync(It.IsAny<Category>())).Returns(Task.CompletedTask);
            _mockUnitOfWork.Setup(u => u.SaveChangesAsync()).Returns(Task.CompletedTask);

            // Act
            var result = await _categoryService.CreateCategoryAsync(createDto);

            // Assert
            result.Should().NotBeNull();
            result.Name.Should().Be("New Category");
            _mockCategoryRepository.Verify(r => r.AddAsync(It.IsAny<Category>()), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveChangesAsync(), Times.Once);
        }

        [Fact]
        public async Task CreateCategoryAsync_WithDuplicateName_ShouldThrowException()
        {
            // Arrange
            var createDto = new CreateCategoryDto { Name = "Existing Category" };
            var existingCategories = new List<Category>
            {
                new Category { Id = 1, Name = "Existing Category" }
            };
            
            _mockCategoryRepository.Setup(r => r.GetAllAsync()).ReturnsAsync(existingCategories);

            // Act & Assert
            await Assert.ThrowsAsync<InvalidOperationException>(() => 
                _categoryService.CreateCategoryAsync(createDto));
        }

        [Fact]
        public async Task UpdateCategoryAsync_WithValidData_ShouldUpdateCategory()
        {
            // Arrange
            var updateDto = new UpdateCategoryDto { Id = 1, Name = "Updated Category" };
            var existingCategory = new Category { Id = 1, Name = "Old Category" };
            
            _mockCategoryRepository.Setup(r => r.GetByIdAsync(1)).ReturnsAsync(existingCategory);
            _mockCategoryRepository.Setup(r => r.GetAllAsync()).ReturnsAsync(new List<Category> { existingCategory });
            _mockCategoryRepository.Setup(r => r.Update(It.IsAny<Category>()));
            _mockUnitOfWork.Setup(u => u.SaveChangesAsync()).Returns(Task.CompletedTask);

            // Act
            var result = await _categoryService.UpdateCategoryAsync(updateDto);

            // Assert
            result.Should().NotBeNull();
            result.Name.Should().Be("Updated Category");
            _mockCategoryRepository.Verify(r => r.Update(It.IsAny<Category>()), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveChangesAsync(), Times.Once);
        }

        [Fact]
        public async Task DeleteCategoryAsync_WithNoProducts_ShouldDeleteCategory()
        {
            // Arrange
            var category = new Category { Id = 1, Name = "Category to Delete" };
            
            _mockCategoryRepository.Setup(r => r.GetByIdAsync(1)).ReturnsAsync(category);
            _mockProductRepository.Setup(r => r.AnyAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Product, bool>>>()))
                .ReturnsAsync(false);
            _mockCategoryRepository.Setup(r => r.Remove(It.IsAny<Category>()));
            _mockUnitOfWork.Setup(u => u.SaveChangesAsync()).Returns(Task.CompletedTask);

            // Act
            var result = await _categoryService.DeleteCategoryAsync(1);

            // Assert
            result.Should().BeTrue();
            _mockCategoryRepository.Verify(r => r.Remove(It.IsAny<Category>()), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveChangesAsync(), Times.Once);
        }

        [Fact]
        public async Task DeleteCategoryAsync_WithProducts_ShouldThrowException()
        {
            // Arrange
            var category = new Category { Id = 1, Name = "Category with Products" };
            
            _mockCategoryRepository.Setup(r => r.GetByIdAsync(1)).ReturnsAsync(category);
            _mockProductRepository.Setup(r => r.AnyAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Product, bool>>>()))
                .ReturnsAsync(true);

            // Act & Assert
            await Assert.ThrowsAsync<InvalidOperationException>(() => 
                _categoryService.DeleteCategoryAsync(1));
        }

        [Fact]
        public async Task CategoryExistsAsync_WithExistingId_ShouldReturnTrue()
        {
            // Arrange
            var category = new Category { Id = 1, Name = "Existing Category" };
            _mockCategoryRepository.Setup(r => r.GetByIdAsync(1)).ReturnsAsync(category);

            // Act
            var result = await _categoryService.CategoryExistsAsync(1);

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public async Task CategoryExistsAsync_WithNonExistingId_ShouldReturnFalse()
        {
            // Arrange
            _mockCategoryRepository.Setup(r => r.GetByIdAsync(999)).ReturnsAsync((Category?)null);

            // Act
            var result = await _categoryService.CategoryExistsAsync(999);

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public async Task GetPagedCategoriesAsync_ShouldReturnPagedResults()
        {
            // Arrange
            var categories = new List<Category>
            {
                new Category { Id = 1, Name = "Category 1" },
                new Category { Id = 2, Name = "Category 2" },
                new Category { Id = 3, Name = "Category 3" }
            };
            _mockCategoryRepository.Setup(r => r.GetAllAsync()).ReturnsAsync(categories);

            // Act
            var (result, totalCount) = await _categoryService.GetPagedCategoriesAsync(1, 2);

            // Assert
            result.Should().HaveCount(2);
            totalCount.Should().Be(3);
        }

        [Theory]
        [InlineData(0)]
        [InlineData(-1)]
        public async Task GetCategoryByIdAsync_WithInvalidId_ShouldThrowArgumentException(int invalidId)
        {
            // Act & Assert
            await Assert.ThrowsAsync<ArgumentException>(() => 
                _categoryService.GetCategoryByIdAsync(invalidId));
        }

        [Fact]
        public async Task CreateCategoryAsync_WithNullDto_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            await Assert.ThrowsAsync<ArgumentNullException>(() => 
                _categoryService.CreateCategoryAsync(null!));
        }
    }
}
