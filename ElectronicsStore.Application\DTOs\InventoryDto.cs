namespace ElectronicsStore.Application.DTOs
{
    public class InventoryLogDto
    {
        public int Id { get; set; }
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public string MovementType { get; set; } = string.Empty;
        public int Quantity { get; set; }
        public decimal UnitCost { get; set; }
        public string ReferenceTable { get; set; } = string.Empty;
        public int ReferenceId { get; set; }
        public string? Note { get; set; }
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
    }

    public class CreateInventoryLogDto
    {
        public int ProductId { get; set; }
        public string MovementType { get; set; } = string.Empty; // purchase, sale, return_sale, return_purchase, adjust
        public int Quantity { get; set; }
        public decimal UnitCost { get; set; }
        public string ReferenceTable { get; set; } = string.Empty;
        public int ReferenceId { get; set; }
        public string? Note { get; set; }
        public int UserId { get; set; }
    }

    public class StockAdjustmentDto
    {
        public int ProductId { get; set; }
        public int AdjustmentQuantity { get; set; } // Can be positive or negative
        public string Reason { get; set; } = string.Empty;
        public int UserId { get; set; }
    }

    public class InventoryMovementDto
    {
        public string MovementType { get; set; } = string.Empty;
        public int Count { get; set; }
        public int TotalQuantity { get; set; }
        public decimal TotalValue { get; set; }
    }

    public class DailyMovementDto
    {
        public DateTime Date { get; set; }
        public int TotalMovements { get; set; }
        public int TotalIn { get; set; }
        public int TotalOut { get; set; }
        public decimal TotalValue { get; set; }
    }

    public class InventoryValuationDto
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public int CurrentStock { get; set; }
        public decimal AverageCost { get; set; }
        public decimal TotalValue { get; set; }
    }

    public class LowStockAlertDto
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public string CategoryName { get; set; } = string.Empty;
        public int CurrentStock { get; set; }
        public DateTime? LastMovement { get; set; }
        public string AlertLevel { get; set; } = string.Empty; // Low, Critical, OutOfStock
    }

    public class InventorySearchDto
    {
        public int? ProductId { get; set; }
        public string? MovementType { get; set; }
        public int? UserId { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string? SearchTerm { get; set; }
    }

    public class StockTransferDto
    {
        public int FromProductId { get; set; }
        public int ToProductId { get; set; }
        public int Quantity { get; set; }
        public string Reason { get; set; } = string.Empty;
        public int UserId { get; set; }
    }
}
