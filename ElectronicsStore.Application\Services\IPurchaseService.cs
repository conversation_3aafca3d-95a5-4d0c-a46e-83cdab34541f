using ElectronicsStore.Application.DTOs;

namespace ElectronicsStore.Application.Services
{
    public interface IPurchaseService
    {
        // Basic CRUD operations
        Task<PurchaseInvoiceDto?> GetPurchaseInvoiceByIdAsync(int id);
        Task<IEnumerable<PurchaseInvoiceDto>> GetAllPurchaseInvoicesAsync();
        Task<PurchaseInvoiceDto> CreatePurchaseInvoiceAsync(CreatePurchaseInvoiceDto createDto, int userId);
        Task<PurchaseInvoiceDto> UpdatePurchaseInvoiceAsync(UpdatePurchaseInvoiceDto updateDto);
        Task<bool> DeletePurchaseInvoiceAsync(int id);

        // Search and filtering
        Task<IEnumerable<PurchaseInvoiceDto>> SearchPurchaseInvoicesAsync(PurchaseSearchDto searchDto);
        Task<PurchaseInvoiceDto?> GetPurchaseInvoiceByNumberAsync(string invoiceNumber);
        Task<IEnumerable<PurchaseInvoiceDto>> GetPurchaseInvoicesBySupplierAsync(int supplierId);
        Task<IEnumerable<PurchaseInvoiceDto>> GetPurchaseInvoicesByUserAsync(int userId);
        Task<IEnumerable<PurchaseInvoiceDto>> GetPurchaseInvoicesByDateRangeAsync(DateTime fromDate, DateTime toDate);

        // Reports and analytics
        Task<IEnumerable<PurchaseReportDto>> GetDailyPurchaseReportAsync(DateTime date);
        Task<IEnumerable<PurchaseReportDto>> GetMonthlyPurchaseReportAsync(int year, int month);
        Task<IEnumerable<PurchaseReportDto>> GetYearlyPurchaseReportAsync(int year);
        Task<PurchaseStatisticsDto> GetPurchaseStatisticsAsync();
        Task<IEnumerable<object>> GetTopSuppliersAsync(int count = 10);
        Task<IEnumerable<object>> GetTopProductsAsync(int count = 10);

        // Today's purchases
        Task<IEnumerable<PurchaseInvoiceDto>> GetTodayPurchasesAsync();
        Task<decimal> GetTodayPurchasesTotalAsync();

        // Validation
        Task<bool> PurchaseInvoiceExistsAsync(int id);
        Task<bool> InvoiceNumberExistsAsync(string invoiceNumber, int? excludeId = null);
        Task<bool> CanDeletePurchaseInvoiceAsync(int id);

        // Pagination
        Task<(IEnumerable<PurchaseInvoiceDto> Invoices, int TotalCount)> GetPagedPurchaseInvoicesAsync(
            int pageNumber, int pageSize, PurchaseSearchDto? searchDto = null);

        // Invoice details
        Task<IEnumerable<PurchaseInvoiceDetailDto>> GetPurchaseInvoiceDetailsAsync(int invoiceId);
        Task<PurchaseInvoiceDetailDto?> GetPurchaseInvoiceDetailByIdAsync(int detailId);
    }
}
