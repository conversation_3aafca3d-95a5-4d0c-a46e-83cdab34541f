using ElectronicsStore.Application.Interfaces;
using ElectronicsStore.Application.Services;
using ElectronicsStore.Domain.Entities;

namespace ElectronicsStore.Infrastructure.Services
{
    public class SeedDataService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IUserService _userService;

        public SeedDataService(IUnitOfWork unitOfWork, IUserService userService)
        {
            _unitOfWork = unitOfWork;
            _userService = userService;
        }

        public async Task SeedAsync()
        {
            await SeedRolesAsync();
            await SeedUsersAsync();
            await SeedCategoriesAsync();
        }

        private async Task SeedRolesAsync()
        {
            var roles = new[]
            {
                new Role { Name = "admin" },
                new Role { Name = "manager" },
                new Role { Name = "cashier" }
            };

            foreach (var role in roles)
            {
                var existingRole = await _unitOfWork.Roles.FirstOrDefaultAsync(r => r.Name == role.Name);
                if (existingRole == null)
                {
                    await _unitOfWork.Roles.AddAsync(role);
                }
            }

            await _unitOfWork.SaveChangesAsync();
        }

        private async Task SeedUsersAsync()
        {
            // Get roles
            var adminRole = await _unitOfWork.Roles.FirstOrDefaultAsync(r => r.Name == "admin");
            var managerRole = await _unitOfWork.Roles.FirstOrDefaultAsync(r => r.Name == "manager");
            var cashierRole = await _unitOfWork.Roles.FirstOrDefaultAsync(r => r.Name == "cashier");

            if (adminRole == null || managerRole == null || cashierRole == null)
                return;

            var users = new[]
            {
                new { Username = "admin", Password = "admin123", RoleId = adminRole.Id },
                new { Username = "manager", Password = "manager123", RoleId = managerRole.Id },
                new { Username = "cashier", Password = "cashier123", RoleId = cashierRole.Id }
            };

            foreach (var userData in users)
            {
                var existingUser = await _unitOfWork.Users.FirstOrDefaultAsync(u => u.Username == userData.Username);
                if (existingUser == null)
                {
                    var user = new User
                    {
                        Username = userData.Username,
                        Password = _userService.HashPassword(userData.Password),
                        RoleId = userData.RoleId,
                        CreatedAt = DateTime.Now
                    };

                    await _unitOfWork.Users.AddAsync(user);
                }
            }

            await _unitOfWork.SaveChangesAsync();
        }

        private async Task SeedCategoriesAsync()
        {
            var categories = new[]
            {
                new Category { Name = "Smartphones" },
                new Category { Name = "Laptops" },
                new Category { Name = "Tablets" },
                new Category { Name = "Accessories" },
                new Category { Name = "Audio" }
            };

            foreach (var category in categories)
            {
                var existingCategory = await _unitOfWork.Categories.FirstOrDefaultAsync(c => c.Name == category.Name);
                if (existingCategory == null)
                {
                    await _unitOfWork.Categories.AddAsync(category);
                }
            }

            await _unitOfWork.SaveChangesAsync();
        }
    }
}
