using ElectronicsStore.Application.DTOs;

namespace ElectronicsStore.Application.Services
{
    public interface IInventoryService
    {
        // Basic inventory operations
        Task<InventoryLogDto?> GetInventoryLogByIdAsync(int id);
        Task<IEnumerable<InventoryLogDto>> GetAllInventoryLogsAsync();
        Task<InventoryLogDto> CreateInventoryLogAsync(CreateInventoryLogDto createInventoryLogDto);

        // Movement queries
        Task<IEnumerable<InventoryLogDto>> GetMovementsByProductAsync(int productId);
        Task<IEnumerable<InventoryLogDto>> GetMovementsByTypeAsync(string movementType);
        Task<IEnumerable<InventoryLogDto>> GetMovementsByUserAsync(int userId);
        Task<IEnumerable<InventoryLogDto>> GetMovementsByDateRangeAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<InventoryLogDto>> GetRecentMovementsAsync(int count = 50);

        // Stock management
        Task<int> GetCurrentStockAsync(int productId);
        Task<Dictionary<int, int>> GetCurrentStockForProductsAsync(IEnumerable<int> productIds);
        Task<IEnumerable<ProductStockDto>> GetStockSummaryAsync();
        Task<bool> AdjustStockAsync(StockAdjustmentDto adjustmentDto);

        // Stock alerts
        Task<IEnumerable<LowStockAlertDto>> GetLowStockAlertsAsync(int threshold = 10);
        Task<IEnumerable<LowStockAlertDto>> GetOutOfStockProductsAsync();
        Task<IEnumerable<LowStockAlertDto>> GetCriticalStockAlertsAsync(int criticalThreshold = 5);

        // Inventory valuation
        Task<decimal> GetInventoryValueAsync(int? productId = null);
        Task<IEnumerable<InventoryValuationDto>> GetInventoryValuationReportAsync();
        Task<decimal> GetTotalInventoryValueAsync();

        // Movement analytics
        Task<IEnumerable<InventoryMovementDto>> GetMovementsByTypeReportAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<DailyMovementDto>> GetDailyMovementsReportAsync(DateTime fromDate, DateTime toDate);
        Task<object> GetInventoryStatisticsAsync(DateTime? fromDate = null, DateTime? toDate = null);

        // Advanced operations
        Task<bool> TransferStockAsync(StockTransferDto transferDto);
        Task<bool> BulkAdjustStockAsync(IEnumerable<StockAdjustmentDto> adjustments);
        Task<IEnumerable<InventoryLogDto>> SearchMovementsAsync(InventorySearchDto searchDto);

        // Validation
        Task<bool> CanAdjustStockAsync(int productId, int quantity);
        Task<bool> HasSufficientStockAsync(int productId, int requiredQuantity);

        // Pagination
        Task<(IEnumerable<InventoryLogDto> Movements, int TotalCount)> GetPagedMovementsAsync(
            int pageNumber, int pageSize, InventorySearchDto? searchDto = null);

        // FIFO/LIFO calculations
        Task<decimal> CalculateAverageCostAsync(int productId);
        Task<decimal> CalculateFIFOCostAsync(int productId, int quantity);
        Task<IEnumerable<object>> GetCostAnalysisAsync(int productId);

        // Audit and tracking
        Task<IEnumerable<InventoryLogDto>> GetAuditTrailAsync(int productId, DateTime? fromDate = null, DateTime? toDate = null);
        Task<object> GetMovementSummaryAsync(int productId);
    }
}
