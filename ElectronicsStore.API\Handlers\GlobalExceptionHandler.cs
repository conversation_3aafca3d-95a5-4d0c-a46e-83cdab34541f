using Microsoft.AspNetCore.Diagnostics;
using ElectronicsStore.Application.Common;
using System.Net;
using System.Text.Json;

namespace ElectronicsStore.API.Handlers
{
    /// <summary>
    /// Global exception handler using .NET 8 IExceptionHandler interface
    /// </summary>
    public class GlobalExceptionHandler : IExceptionHandler
    {
        private readonly ILogger<GlobalExceptionHandler> _logger;

        public GlobalExceptionHandler(ILogger<GlobalExceptionHandler> logger)
        {
            _logger = logger;
        }

        public async ValueTask<bool> TryHandleAsync(
            HttpContext httpContext, 
            Exception exception, 
            CancellationToken cancellationToken)
        {
            // Log the exception with structured logging
            LogException(exception, httpContext);

            // Create standardized error response
            var errorResponse = CreateErrorResponse(exception, httpContext);

            // Set response properties
            httpContext.Response.StatusCode = errorResponse.StatusCode;
            httpContext.Response.ContentType = "application/json";

            // Serialize and write response
            var jsonResponse = JsonSerializer.Serialize(errorResponse.Response, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = true
            });

            await httpContext.Response.WriteAsync(jsonResponse, cancellationToken);

            return true; // Exception handled
        }

        private void LogException(Exception exception, HttpContext httpContext)
        {
            var userId = GetUserIdFromContext(httpContext);
            var userRole = GetUserRoleFromContext(httpContext);
            var requestPath = httpContext.Request.Path;
            var requestMethod = httpContext.Request.Method;
            var userAgent = httpContext.Request.Headers.UserAgent.ToString();
            var ipAddress = httpContext.Connection.RemoteIpAddress?.ToString();

            // Structured logging with context information
            _logger.LogError(exception,
                "Unhandled exception occurred. " +
                "UserId: {UserId}, UserRole: {UserRole}, " +
                "Method: {Method}, Path: {Path}, " +
                "UserAgent: {UserAgent}, IP: {IPAddress}, " +
                "ExceptionType: {ExceptionType}, Message: {Message}",
                userId, userRole, requestMethod, requestPath,
                userAgent, ipAddress, exception.GetType().Name, exception.Message);

            // Log additional context for specific exception types
            switch (exception)
            {
                case UnauthorizedAccessException:
                    _logger.LogWarning("Security: Unauthorized access attempt by User {UserId} from IP {IPAddress} to {Method} {Path}",
                        userId, ipAddress, requestMethod, requestPath);
                    break;

                case InvalidOperationException when exception.Message.Contains("already exists"):
                    _logger.LogInformation("Business Logic: Duplicate entity creation attempt by User {UserId}: {Message}",
                        userId, exception.Message);
                    break;

                case ArgumentException:
                    _logger.LogWarning("Validation: Invalid argument provided by User {UserId} to {Method} {Path}: {Message}",
                        userId, requestMethod, requestPath, exception.Message);
                    break;
            }
        }

        private (int StatusCode, ApiResponse Response) CreateErrorResponse(Exception exception, HttpContext httpContext)
        {
            var traceId = httpContext.TraceIdentifier;
            var timestamp = DateTime.UtcNow;

            return exception switch
            {
                UnauthorizedAccessException => (
                    (int)HttpStatusCode.Unauthorized,
                    CreateApiResponse("Unauthorized access.", "You don't have permission to access this resource.", traceId, timestamp)
                ),

                InvalidOperationException => (
                    (int)HttpStatusCode.BadRequest,
                    CreateApiResponse("Invalid operation.", exception.Message, traceId, timestamp)
                ),

                ArgumentNullException => (
                    (int)HttpStatusCode.BadRequest,
                    CreateApiResponse("Missing required parameter.", exception.Message, traceId, timestamp)
                ),

                ArgumentException => (
                    (int)HttpStatusCode.BadRequest,
                    CreateApiResponse("Invalid argument.", exception.Message, traceId, timestamp)
                ),

                KeyNotFoundException => (
                    (int)HttpStatusCode.NotFound,
                    CreateApiResponse("Resource not found.", exception.Message, traceId, timestamp)
                ),

                TimeoutException => (
                    (int)HttpStatusCode.RequestTimeout,
                    CreateApiResponse("Request timeout.", "The request took too long to process. Please try again.", traceId, timestamp)
                ),

                NotImplementedException => (
                    (int)HttpStatusCode.NotImplemented,
                    CreateApiResponse("Feature not implemented.", "This feature is not yet available.", traceId, timestamp)
                ),

                TaskCanceledException => (
                    (int)HttpStatusCode.RequestTimeout,
                    CreateApiResponse("Request cancelled.", "The request was cancelled or timed out.", traceId, timestamp)
                ),

                // Default case for unhandled exceptions
                _ => (
                    (int)HttpStatusCode.InternalServerError,
                    CreateApiResponse("Internal server error.", "An unexpected error occurred. Please contact support if the problem persists.", traceId, timestamp)
                )
            };
        }

        private static ApiResponse CreateApiResponse(string message, string details, string traceId, DateTime timestamp)
        {
            return new ApiResponse
            {
                Success = false,
                Message = message,
                Errors = new List<string> { details },
                Timestamp = timestamp,
                TraceId = traceId
            };
        }

        private static int? GetUserIdFromContext(HttpContext httpContext)
        {
            try
            {
                var userIdClaim = httpContext.User?.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ??
                                 httpContext.User?.FindFirst("UserId")?.Value;
                
                return int.TryParse(userIdClaim, out int userId) ? userId : null;
            }
            catch
            {
                return null;
            }
        }

        private static string? GetUserRoleFromContext(HttpContext httpContext)
        {
            try
            {
                return httpContext.User?.FindFirst(System.Security.Claims.ClaimTypes.Role)?.Value;
            }
            catch
            {
                return null;
            }
        }
    }

    /// <summary>
    /// Extension methods for registering the global exception handler
    /// </summary>
    public static class GlobalExceptionHandlerExtensions
    {
        /// <summary>
        /// Add global exception handler to the service collection
        /// </summary>
        public static IServiceCollection AddGlobalExceptionHandler(this IServiceCollection services)
        {
            services.AddExceptionHandler<GlobalExceptionHandler>();
            services.AddProblemDetails();
            return services;
        }

        /// <summary>
        /// Use global exception handler in the application pipeline
        /// </summary>
        public static IApplicationBuilder UseGlobalExceptionHandler(this IApplicationBuilder app)
        {
            app.UseExceptionHandler();
            return app;
        }
    }
}
