using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ElectronicsStore.Application.DTOs;
using ElectronicsStore.Application.Services;
using System.Security.Claims;

namespace ElectronicsStore.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ReturnsController : ControllerBase
    {
        private readonly IReturnService _returnService;

        public ReturnsController(IReturnService returnService)
        {
            _returnService = returnService;
        }

        #region Sales Returns

        /// <summary>
        /// Create a new sales return
        /// </summary>
        /// <param name="createDto">Sales return creation data</param>
        /// <returns>Created sales return</returns>
        [HttpPost("sales")]
        [Authorize(Roles = "admin,manager,cashier")]
        public async Task<ActionResult<SalesReturnDto>> CreateSalesReturn(CreateSalesReturnDto createDto)
        {
            try
            {
                var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
                var salesReturn = await _returnService.CreateSalesReturnAsync(createDto, userId);
                return CreatedAtAction(nameof(GetSalesReturn), new { id = salesReturn.Id }, salesReturn);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Get sales return by ID
        /// </summary>
        /// <param name="id">Sales return ID</param>
        /// <returns>Sales return details</returns>
        [HttpGet("sales/{id}")]
        [Authorize(Roles = "admin,manager,cashier")]
        public async Task<ActionResult<SalesReturnDto>> GetSalesReturn(int id)
        {
            var salesReturn = await _returnService.GetSalesReturnByIdAsync(id);
            if (salesReturn == null)
                return NotFound($"Sales return with ID {id} not found.");

            return Ok(salesReturn);
        }

        /// <summary>
        /// Get all sales returns
        /// </summary>
        /// <returns>List of sales returns</returns>
        [HttpGet("sales")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<SalesReturnDto>>> GetAllSalesReturns()
        {
            var salesReturns = await _returnService.GetAllSalesReturnsAsync();
            return Ok(salesReturns);
        }

        /// <summary>
        /// Get sales returns by invoice ID
        /// </summary>
        /// <param name="invoiceId">Sales invoice ID</param>
        /// <returns>List of sales returns for the invoice</returns>
        [HttpGet("sales/invoice/{invoiceId}")]
        [Authorize(Roles = "admin,manager,cashier")]
        public async Task<ActionResult<IEnumerable<SalesReturnDto>>> GetSalesReturnsByInvoice(int invoiceId)
        {
            var salesReturns = await _returnService.GetSalesReturnsByInvoiceAsync(invoiceId);
            return Ok(salesReturns);
        }

        /// <summary>
        /// Get sales returns by product ID
        /// </summary>
        /// <param name="productId">Product ID</param>
        /// <returns>List of sales returns for the product</returns>
        [HttpGet("sales/product/{productId}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<SalesReturnDto>>> GetSalesReturnsByProduct(int productId)
        {
            var salesReturns = await _returnService.GetSalesReturnsByProductAsync(productId);
            return Ok(salesReturns);
        }

        /// <summary>
        /// Get sales returns by user ID
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>List of sales returns created by the user</returns>
        [HttpGet("sales/user/{userId}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<SalesReturnDto>>> GetSalesReturnsByUser(int userId)
        {
            var salesReturns = await _returnService.GetSalesReturnsByUserAsync(userId);
            return Ok(salesReturns);
        }

        /// <summary>
        /// Delete a sales return
        /// </summary>
        /// <param name="id">Sales return ID</param>
        /// <returns>Success status</returns>
        [HttpDelete("sales/{id}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult> DeleteSalesReturn(int id)
        {
            try
            {
                var result = await _returnService.DeleteSalesReturnAsync(id);
                if (!result)
                    return NotFound($"Sales return with ID {id} not found.");

                return NoContent();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        #endregion

        #region Purchase Returns

        /// <summary>
        /// Create a new purchase return
        /// </summary>
        /// <param name="createDto">Purchase return creation data</param>
        /// <returns>Created purchase return</returns>
        [HttpPost("purchases")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<PurchaseReturnDto>> CreatePurchaseReturn(CreatePurchaseReturnDto createDto)
        {
            try
            {
                var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
                var purchaseReturn = await _returnService.CreatePurchaseReturnAsync(createDto, userId);
                return CreatedAtAction(nameof(GetPurchaseReturn), new { id = purchaseReturn.Id }, purchaseReturn);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Get purchase return by ID
        /// </summary>
        /// <param name="id">Purchase return ID</param>
        /// <returns>Purchase return details</returns>
        [HttpGet("purchases/{id}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<PurchaseReturnDto>> GetPurchaseReturn(int id)
        {
            var purchaseReturn = await _returnService.GetPurchaseReturnByIdAsync(id);
            if (purchaseReturn == null)
                return NotFound($"Purchase return with ID {id} not found.");

            return Ok(purchaseReturn);
        }

        /// <summary>
        /// Get all purchase returns
        /// </summary>
        /// <returns>List of purchase returns</returns>
        [HttpGet("purchases")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<PurchaseReturnDto>>> GetAllPurchaseReturns()
        {
            var purchaseReturns = await _returnService.GetAllPurchaseReturnsAsync();
            return Ok(purchaseReturns);
        }

        /// <summary>
        /// Get purchase returns by invoice ID
        /// </summary>
        /// <param name="invoiceId">Purchase invoice ID</param>
        /// <returns>List of purchase returns for the invoice</returns>
        [HttpGet("purchases/invoice/{invoiceId}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<PurchaseReturnDto>>> GetPurchaseReturnsByInvoice(int invoiceId)
        {
            var purchaseReturns = await _returnService.GetPurchaseReturnsByInvoiceAsync(invoiceId);
            return Ok(purchaseReturns);
        }

        /// <summary>
        /// Get purchase returns by product ID
        /// </summary>
        /// <param name="productId">Product ID</param>
        /// <returns>List of purchase returns for the product</returns>
        [HttpGet("purchases/product/{productId}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<PurchaseReturnDto>>> GetPurchaseReturnsByProduct(int productId)
        {
            var purchaseReturns = await _returnService.GetPurchaseReturnsByProductAsync(productId);
            return Ok(purchaseReturns);
        }

        /// <summary>
        /// Get purchase returns by user ID
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>List of purchase returns created by the user</returns>
        [HttpGet("purchases/user/{userId}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<PurchaseReturnDto>>> GetPurchaseReturnsByUser(int userId)
        {
            var purchaseReturns = await _returnService.GetPurchaseReturnsByUserAsync(userId);
            return Ok(purchaseReturns);
        }

        /// <summary>
        /// Delete a purchase return
        /// </summary>
        /// <param name="id">Purchase return ID</param>
        /// <returns>Success status</returns>
        [HttpDelete("purchases/{id}")]
        [Authorize(Roles = "admin")]
        public async Task<ActionResult> DeletePurchaseReturn(int id)
        {
            try
            {
                var result = await _returnService.DeletePurchaseReturnAsync(id);
                if (!result)
                    return NotFound($"Purchase return with ID {id} not found.");

                return NoContent();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        #endregion

        #region Combined Operations

        /// <summary>
        /// Get all returns (sales and purchase combined)
        /// </summary>
        /// <returns>List of all returns</returns>
        [HttpGet]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<object>>> GetAllReturns()
        {
            var returns = await _returnService.GetAllReturnsAsync();
            return Ok(returns);
        }

        /// <summary>
        /// Search returns
        /// </summary>
        /// <param name="searchDto">Search criteria</param>
        /// <returns>List of matching returns</returns>
        [HttpGet("search")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<object>>> SearchReturns([FromQuery] ReturnSearchDto searchDto)
        {
            var returns = await _returnService.SearchReturnsAsync(searchDto);
            return Ok(returns);
        }

        /// <summary>
        /// Get returns by date range
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <returns>List of returns in the date range</returns>
        [HttpGet("date-range")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<object>>> GetReturnsByDateRange(
            [FromQuery] DateTime fromDate, [FromQuery] DateTime toDate)
        {
            var returns = await _returnService.GetReturnsByDateRangeAsync(fromDate, toDate);
            return Ok(returns);
        }

        /// <summary>
        /// Get today's returns
        /// </summary>
        /// <returns>List of today's returns</returns>
        [HttpGet("today")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<object>>> GetTodayReturns()
        {
            var returns = await _returnService.GetTodayReturnsAsync();
            return Ok(returns);
        }

        #endregion

        #region Statistics and Reports

        /// <summary>
        /// Get return statistics
        /// </summary>
        /// <returns>Return statistics</returns>
        [HttpGet("statistics")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<ReturnStatisticsDto>> GetReturnStatistics()
        {
            var statistics = await _returnService.GetReturnStatisticsAsync();
            return Ok(statistics);
        }

        /// <summary>
        /// Get daily return report
        /// </summary>
        /// <param name="date">Report date</param>
        /// <returns>Daily return report</returns>
        [HttpGet("reports/daily")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<ReturnReportDto>>> GetDailyReturnReport([FromQuery] DateTime date)
        {
            var report = await _returnService.GetDailyReturnReportAsync(date);
            return Ok(report);
        }

        /// <summary>
        /// Get top returned products
        /// </summary>
        /// <param name="count">Number of products to return</param>
        /// <returns>List of top returned products</returns>
        [HttpGet("top-products")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<TopReturnedProductDto>>> GetTopReturnedProducts([FromQuery] int count = 10)
        {
            var products = await _returnService.GetTopReturnedProductsAsync(count);
            return Ok(products);
        }

        /// <summary>
        /// Get return reason analysis
        /// </summary>
        /// <returns>Return reason analysis</returns>
        [HttpGet("reason-analysis")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<ReturnReasonAnalysisDto>>> GetReturnReasonAnalysis()
        {
            var analysis = await _returnService.GetReturnReasonAnalysisAsync();
            return Ok(analysis);
        }

        /// <summary>
        /// Check if sales item can be returned
        /// </summary>
        /// <param name="salesInvoiceId">Sales invoice ID</param>
        /// <param name="productId">Product ID</param>
        /// <param name="quantity">Quantity to return</param>
        /// <returns>Validation result</returns>
        [HttpGet("sales/can-return")]
        [Authorize(Roles = "admin,manager,cashier")]
        public async Task<ActionResult<object>> CanReturnSalesItem(
            [FromQuery] int salesInvoiceId, [FromQuery] int productId, [FromQuery] int quantity)
        {
            var canReturn = await _returnService.CanReturnSalesItemAsync(salesInvoiceId, productId, quantity);
            var maxReturnable = await _returnService.GetMaxReturnableQuantityAsync(salesInvoiceId, productId, "sales");

            return Ok(new
            {
                CanReturn = canReturn,
                MaxReturnableQuantity = maxReturnable,
                RequestedQuantity = quantity,
                SalesInvoiceId = salesInvoiceId,
                ProductId = productId
            });
        }

        #endregion
    }
}
