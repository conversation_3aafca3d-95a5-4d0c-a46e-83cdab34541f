using Microsoft.EntityFrameworkCore;
using ElectronicsStore.Application.Interfaces;
using ElectronicsStore.Domain.Entities;
using System.Linq.Expressions;

namespace ElectronicsStore.Persistence.Repositories
{
    public class ProductRepository : GenericRepository<Product>, IProductRepository
    {
        public ProductRepository(ElectronicsDbContext context) : base(context)
        {
        }

        public async Task<Product?> GetByBarcodeAsync(string barcode)
        {
            return await _dbSet.FirstOrDefaultAsync(p => p.Barcode == barcode);
        }

        public async Task<IEnumerable<Product>> GetByCategoryAsync(int categoryId)
        {
            return await _dbSet
                .Include(p => p.Category)
                .Include(p => p.Supplier)
                .Where(p => p.CategoryId == categoryId)
                .ToListAsync();
        }

        public async Task<IEnumerable<Product>> GetBySupplierAsync(int supplierId)
        {
            return await _dbSet
                .Include(p => p.Category)
                .Include(p => p.Supplier)
                .Where(p => p.SupplierId == supplierId)
                .ToListAsync();
        }

        public async Task<IEnumerable<Product>> SearchByNameAsync(string searchTerm)
        {
            return await _dbSet
                .Include(p => p.Category)
                .Include(p => p.Supplier)
                .Where(p => p.Name.Contains(searchTerm) || 
                           (p.Barcode != null && p.Barcode.Contains(searchTerm)))
                .ToListAsync();
        }

        public async Task<bool> BarcodeExistsAsync(string barcode, int? excludeId = null)
        {
            var query = _dbSet.Where(p => p.Barcode == barcode);
            if (excludeId.HasValue)
                query = query.Where(p => p.Id != excludeId.Value);
            
            return await query.AnyAsync();
        }

        public async Task<IEnumerable<Product>> GetLowStockProductsAsync(int threshold = 10)
        {
            var query = @"
                SELECT p.*, c.name as CategoryName, s.name as SupplierName,
                       ISNULL(SUM(il.quantity), 0) as CurrentStock
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                LEFT JOIN suppliers s ON p.supplier_id = s.id
                LEFT JOIN inventory_logs il ON p.id = il.product_id
                GROUP BY p.id, p.name, p.barcode, p.category_id, p.supplier_id, 
                         p.default_cost_price, p.default_selling_price, p.min_selling_price, 
                         p.description, p.created_at, c.name, s.name
                HAVING ISNULL(SUM(il.quantity), 0) <= {0}";

            return await _dbSet
                .FromSqlRaw(query, threshold)
                .Include(p => p.Category)
                .Include(p => p.Supplier)
                .ToListAsync();
        }

        public async Task<IEnumerable<Product>> GetOutOfStockProductsAsync()
        {
            return await GetLowStockProductsAsync(0);
        }

        public async Task<Product?> GetWithCurrentStockAsync(int productId)
        {
            return await _dbSet
                .Include(p => p.Category)
                .Include(p => p.Supplier)
                .FirstOrDefaultAsync(p => p.Id == productId);
        }

        public async Task<IEnumerable<Product>> GetWithCurrentStockAsync()
        {
            return await _dbSet
                .Include(p => p.Category)
                .Include(p => p.Supplier)
                .ToListAsync();
        }

        public async Task<IEnumerable<Product>> GetProductsInPriceRangeAsync(decimal minPrice, decimal maxPrice)
        {
            return await _dbSet
                .Include(p => p.Category)
                .Include(p => p.Supplier)
                .Where(p => p.DefaultSellingPrice >= minPrice && p.DefaultSellingPrice <= maxPrice)
                .ToListAsync();
        }

        public async Task<decimal> GetAveragePriceAsync()
        {
            return await _dbSet.AverageAsync(p => p.DefaultSellingPrice);
        }

        public async Task<decimal> GetTotalInventoryValueAsync()
        {
            var query = @"
                SELECT ISNULL(SUM(il.quantity * il.unit_cost), 0)
                FROM inventory_logs il
                WHERE il.quantity > 0";

            return await _context.Database.SqlQueryRaw<decimal>(query).FirstAsync();
        }

        public async Task<IEnumerable<Product>> GetTopSellingProductsAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _context.SalesInvoiceDetails
                .Include(sid => sid.Product)
                .ThenInclude(p => p.Category)
                .Include(sid => sid.Product)
                .ThenInclude(p => p.Supplier)
                .Include(sid => sid.SalesInvoice)
                .AsQueryable();

            if (fromDate.HasValue)
                query = query.Where(sid => sid.SalesInvoice.InvoiceDate >= fromDate.Value);
            
            if (toDate.HasValue)
                query = query.Where(sid => sid.SalesInvoice.InvoiceDate <= toDate.Value);

            var topProducts = await query
                .GroupBy(sid => sid.Product)
                .OrderByDescending(g => g.Sum(sid => sid.Quantity))
                .Take(count)
                .Select(g => g.Key)
                .ToListAsync();

            return topProducts;
        }

        public async Task<IEnumerable<Product>> GetProductsWithDetailsAsync(Expression<Func<Product, bool>>? filter = null)
        {
            var query = _dbSet
                .Include(p => p.Category)
                .Include(p => p.Supplier)
                .AsQueryable();

            if (filter != null)
                query = query.Where(filter);

            return await query.ToListAsync();
        }

        public async Task<(IEnumerable<Product> Products, int TotalCount)> GetPagedWithStockAsync(
            int pageNumber, int pageSize, string? searchTerm = null, int? categoryId = null)
        {
            var query = _dbSet
                .Include(p => p.Category)
                .Include(p => p.Supplier)
                .AsQueryable();

            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(p => p.Name.Contains(searchTerm) || 
                                        (p.Barcode != null && p.Barcode.Contains(searchTerm)));
            }

            if (categoryId.HasValue)
            {
                query = query.Where(p => p.CategoryId == categoryId.Value);
            }

            var totalCount = await query.CountAsync();
            var products = await query
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return (products, totalCount);
        }
    }
}
