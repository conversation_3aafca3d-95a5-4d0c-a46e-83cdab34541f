using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ElectronicsStore.Domain.Entities
{
    [Table("purchase_returns")]
    public class PurchaseReturn
    {
        [Key]
        [Column("id")]
        public int Id { get; set; }

        [Column("purchase_invoice_id")]
        public int PurchaseInvoiceId { get; set; }

        [Column("product_id")]
        public int ProductId { get; set; }

        [Column("quantity")]
        public int Quantity { get; set; }

        [Column("unit_cost", TypeName = "decimal(18,2)")]
        public decimal UnitCost { get; set; }

        [MaxLength(200)]
        [Column("reason")]
        public string? Reason { get; set; }

        [Column("user_id")]
        public int UserId { get; set; }

        [Column("created_at")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation properties
        [ForeignKey("PurchaseInvoiceId")]
        public virtual PurchaseInvoice PurchaseInvoice { get; set; } = null!;

        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; } = null!;

        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;
    }
}
