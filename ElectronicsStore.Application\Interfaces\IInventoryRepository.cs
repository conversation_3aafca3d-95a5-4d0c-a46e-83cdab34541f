using ElectronicsStore.Domain.Entities;

namespace ElectronicsStore.Application.Interfaces
{
    public interface IInventoryRepository : IGenericRepository<InventoryLog>
    {
        // Inventory movement methods
        Task<IEnumerable<InventoryLog>> GetByProductAsync(int productId);
        Task<IEnumerable<InventoryLog>> GetByMovementTypeAsync(string movementType);
        Task<IEnumerable<InventoryLog>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<InventoryLog>> GetByUserAsync(int userId);
        
        // Stock calculation methods
        Task<int> GetCurrentStockAsync(int productId);
        Task<Dictionary<int, int>> GetCurrentStockForProductsAsync(IEnumerable<int> productIds);
        Task<IEnumerable<object>> GetStockSummaryAsync();
        
        // Movement analysis
        Task<IEnumerable<InventoryLog>> GetRecentMovementsAsync(int count = 50);
        Task<IEnumerable<object>> GetMovementsByTypeAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<object>> GetDailyMovementsAsync(DateTime fromDate, DateTime toDate);
        
        // Stock valuation
        Task<decimal> GetInventoryValueAsync(int? productId = null);
        Task<IEnumerable<object>> GetInventoryValuationReportAsync();
        
        // Low stock alerts
        Task<IEnumerable<object>> GetLowStockAlertsAsync(int threshold = 10);
        Task<IEnumerable<object>> GetOutOfStockProductsAsync();
        
        // Advanced queries
        Task<IEnumerable<InventoryLog>> GetMovementsWithDetailsAsync(
            int? productId = null, string? movementType = null, DateTime? fromDate = null, DateTime? toDate = null);
        
        // Pagination
        Task<(IEnumerable<InventoryLog> Movements, int TotalCount)> GetPagedMovementsAsync(
            int pageNumber, int pageSize, int? productId = null, string? movementType = null, 
            DateTime? fromDate = null, DateTime? toDate = null);
    }
}
