version: '3.8'

services:
  # SQL Server Database
  sqlserver:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: electronics-store-db
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=YourStrong@Passw0rd
      - MSSQL_PID=Express
    ports:
      - "1433:1433"
    volumes:
      - sqlserver_data:/var/opt/mssql
    networks:
      - electronics-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "/opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P YourStrong@Passw0rd -Q 'SELECT 1'"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Electronics Store API
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: electronics-store-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Server=sqlserver;Database=ElectronicsStoreDB;User Id=sa;Password=YourStrong@Passw0rd;TrustServerCertificate=true;
      - JwtSettings__SecretKey=ElectronicsStore_SuperSecretKey_2024_ForJWT_Authentication_MinimumLength32Characters
      - JwtSettings__Issuer=ElectronicsStoreAPI
      - JwtSettings__Audience=ElectronicsStoreUsers
      - JwtSettings__ExpirationInMinutes=60
    ports:
      - "5000:80"
    depends_on:
      sqlserver:
        condition: service_healthy
    networks:
      - electronics-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:80/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    volumes:
      - ./Reports:/app/Reports
      - ./Logs:/app/Logs

  # Redis Cache (Optional - for future use)
  redis:
    image: redis:7-alpine
    container_name: electronics-store-redis
    ports:
      - "6379:6379"
    networks:
      - electronics-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    volumes:
      - redis_data:/data

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: electronics-store-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - api
    networks:
      - electronics-network
    restart: unless-stopped

volumes:
  sqlserver_data:
    driver: local
  redis_data:
    driver: local

networks:
  electronics-network:
    driver: bridge
