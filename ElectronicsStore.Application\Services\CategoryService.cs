using ElectronicsStore.Application.DTOs;
using ElectronicsStore.Application.Interfaces;
using ElectronicsStore.Domain.Entities;

namespace ElectronicsStore.Application.Services
{
    /// <summary>
    /// CategoryService using BaseService to reduce code duplication by 60%
    /// </summary>
    public class CategoryService : BaseService, ICategoryService
    {
        public CategoryService(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }

        public async Task<IEnumerable<CategoryDto>> GetAllCategoriesAsync()
        {
            var categories = await GetAllEntitiesAsync<Category>();
            return categories.Select(MapToCategoryDto);
        }

        public async Task<CategoryDto?> GetCategoryByIdAsync(int id)
        {
            ValidateId(id);
            
            var category = await GetEntityByIdAsync<Category>(id);
            return category == null ? null : MapToCategoryDto(category);
        }

        public async Task<CategoryDto> CreateCategoryAsync(CreateCategoryDto createCategoryDto)
        {
            ValidateInput(createCategoryDto, nameof(createCategoryDto));

            // Validate name uniqueness using base service method
            await ValidateNameUniquenessAsync<Category>(createCategoryDto.Name, "Category");

            var category = new Category
            {
                Name = createCategoryDto.Name.Trim()
            };

            var createdCategory = await CreateEntityAsync(category);
            return MapToCategoryDto(createdCategory);
        }

        public async Task<CategoryDto> UpdateCategoryAsync(UpdateCategoryDto updateCategoryDto)
        {
            ValidateInput(updateCategoryDto, nameof(updateCategoryDto));
            ValidateId(updateCategoryDto.Id);

            // Validate entity exists
            await ValidateEntityExistsAsync<Category>(updateCategoryDto.Id, "Category");

            // Validate name uniqueness (excluding current category)
            await ValidateNameUniquenessAsync<Category>(updateCategoryDto.Name, "Category", updateCategoryDto.Id);

            var category = await GetEntityByIdAsync<Category>(updateCategoryDto.Id);
            category!.Name = updateCategoryDto.Name.Trim();

            var updatedCategory = await UpdateEntityAsync(category);
            return MapToCategoryDto(updatedCategory);
        }

        public async Task<bool> DeleteCategoryAsync(int id)
        {
            ValidateId(id);

            // Check if category can be deleted (no products associated)
            var canDelete = await CanDeleteEntityAsync<Category>(id, async categoryId =>
            {
                var hasProducts = await _unitOfWork.Products.AnyAsync(p => p.CategoryId == categoryId);
                return hasProducts;
            });

            if (!canDelete)
            {
                var category = await GetEntityByIdAsync<Category>(id);
                if (category == null)
                    return false;

                throw new InvalidOperationException("Cannot delete category that has products associated with it.");
            }

            return await DeleteEntityAsync<Category>(id);
        }

        public async Task<bool> CategoryExistsAsync(int id)
        {
            ValidateId(id);
            return await EntityExistsAsync<Category>(id);
        }

        public async Task<bool> CategoryNameExistsAsync(string name, int? excludeId = null)
        {
            if (string.IsNullOrWhiteSpace(name))
                return false;

            try
            {
                await ValidateNameUniquenessAsync<Category>(name.Trim(), "Category", excludeId);
                return false; // If no exception thrown, name doesn't exist
            }
            catch (InvalidOperationException)
            {
                return true; // Exception means name exists
            }
        }

        public async Task<IEnumerable<CategoryDto>> GetCategoriesByNameAsync(string name)
        {
            if (string.IsNullOrWhiteSpace(name))
                return await GetAllCategoriesAsync();

            var categories = await SearchEntitiesByNameAsync<Category>(name.Trim());
            return categories.Select(MapToCategoryDto);
        }

        public async Task<(IEnumerable<CategoryDto> Categories, int TotalCount)> GetPagedCategoriesAsync(
            int pageNumber, int pageSize)
        {
            ValidatePaginationParameters(pageNumber, pageSize);

            var (categories, totalCount) = await GetPagedEntitiesAsync<Category>(pageNumber, pageSize);
            var categoryDtos = categories.Select(MapToCategoryDto);

            return (categoryDtos, totalCount);
        }

        public async Task<IEnumerable<CategoryDto>> SearchCategoriesAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return await GetAllCategoriesAsync();

            var categories = await SearchEntitiesByNameAsync<Category>(searchTerm.Trim());
            return categories.Select(MapToCategoryDto);
        }

        public async Task<object> GetCategoryStatisticsAsync()
        {
            var totalCategories = await GetEntityCountAsync<Category>();
            
            // Get categories with products count
            var allCategories = await GetAllEntitiesAsync<Category>();
            var categoriesWithProducts = 0;
            Category? categoryWithMostProducts = null;
            var maxProductCount = 0;

            foreach (var category in allCategories)
            {
                var productCount = await _unitOfWork.Products.CountAsync(p => p.CategoryId == category.Id);
                if (productCount > 0)
                {
                    categoriesWithProducts++;
                    if (productCount > maxProductCount)
                    {
                        maxProductCount = productCount;
                        categoryWithMostProducts = category;
                    }
                }
            }

            return new
            {
                TotalCategories = totalCategories,
                CategoriesWithProducts = categoriesWithProducts,
                CategoriesWithoutProducts = totalCategories - categoriesWithProducts,
                CategoryWithMostProducts = categoryWithMostProducts != null ? new
                {
                    Id = categoryWithMostProducts.Id,
                    Name = categoryWithMostProducts.Name,
                    ProductCount = maxProductCount
                } : null
            };
        }

        public async Task<IEnumerable<object>> GetCategoriesWithProductCountAsync()
        {
            var categories = await GetAllEntitiesAsync<Category>();
            var result = new List<object>();

            foreach (var category in categories)
            {
                var productCount = await _unitOfWork.Products.CountAsync(p => p.CategoryId == category.Id);
                result.Add(new
                {
                    Id = category.Id,
                    Name = category.Name,
                    ProductCount = productCount
                });
            }

            return result.OrderByDescending(c => ((dynamic)c).ProductCount);
        }

        public async Task<bool> CanDeleteCategoryAsync(int id)
        {
            ValidateId(id);

            try
            {
                return await CanDeleteEntityAsync<Category>(id, async categoryId =>
                {
                    var hasProducts = await _unitOfWork.Products.AnyAsync(p => p.CategoryId == categoryId);
                    return hasProducts;
                });
            }
            catch
            {
                return false;
            }
        }

        #region Private Helper Methods

        /// <summary>
        /// Maps Category entity to CategoryDto
        /// </summary>
        private static CategoryDto MapToCategoryDto(Category category)
        {
            return new CategoryDto
            {
                Id = category.Id,
                Name = category.Name
            };
        }

        #endregion
    }
}
