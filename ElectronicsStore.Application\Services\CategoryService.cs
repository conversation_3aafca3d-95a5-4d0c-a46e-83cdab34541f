using ElectronicsStore.Application.DTOs;
using ElectronicsStore.Application.Interfaces;
using ElectronicsStore.Domain.Entities;

namespace ElectronicsStore.Application.Services
{
    public class CategoryService : ICategoryService
    {
        private readonly IUnitOfWork _unitOfWork;

        public CategoryService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<CategoryDto>> GetAllCategoriesAsync()
        {
            var categories = await _unitOfWork.Categories.GetAllAsync();
            return categories.Select(c => new CategoryDto
            {
                Id = c.Id,
                Name = c.Name
            });
        }

        public async Task<CategoryDto?> GetCategoryByIdAsync(int id)
        {
            var category = await _unitOfWork.Categories.GetByIdAsync(id);
            if (category == null)
                return null;

            return new CategoryDto
            {
                Id = category.Id,
                Name = category.Name
            };
        }

        public async Task<CategoryDto> CreateCategoryAsync(CreateCategoryDto createCategoryDto)
        {
            // Check if category name already exists
            var existingCategory = await _unitOfWork.Categories.FirstOrDefaultAsync(c => c.Name == createCategoryDto.Name);
            if (existingCategory != null)
                throw new InvalidOperationException($"Category with name '{createCategoryDto.Name}' already exists.");

            var category = new Category
            {
                Name = createCategoryDto.Name
            };

            await _unitOfWork.Categories.AddAsync(category);
            await _unitOfWork.SaveChangesAsync();

            return new CategoryDto
            {
                Id = category.Id,
                Name = category.Name
            };
        }

        public async Task<CategoryDto> UpdateCategoryAsync(UpdateCategoryDto updateCategoryDto)
        {
            var category = await _unitOfWork.Categories.GetByIdAsync(updateCategoryDto.Id);
            if (category == null)
                throw new InvalidOperationException($"Category with ID {updateCategoryDto.Id} not found.");

            // Check if category name already exists (excluding current category)
            var existingCategory = await _unitOfWork.Categories.FirstOrDefaultAsync(c => c.Name == updateCategoryDto.Name && c.Id != updateCategoryDto.Id);
            if (existingCategory != null)
                throw new InvalidOperationException($"Category with name '{updateCategoryDto.Name}' already exists.");

            category.Name = updateCategoryDto.Name;
            _unitOfWork.Categories.Update(category);
            await _unitOfWork.SaveChangesAsync();

            return new CategoryDto
            {
                Id = category.Id,
                Name = category.Name
            };
        }

        public async Task<bool> DeleteCategoryAsync(int id)
        {
            var category = await _unitOfWork.Categories.GetByIdAsync(id);
            if (category == null)
                return false;

            // Check if category has products
            var hasProducts = await _unitOfWork.Products.AnyAsync(p => p.CategoryId == id);
            if (hasProducts)
                throw new InvalidOperationException("Cannot delete category that has products associated with it.");

            _unitOfWork.Categories.Remove(category);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<bool> CategoryExistsAsync(int id)
        {
            return await _unitOfWork.Categories.AnyAsync(c => c.Id == id);
        }

        public async Task<bool> CategoryNameExistsAsync(string name, int? excludeId = null)
        {
            if (excludeId.HasValue)
                return await _unitOfWork.Categories.AnyAsync(c => c.Name == name && c.Id != excludeId.Value);
            
            return await _unitOfWork.Categories.AnyAsync(c => c.Name == name);
        }
    }
}
