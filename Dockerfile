# Use the official .NET 8 runtime as base image
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

# Use the official .NET 8 SDK for building
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy project files
COPY ["ElectronicsStore.API/ElectronicsStore.API.csproj", "ElectronicsStore.API/"]
COPY ["ElectronicsStore.Application/ElectronicsStore.Application.csproj", "ElectronicsStore.Application/"]
COPY ["ElectronicsStore.Domain/ElectronicsStore.Domain.csproj", "ElectronicsStore.Domain/"]
COPY ["ElectronicsStore.Infrastructure/ElectronicsStore.Infrastructure.csproj", "ElectronicsStore.Infrastructure/"]
COPY ["ElectronicsStore.Persistence/ElectronicsStore.Persistence.csproj", "ElectronicsStore.Persistence/"]

# Restore dependencies
RUN dotnet restore "ElectronicsStore.API/ElectronicsStore.API.csproj"

# Copy all source code
COPY . .

# Build the application
WORKDIR "/src/ElectronicsStore.API"
RUN dotnet build "ElectronicsStore.API.csproj" -c Release -o /app/build

# Publish the application
FROM build AS publish
RUN dotnet publish "ElectronicsStore.API.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Final stage - runtime image
FROM base AS final
WORKDIR /app

# Create non-root user for security
RUN adduser --disabled-password --gecos '' appuser && chown -R appuser /app
USER appuser

# Copy published application
COPY --from=publish /app/publish .

# Set environment variables
ENV ASPNETCORE_ENVIRONMENT=Production
ENV ASPNETCORE_URLS=http://+:80

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:80/health || exit 1

# Entry point
ENTRYPOINT ["dotnet", "ElectronicsStore.API.dll"]
