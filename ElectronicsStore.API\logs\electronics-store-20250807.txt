2025-08-07 03:07:16.262 +03:00 [INF] Executed DbCommand (45ms) [Parameters=[@__role_Name_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[id], [r].[name]
FROM [roles] AS [r]
WHERE [r].[name] = @__role_Name_0
2025-08-07 03:07:16.406 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__role_Name_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[id], [r].[name]
FROM [roles] AS [r]
WHERE [r].[name] = @__role_Name_0
2025-08-07 03:07:16.415 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__role_Name_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[id], [r].[name]
FROM [roles] AS [r]
WHERE [r].[name] = @__role_Name_0
2025-08-07 03:07:16.469 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[id], [r].[name]
FROM [roles] AS [r]
WHERE [r].[name] = N'admin'
2025-08-07 03:07:16.481 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[id], [r].[name]
FROM [roles] AS [r]
WHERE [r].[name] = N'manager'
2025-08-07 03:07:16.494 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[id], [r].[name]
FROM [roles] AS [r]
WHERE [r].[name] = N'cashier'
2025-08-07 03:07:16.507 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__userData_Username_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[id], [u].[created_at], [u].[password], [u].[role_id], [u].[username]
FROM [users] AS [u]
WHERE [u].[username] = @__userData_Username_0
2025-08-07 03:07:16.563 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__userData_Username_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[id], [u].[created_at], [u].[password], [u].[role_id], [u].[username]
FROM [users] AS [u]
WHERE [u].[username] = @__userData_Username_0
2025-08-07 03:07:16.569 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__userData_Username_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[id], [u].[created_at], [u].[password], [u].[role_id], [u].[username]
FROM [users] AS [u]
WHERE [u].[username] = @__userData_Username_0
2025-08-07 03:07:16.595 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__category_Name_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[id], [c].[name]
FROM [categories] AS [c]
WHERE [c].[name] = @__category_Name_0
2025-08-07 03:07:16.609 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__category_Name_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[id], [c].[name]
FROM [categories] AS [c]
WHERE [c].[name] = @__category_Name_0
2025-08-07 03:07:16.616 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__category_Name_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[id], [c].[name]
FROM [categories] AS [c]
WHERE [c].[name] = @__category_Name_0
2025-08-07 03:07:16.621 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__category_Name_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[id], [c].[name]
FROM [categories] AS [c]
WHERE [c].[name] = @__category_Name_0
2025-08-07 03:07:16.627 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__category_Name_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[id], [c].[name]
FROM [categories] AS [c]
WHERE [c].[name] = @__category_Name_0
2025-08-07 03:07:16.666 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-07 03:07:16.765 +03:00 [INF] Now listening on: http://localhost:5226
2025-08-07 03:07:16.771 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-07 03:07:16.773 +03:00 [INF] Hosting environment: Development
2025-08-07 03:07:16.775 +03:00 [INF] Content root path: C:\Users\<USER>\OneDrive\Desktop\Final\first\ElectronicsStore.API
2025-08-07 03:07:47.986 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5226/index.html - null null
2025-08-07 03:07:48.210 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5226/index.html - 200 null text/html;charset=utf-8 227.9659ms
2025-08-07 03:07:50.233 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5226/swagger/v1/swagger.json - null null
2025-08-07 03:07:50.678 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5226/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 444.8437ms
2025-08-07 03:08:48.925 +03:00 [INF] Executed DbCommand (60ms) [Parameters=[@__role_Name_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[id], [r].[name]
FROM [roles] AS [r]
WHERE [r].[name] = @__role_Name_0
2025-08-07 03:08:49.087 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__role_Name_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[id], [r].[name]
FROM [roles] AS [r]
WHERE [r].[name] = @__role_Name_0
2025-08-07 03:08:49.095 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__role_Name_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[id], [r].[name]
FROM [roles] AS [r]
WHERE [r].[name] = @__role_Name_0
2025-08-07 03:08:49.147 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[id], [r].[name]
FROM [roles] AS [r]
WHERE [r].[name] = N'admin'
2025-08-07 03:08:49.161 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[id], [r].[name]
FROM [roles] AS [r]
WHERE [r].[name] = N'manager'
2025-08-07 03:08:49.174 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[id], [r].[name]
FROM [roles] AS [r]
WHERE [r].[name] = N'cashier'
2025-08-07 03:08:49.188 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__userData_Username_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[id], [u].[created_at], [u].[password], [u].[role_id], [u].[username]
FROM [users] AS [u]
WHERE [u].[username] = @__userData_Username_0
2025-08-07 03:08:49.238 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__userData_Username_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[id], [u].[created_at], [u].[password], [u].[role_id], [u].[username]
FROM [users] AS [u]
WHERE [u].[username] = @__userData_Username_0
2025-08-07 03:08:49.247 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__userData_Username_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[id], [u].[created_at], [u].[password], [u].[role_id], [u].[username]
FROM [users] AS [u]
WHERE [u].[username] = @__userData_Username_0
2025-08-07 03:08:49.276 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__category_Name_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[id], [c].[name]
FROM [categories] AS [c]
WHERE [c].[name] = @__category_Name_0
2025-08-07 03:08:49.292 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__category_Name_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[id], [c].[name]
FROM [categories] AS [c]
WHERE [c].[name] = @__category_Name_0
2025-08-07 03:08:49.299 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__category_Name_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[id], [c].[name]
FROM [categories] AS [c]
WHERE [c].[name] = @__category_Name_0
2025-08-07 03:08:49.305 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__category_Name_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[id], [c].[name]
FROM [categories] AS [c]
WHERE [c].[name] = @__category_Name_0
2025-08-07 03:08:49.310 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__category_Name_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[id], [c].[name]
FROM [categories] AS [c]
WHERE [c].[name] = @__category_Name_0
2025-08-07 03:08:49.340 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-07 03:08:49.451 +03:00 [INF] Now listening on: http://localhost:5226
2025-08-07 03:08:49.458 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-07 03:08:49.462 +03:00 [INF] Hosting environment: Development
2025-08-07 03:08:49.465 +03:00 [INF] Content root path: C:\Users\<USER>\OneDrive\Desktop\Final\first\ElectronicsStore.API
2025-08-07 03:10:30.150 +03:00 [INF] Executed DbCommand (56ms) [Parameters=[@__role_Name_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[id], [r].[name]
FROM [roles] AS [r]
WHERE [r].[name] = @__role_Name_0
2025-08-07 03:10:30.351 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__role_Name_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[id], [r].[name]
FROM [roles] AS [r]
WHERE [r].[name] = @__role_Name_0
2025-08-07 03:10:30.358 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__role_Name_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[id], [r].[name]
FROM [roles] AS [r]
WHERE [r].[name] = @__role_Name_0
2025-08-07 03:10:30.416 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[id], [r].[name]
FROM [roles] AS [r]
WHERE [r].[name] = N'admin'
2025-08-07 03:10:30.428 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[id], [r].[name]
FROM [roles] AS [r]
WHERE [r].[name] = N'manager'
2025-08-07 03:10:30.443 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[id], [r].[name]
FROM [roles] AS [r]
WHERE [r].[name] = N'cashier'
2025-08-07 03:10:30.459 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__userData_Username_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[id], [u].[created_at], [u].[password], [u].[role_id], [u].[username]
FROM [users] AS [u]
WHERE [u].[username] = @__userData_Username_0
2025-08-07 03:10:30.517 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__userData_Username_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[id], [u].[created_at], [u].[password], [u].[role_id], [u].[username]
FROM [users] AS [u]
WHERE [u].[username] = @__userData_Username_0
2025-08-07 03:10:30.524 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__userData_Username_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[id], [u].[created_at], [u].[password], [u].[role_id], [u].[username]
FROM [users] AS [u]
WHERE [u].[username] = @__userData_Username_0
2025-08-07 03:10:30.557 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__category_Name_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[id], [c].[name]
FROM [categories] AS [c]
WHERE [c].[name] = @__category_Name_0
2025-08-07 03:10:30.575 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__category_Name_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[id], [c].[name]
FROM [categories] AS [c]
WHERE [c].[name] = @__category_Name_0
2025-08-07 03:10:30.585 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__category_Name_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[id], [c].[name]
FROM [categories] AS [c]
WHERE [c].[name] = @__category_Name_0
2025-08-07 03:10:30.594 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__category_Name_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[id], [c].[name]
FROM [categories] AS [c]
WHERE [c].[name] = @__category_Name_0
2025-08-07 03:10:30.600 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__category_Name_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[id], [c].[name]
FROM [categories] AS [c]
WHERE [c].[name] = @__category_Name_0
2025-08-07 03:10:30.627 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-07 03:10:30.710 +03:00 [INF] Now listening on: http://localhost:5226
2025-08-07 03:10:30.717 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-07 03:10:30.721 +03:00 [INF] Hosting environment: Development
2025-08-07 03:10:30.723 +03:00 [INF] Content root path: C:\Users\<USER>\OneDrive\Desktop\Final\first\ElectronicsStore.API
2025-08-07 03:10:35.787 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5226/index.html - null null
2025-08-07 03:10:36.078 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5226/index.html - 200 null text/html;charset=utf-8 296.924ms
2025-08-07 03:10:38.831 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5226/swagger/v1/swagger.json - null null
2025-08-07 03:10:39.584 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5226/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 751.9406ms
2025-08-07 03:10:51.098 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5226/index.html - null null
2025-08-07 03:10:51.109 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5226/index.html - 200 null text/html;charset=utf-8 11.378ms
2025-08-07 03:10:53.193 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5226/swagger/v1/swagger.json - null null
2025-08-07 03:10:53.359 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5226/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 166.6119ms
2025-08-07 03:10:59.772 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5226/api/auth/login - application/json 45
2025-08-07 03:10:59.809 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-07 03:10:59.934 +03:00 [INF] Executing endpoint 'ElectronicsStore.API.Controllers.AuthController.Login (ElectronicsStore.API)'
2025-08-07 03:10:59.992 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[ElectronicsStore.Application.DTOs.LoginResponseDto]] Login(ElectronicsStore.Application.DTOs.LoginDto) on controller ElectronicsStore.API.Controllers.AuthController (ElectronicsStore.API).
2025-08-07 03:11:00.174 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__username_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[id], [u].[created_at], [u].[password], [u].[role_id], [u].[username]
FROM [users] AS [u]
WHERE [u].[username] = @__username_0
2025-08-07 03:11:01.078 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[id], [u].[created_at], [u].[password], [u].[role_id], [u].[username], [r].[id], [r].[name]
FROM [users] AS [u]
INNER JOIN [roles] AS [r] ON [u].[role_id] = [r].[id]
2025-08-07 03:11:01.162 +03:00 [INF] Executing OkObjectResult, writing value of type 'ElectronicsStore.Application.DTOs.LoginResponseDto'.
2025-08-07 03:11:01.196 +03:00 [INF] Executed action ElectronicsStore.API.Controllers.AuthController.Login (ElectronicsStore.API) in 1194.2696ms
2025-08-07 03:11:01.201 +03:00 [INF] Executed endpoint 'ElectronicsStore.API.Controllers.AuthController.Login (ElectronicsStore.API)'
2025-08-07 03:11:01.206 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5226/api/auth/login - 200 null application/json; charset=utf-8 1433.8398ms
2025-08-07 03:11:12.175 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5226/api/products - application/json 215
2025-08-07 03:11:12.244 +03:00 [INF] Executing endpoint 'ElectronicsStore.API.Controllers.ProductsController.CreateProduct (ElectronicsStore.API)'
2025-08-07 03:11:12.252 +03:00 [INF] Route matched with {action = "CreateProduct", controller = "Products"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[ElectronicsStore.Application.DTOs.ProductDto]] CreateProduct(ElectronicsStore.Application.DTOs.CreateProductDto) on controller ElectronicsStore.API.Controllers.ProductsController (ElectronicsStore.API).
2025-08-07 03:11:12.311 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[id], [c].[name]
FROM [categories] AS [c]
WHERE [c].[id] = @__p_0
2025-08-07 03:11:12.356 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__barcode_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [products] AS [p]
        WHERE [p].[barcode] = @__barcode_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-07 03:11:12.564 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@p0='?' (Size = 50), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Precision = 10) (Scale = 2) (DbType = Decimal), @p4='?' (Precision = 10) (Scale = 2) (DbType = Decimal), @p5='?' (Size = 500), @p6='?' (Precision = 10) (Scale = 2) (DbType = Decimal), @p7='?' (Size = 150), @p8='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [products] ([barcode], [category_id], [created_at], [default_cost_price], [default_selling_price], [description], [min_selling_price], [name], [supplier_id])
OUTPUT INSERTED.[id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8);
2025-08-07 03:11:12.649 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (Size = 20), @p2='?' (Size = 200), @p3='?' (DbType = Int32), @p4='?' (DbType = Int32), @p5='?' (DbType = Int32), @p6='?' (Size = 50), @p7='?' (Precision = 10) (Scale = 2) (DbType = Decimal), @p8='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [inventory_logs] ([created_at], [movement_type], [note], [product_id], [quantity], [reference_id], [reference_tbl], [unit_cost], [user_id])
OUTPUT INSERTED.[id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8);
2025-08-07 03:11:12.734 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[id], [p].[barcode], [p].[category_id], [p].[created_at], [p].[default_cost_price], [p].[default_selling_price], [p].[description], [p].[min_selling_price], [p].[name], [p].[supplier_id], [c].[id], [c].[name], [s].[id], [s].[address], [s].[email], [s].[name], [s].[phone]
FROM [products] AS [p]
INNER JOIN [categories] AS [c] ON [p].[category_id] = [c].[id]
LEFT JOIN [suppliers] AS [s] ON [p].[supplier_id] = [s].[id]
WHERE [p].[id] = 2
2025-08-07 03:11:12.757 +03:00 [INF] Executing CreatedAtActionResult, writing value of type 'ElectronicsStore.Application.DTOs.ProductDto'.
2025-08-07 03:11:12.816 +03:00 [INF] Executed action ElectronicsStore.API.Controllers.ProductsController.CreateProduct (ElectronicsStore.API) in 558.6182ms
2025-08-07 03:11:12.822 +03:00 [INF] Executed endpoint 'ElectronicsStore.API.Controllers.ProductsController.CreateProduct (ElectronicsStore.API)'
2025-08-07 03:11:12.826 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5226/api/products - 201 null application/json; charset=utf-8 651.2717ms
2025-08-07 03:11:22.689 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5226/api/products - null null
2025-08-07 03:11:22.707 +03:00 [INF] Executing endpoint 'ElectronicsStore.API.Controllers.ProductsController.GetProducts (ElectronicsStore.API)'
2025-08-07 03:11:22.719 +03:00 [INF] Route matched with {action = "GetProducts", controller = "Products"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[ElectronicsStore.Application.DTOs.ProductDto]]] GetProducts() on controller ElectronicsStore.API.Controllers.ProductsController (ElectronicsStore.API).
2025-08-07 03:11:22.757 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[id], [p].[barcode], [p].[category_id], [p].[created_at], [p].[default_cost_price], [p].[default_selling_price], [p].[description], [p].[min_selling_price], [p].[name], [p].[supplier_id], [c].[id], [c].[name], [s].[id], [s].[address], [s].[email], [s].[name], [s].[phone]
FROM [products] AS [p]
INNER JOIN [categories] AS [c] ON [p].[category_id] = [c].[id]
LEFT JOIN [suppliers] AS [s] ON [p].[supplier_id] = [s].[id]
2025-08-07 03:11:22.808 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__productId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT COALESCE(SUM([i].[quantity]), 0)
FROM [inventory_logs] AS [i]
WHERE [i].[product_id] = @__productId_0
2025-08-07 03:11:22.846 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__productId_Value_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT COALESCE(SUM(CAST([i].[quantity] AS decimal(10,2)) * [i].[unit_cost]), 0.0)
FROM [inventory_logs] AS [i]
WHERE [i].[product_id] = @__productId_Value_0 AND [i].[quantity] > 0
2025-08-07 03:11:22.861 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__productId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT COALESCE(SUM([i].[quantity]), 0)
FROM [inventory_logs] AS [i]
WHERE [i].[product_id] = @__productId_0
2025-08-07 03:11:22.874 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__productId_Value_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT COALESCE(SUM(CAST([i].[quantity] AS decimal(10,2)) * [i].[unit_cost]), 0.0)
FROM [inventory_logs] AS [i]
WHERE [i].[product_id] = @__productId_Value_0 AND [i].[quantity] > 0
2025-08-07 03:11:22.885 +03:00 [INF] Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[ElectronicsStore.Application.DTOs.ProductDto, ElectronicsStore.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-07 03:11:22.901 +03:00 [INF] Executed action ElectronicsStore.API.Controllers.ProductsController.GetProducts (ElectronicsStore.API) in 174.2591ms
2025-08-07 03:11:22.907 +03:00 [INF] Executed endpoint 'ElectronicsStore.API.Controllers.ProductsController.GetProducts (ElectronicsStore.API)'
2025-08-07 03:11:22.912 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5226/api/products - 200 null application/json; charset=utf-8 223.1281ms
2025-08-07 03:11:35.685 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5226/api/sales - application/json 274
2025-08-07 03:11:35.696 +03:00 [INF] Executing endpoint 'ElectronicsStore.API.Controllers.SalesController.CreateSalesInvoice (ElectronicsStore.API)'
2025-08-07 03:11:35.705 +03:00 [INF] Route matched with {action = "CreateSalesInvoice", controller = "Sales"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[ElectronicsStore.Application.DTOs.SalesInvoiceDto]] CreateSalesInvoice(ElectronicsStore.Application.DTOs.CreateSalesInvoiceDto) on controller ElectronicsStore.API.Controllers.SalesController (ElectronicsStore.API).
2025-08-07 03:11:35.758 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__invoiceNumber_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [sales_invoices] AS [s]
        WHERE [s].[invoice_number] = @__invoiceNumber_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-07 03:11:35.779 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[@__createSalesInvoiceDto_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [users] AS [u]
        WHERE [u].[id] = @__createSalesInvoiceDto_UserId_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-07 03:11:35.890 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[@p0='?' (Size = 100), @p1='?' (Precision = 12) (Scale = 2) (DbType = Decimal), @p2='?' (DbType = DateTime2), @p3='?' (Size = 50), @p4='?' (DbType = Int32), @p5='?' (DbType = DateTime2), @p6='?' (Size = 20), @p7='?' (Precision = 14) (Scale = 2) (DbType = Decimal), @p8='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [sales_invoices] ([customer_name], [discount_total], [invoice_date], [invoice_number], [override_by_user_id], [override_date], [payment_method], [total_amount], [user_id])
OUTPUT INSERTED.[id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8);
2025-08-07 03:11:35.926 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[id], [p].[barcode], [p].[category_id], [p].[created_at], [p].[default_cost_price], [p].[default_selling_price], [p].[description], [p].[min_selling_price], [p].[name], [p].[supplier_id]
FROM [products] AS [p]
WHERE [p].[id] = @__p_0
2025-08-07 03:11:35.938 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__productId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT COALESCE(SUM([i].[quantity]), 0)
FROM [inventory_logs] AS [i]
WHERE [i].[product_id] = @__productId_0
2025-08-07 03:11:35.985 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[id], [p].[barcode], [p].[category_id], [p].[created_at], [p].[default_cost_price], [p].[default_selling_price], [p].[description], [p].[min_selling_price], [p].[name], [p].[supplier_id]
FROM [products] AS [p]
WHERE [p].[id] = @__p_0
2025-08-07 03:11:35.992 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__productId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT COALESCE(SUM([i].[quantity]), 0)
FROM [inventory_logs] AS [i]
WHERE [i].[product_id] = @__productId_0
2025-08-07 03:11:36.055 +03:00 [ERR] Failed executing DbCommand (26ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (Size = 20), @p2='?' (Size = 200), @p3='?' (DbType = Int32), @p4='?' (DbType = Int32), @p5='?' (DbType = Int32), @p6='?' (Size = 50), @p7='?' (Precision = 10) (Scale = 2) (DbType = Decimal), @p8='?' (DbType = Int32), @p9='?' (DbType = DateTime2), @p10='?' (Size = 20), @p11='?' (Size = 200), @p12='?' (DbType = Int32), @p13='?' (DbType = Int32), @p14='?' (DbType = Int32), @p15='?' (Size = 50), @p16='?' (Precision = 10) (Scale = 2) (DbType = Decimal), @p17='?' (DbType = Int32), @p18='?' (Precision = 10) (Scale = 2) (DbType = Decimal), @p19='?' (DbType = Int32), @p20='?' (DbType = Int32), @p21='?' (DbType = Int32), @p22='?' (Precision = 10) (Scale = 2) (DbType = Decimal), @p23='?' (Precision = 10) (Scale = 2) (DbType = Decimal), @p24='?' (DbType = Int32), @p25='?' (DbType = Int32), @p26='?' (DbType = Int32), @p27='?' (Precision = 10) (Scale = 2) (DbType = Decimal), @p37='?' (DbType = Int32), @p28='?' (Size = 100), @p29='?' (Precision = 12) (Scale = 2) (DbType = Decimal), @p30='?' (DbType = DateTime2), @p31='?' (Size = 50), @p32='?' (DbType = Int32), @p33='?' (DbType = DateTime2), @p34='?' (Size = 20), @p35='?' (Precision = 14) (Scale = 2) (DbType = Decimal), @p36='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET NOCOUNT ON;
MERGE [inventory_logs] USING (
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, 0),
(@p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, 1)) AS i ([created_at], [movement_type], [note], [product_id], [quantity], [reference_id], [reference_tbl], [unit_cost], [user_id], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([created_at], [movement_type], [note], [product_id], [quantity], [reference_id], [reference_tbl], [unit_cost], [user_id])
VALUES (i.[created_at], i.[movement_type], i.[note], i.[product_id], i.[quantity], i.[reference_id], i.[reference_tbl], i.[unit_cost], i.[user_id])
OUTPUT INSERTED.[id], i._Position;
MERGE [sales_invoice_details] USING (
VALUES (@p18, @p19, @p20, @p21, @p22, 0),
(@p23, @p24, @p25, @p26, @p27, 1)) AS i ([discount_amount], [product_id], [quantity], [sales_invoice_id], [unit_price], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([discount_amount], [product_id], [quantity], [sales_invoice_id], [unit_price])
VALUES (i.[discount_amount], i.[product_id], i.[quantity], i.[sales_invoice_id], i.[unit_price])
OUTPUT INSERTED.[id], INSERTED.[line_total], i._Position;
UPDATE [sales_invoices] SET [customer_name] = @p28, [discount_total] = @p29, [invoice_date] = @p30, [invoice_number] = @p31, [override_by_user_id] = @p32, [override_date] = @p33, [payment_method] = @p34, [total_amount] = @p35, [user_id] = @p36
OUTPUT 1
WHERE [id] = @p37;
2025-08-07 03:11:36.113 +03:00 [ERR] An exception occurred in the database while saving changes for context type 'ElectronicsStore.Persistence.ElectronicsDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: Could not save changes because the target table has database triggers. Please configure your table accordingly, see https://aka.ms/efcore-docs-sqlserver-save-changes-and-output-clause for more information.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): The target table 'sales_invoice_details' of the DML statement cannot have any enabled triggers if the statement contains an OUTPUT clause without INTO clause.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
ClientConnectionId:54a33b22-c350-465a-84a6-314504308a6b
Error Number:334,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: Could not save changes because the target table has database triggers. Please configure your table accordingly, see https://aka.ms/efcore-docs-sqlserver-save-changes-and-output-clause for more information.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): The target table 'sales_invoice_details' of the DML statement cannot have any enabled triggers if the statement contains an OUTPUT clause without INTO clause.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
ClientConnectionId:54a33b22-c350-465a-84a6-314504308a6b
Error Number:334,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-08-07 03:11:36.150 +03:00 [INF] Executed action ElectronicsStore.API.Controllers.SalesController.CreateSalesInvoice (ElectronicsStore.API) in 437.9747ms
2025-08-07 03:11:36.154 +03:00 [INF] Executed endpoint 'ElectronicsStore.API.Controllers.SalesController.CreateSalesInvoice (ElectronicsStore.API)'
2025-08-07 03:11:36.159 +03:00 [ERR] An unhandled exception occurred. Request: POST /api/sales
Microsoft.EntityFrameworkCore.DbUpdateException: Could not save changes because the target table has database triggers. Please configure your table accordingly, see https://aka.ms/efcore-docs-sqlserver-save-changes-and-output-clause for more information.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): The target table 'sales_invoice_details' of the DML statement cannot have any enabled triggers if the statement contains an OUTPUT clause without INTO clause.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
ClientConnectionId:54a33b22-c350-465a-84a6-314504308a6b
Error Number:334,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at ElectronicsStore.Persistence.Repositories.UnitOfWork.SaveChangesAsync() in C:\Users\<USER>\OneDrive\Desktop\Final\first\ElectronicsStore.Persistence\Repositories\UnitOfWork.cs:line 101
   at ElectronicsStore.Application.Services.SalesService.CreateSalesInvoiceAsync(CreateSalesInvoiceDto createSalesInvoiceDto) in C:\Users\<USER>\OneDrive\Desktop\Final\first\ElectronicsStore.Application\Services\SalesService.cs:line 125
   at ElectronicsStore.Application.Services.SalesService.CreateSalesInvoiceAsync(CreateSalesInvoiceDto createSalesInvoiceDto) in C:\Users\<USER>\OneDrive\Desktop\Final\first\ElectronicsStore.Application\Services\SalesService.cs:line 135
   at ElectronicsStore.API.Controllers.SalesController.CreateSalesInvoice(CreateSalesInvoiceDto createSalesInvoiceDto) in C:\Users\<USER>\OneDrive\Desktop\Final\first\ElectronicsStore.API\Controllers\SalesController.cs:line 75
   at lambda_method695(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at ElectronicsStore.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\OneDrive\Desktop\Final\first\ElectronicsStore.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 22
2025-08-07 03:11:36.219 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5226/api/sales - 400 null application/json 534.1611ms
2025-08-07 03:12:01.123 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5226/api/inventory/stock/1 - null null
2025-08-07 03:12:01.130 +03:00 [INF] Executing endpoint 'ElectronicsStore.API.Controllers.InventoryController.GetCurrentStock (ElectronicsStore.API)'
2025-08-07 03:12:01.136 +03:00 [INF] Route matched with {action = "GetCurrentStock", controller = "Inventory"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Object]] GetCurrentStock(Int32) on controller ElectronicsStore.API.Controllers.InventoryController (ElectronicsStore.API).
2025-08-07 03:12:01.146 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__productId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT COALESCE(SUM([i].[quantity]), 0)
FROM [inventory_logs] AS [i]
WHERE [i].[product_id] = @__productId_0
2025-08-07 03:12:01.154 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType3`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-07 03:12:01.168 +03:00 [INF] Executed action ElectronicsStore.API.Controllers.InventoryController.GetCurrentStock (ElectronicsStore.API) in 27.2748ms
2025-08-07 03:12:01.174 +03:00 [INF] Executed endpoint 'ElectronicsStore.API.Controllers.InventoryController.GetCurrentStock (ElectronicsStore.API)'
2025-08-07 03:12:01.179 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5226/api/inventory/stock/1 - 200 null application/json; charset=utf-8 56.3006ms
2025-08-07 03:12:11.714 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5226/api/inventory/stock/summary - null null
2025-08-07 03:12:11.730 +03:00 [INF] Executing endpoint 'ElectronicsStore.API.Controllers.InventoryController.GetStockSummary (ElectronicsStore.API)'
2025-08-07 03:12:11.740 +03:00 [INF] Route matched with {action = "GetStockSummary", controller = "Inventory"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[ElectronicsStore.Application.DTOs.ProductStockDto]]] GetStockSummary() on controller ElectronicsStore.API.Controllers.InventoryController (ElectronicsStore.API).
2025-08-07 03:12:11.836 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [i].[product_id] AS [ProductId], [p].[name] AS [ProductName], COALESCE(SUM([i].[quantity]), 0) AS [CurrentStock], COALESCE(SUM(CASE
    WHEN [i].[quantity] > 0 THEN [i].[quantity]
END), 0) AS [TotalIn], COALESCE(SUM(CASE
    WHEN [i].[quantity] < 0 THEN ABS([i].[quantity])
END), 0) AS [TotalOut], MAX([i].[created_at]) AS [LastMovement]
FROM [inventory_logs] AS [i]
INNER JOIN [products] AS [p] ON [i].[product_id] = [p].[id]
GROUP BY [i].[product_id], [p].[name]
ORDER BY [p].[name]
2025-08-07 03:12:11.844 +03:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+SelectEnumerableIterator`2[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[ElectronicsStore.Application.DTOs.ProductStockDto, ElectronicsStore.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-07 03:12:11.909 +03:00 [INF] Executed action ElectronicsStore.API.Controllers.InventoryController.GetStockSummary (ElectronicsStore.API) in 160.4138ms
2025-08-07 03:12:11.915 +03:00 [INF] Executed endpoint 'ElectronicsStore.API.Controllers.InventoryController.GetStockSummary (ElectronicsStore.API)'
2025-08-07 03:12:11.918 +03:00 [ERR] An unhandled exception occurred. Request: GET /api/inventory/stock/summary
Microsoft.CSharp.RuntimeBinder.RuntimeBinderException: 'object' does not contain a definition for 'ProductId'
   at CallSite.Target(Closure, CallSite, Object)
   at System.Dynamic.UpdateDelegates.UpdateAndExecute1[T0,TRet](CallSite site, T0 arg0)
   at ElectronicsStore.Application.Services.InventoryService.<GetStockSummaryAsync>b__12_0(Object item) in C:\Users\<USER>\OneDrive\Desktop\Final\first\ElectronicsStore.Application\Services\InventoryService.cs:line 119
   at System.Linq.Enumerable.SelectEnumerableIterator`2.MoveNext()
   at System.Text.Json.Serialization.Converters.IEnumerableDefaultConverter`2.OnWriteResume(Utf8JsonWriter writer, TCollection value, JsonSerializerOptions options, WriteStack& state)
   at System.Text.Json.Serialization.JsonCollectionConverter`2.OnTryWrite(Utf8JsonWriter writer, TCollection value, JsonSerializerOptions options, WriteStack& state)
   at System.Text.Json.Serialization.JsonConverter`1.TryWrite(Utf8JsonWriter writer, T& value, JsonSerializerOptions options, WriteStack& state)
   at System.Text.Json.Serialization.JsonConverter`1.WriteCore(Utf8JsonWriter writer, T& value, JsonSerializerOptions options, WriteStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.SerializeAsync(PipeWriter pipeWriter, T rootValue, Int32 flushThreshold, CancellationToken cancellationToken, Object rootValueBoxed)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.SerializeAsync(PipeWriter pipeWriter, T rootValue, Int32 flushThreshold, CancellationToken cancellationToken, Object rootValueBoxed)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.SerializeAsync(PipeWriter pipeWriter, T rootValue, Int32 flushThreshold, CancellationToken cancellationToken, Object rootValueBoxed)
   at Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter.WriteResponseBodyAsync(OutputFormatterWriteContext context, Encoding selectedEncoding)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at ElectronicsStore.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\OneDrive\Desktop\Final\first\ElectronicsStore.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 22
2025-08-07 03:12:11.940 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5226/api/inventory/stock/summary - 500 null application/json 225.5906ms
2025-08-07 03:12:21.946 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5226/api/categories - null null
2025-08-07 03:12:21.957 +03:00 [INF] Executing endpoint 'ElectronicsStore.API.Controllers.CategoriesController.GetCategories (ElectronicsStore.API)'
2025-08-07 03:12:21.967 +03:00 [INF] Route matched with {action = "GetCategories", controller = "Categories"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[ElectronicsStore.Application.DTOs.CategoryDto]]] GetCategories() on controller ElectronicsStore.API.Controllers.CategoriesController (ElectronicsStore.API).
2025-08-07 03:12:21.991 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[id], [c].[name]
FROM [categories] AS [c]
2025-08-07 03:12:22.000 +03:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+SelectListIterator`2[[ElectronicsStore.Domain.Entities.Category, ElectronicsStore.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[ElectronicsStore.Application.DTOs.CategoryDto, ElectronicsStore.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-07 03:12:22.006 +03:00 [INF] Executed action ElectronicsStore.API.Controllers.CategoriesController.GetCategories (ElectronicsStore.API) in 30.6429ms
2025-08-07 03:12:22.009 +03:00 [INF] Executed endpoint 'ElectronicsStore.API.Controllers.CategoriesController.GetCategories (ElectronicsStore.API)'
2025-08-07 03:12:22.011 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5226/api/categories - 200 null application/json; charset=utf-8 64.9243ms
2025-08-07 03:12:34.103 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5226/api/categories - application/json 18
2025-08-07 03:12:34.112 +03:00 [INF] Executing endpoint 'ElectronicsStore.API.Controllers.CategoriesController.CreateCategory (ElectronicsStore.API)'
2025-08-07 03:12:34.119 +03:00 [INF] Route matched with {action = "CreateCategory", controller = "Categories"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[ElectronicsStore.Application.DTOs.CategoryDto]] CreateCategory(ElectronicsStore.Application.DTOs.CreateCategoryDto) on controller ElectronicsStore.API.Controllers.CategoriesController (ElectronicsStore.API).
2025-08-07 03:12:34.135 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__createCategoryDto_Name_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[id], [c].[name]
FROM [categories] AS [c]
WHERE [c].[name] = @__createCategoryDto_Name_0
2025-08-07 03:12:34.154 +03:00 [INF] Executed DbCommand (10ms) [Parameters=[@p0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [categories] ([name])
OUTPUT INSERTED.[id]
VALUES (@p0);
2025-08-07 03:12:34.163 +03:00 [INF] Executing CreatedAtActionResult, writing value of type 'ElectronicsStore.Application.DTOs.CategoryDto'.
2025-08-07 03:12:34.167 +03:00 [INF] Executed action ElectronicsStore.API.Controllers.CategoriesController.CreateCategory (ElectronicsStore.API) in 43.7879ms
2025-08-07 03:12:34.169 +03:00 [INF] Executed endpoint 'ElectronicsStore.API.Controllers.CategoriesController.CreateCategory (ElectronicsStore.API)'
2025-08-07 03:12:34.171 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5226/api/categories - 201 null application/json; charset=utf-8 67.7818ms
2025-08-07 03:21:12.054 +03:00 [INF] Executed DbCommand (41ms) [Parameters=[@__role_Name_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[id], [r].[name]
FROM [roles] AS [r]
WHERE [r].[name] = @__role_Name_0
2025-08-07 03:21:12.189 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__role_Name_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[id], [r].[name]
FROM [roles] AS [r]
WHERE [r].[name] = @__role_Name_0
2025-08-07 03:21:12.195 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__role_Name_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[id], [r].[name]
FROM [roles] AS [r]
WHERE [r].[name] = @__role_Name_0
2025-08-07 03:21:12.235 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[id], [r].[name]
FROM [roles] AS [r]
WHERE [r].[name] = N'admin'
2025-08-07 03:21:12.246 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[id], [r].[name]
FROM [roles] AS [r]
WHERE [r].[name] = N'manager'
2025-08-07 03:21:12.258 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[id], [r].[name]
FROM [roles] AS [r]
WHERE [r].[name] = N'cashier'
2025-08-07 03:21:12.274 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__userData_Username_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[id], [u].[created_at], [u].[password], [u].[role_id], [u].[username]
FROM [users] AS [u]
WHERE [u].[username] = @__userData_Username_0
2025-08-07 03:21:12.329 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__userData_Username_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[id], [u].[created_at], [u].[password], [u].[role_id], [u].[username]
FROM [users] AS [u]
WHERE [u].[username] = @__userData_Username_0
2025-08-07 03:21:12.336 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__userData_Username_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[id], [u].[created_at], [u].[password], [u].[role_id], [u].[username]
FROM [users] AS [u]
WHERE [u].[username] = @__userData_Username_0
2025-08-07 03:21:12.362 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__category_Name_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[id], [c].[name]
FROM [categories] AS [c]
WHERE [c].[name] = @__category_Name_0
2025-08-07 03:21:12.377 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__category_Name_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[id], [c].[name]
FROM [categories] AS [c]
WHERE [c].[name] = @__category_Name_0
2025-08-07 03:21:12.383 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__category_Name_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[id], [c].[name]
FROM [categories] AS [c]
WHERE [c].[name] = @__category_Name_0
2025-08-07 03:21:12.389 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__category_Name_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[id], [c].[name]
FROM [categories] AS [c]
WHERE [c].[name] = @__category_Name_0
2025-08-07 03:21:12.394 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__category_Name_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[id], [c].[name]
FROM [categories] AS [c]
WHERE [c].[name] = @__category_Name_0
2025-08-07 03:21:12.419 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-07 03:21:12.522 +03:00 [INF] Now listening on: http://localhost:5226
2025-08-07 03:21:12.532 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-07 03:21:12.537 +03:00 [INF] Hosting environment: Development
2025-08-07 03:21:12.541 +03:00 [INF] Content root path: C:\Users\<USER>\OneDrive\Desktop\Final\first\ElectronicsStore.API
2025-08-07 03:21:28.813 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5226/api/auth/login - application/json 45
2025-08-07 03:21:28.920 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-07 03:21:28.987 +03:00 [INF] Executing endpoint 'ElectronicsStore.API.Controllers.AuthController.Login (ElectronicsStore.API)'
2025-08-07 03:21:29.039 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[ElectronicsStore.Application.DTOs.LoginResponseDto]] Login(ElectronicsStore.Application.DTOs.LoginDto) on controller ElectronicsStore.API.Controllers.AuthController (ElectronicsStore.API).
2025-08-07 03:21:29.307 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__username_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[id], [u].[created_at], [u].[password], [u].[role_id], [u].[username]
FROM [users] AS [u]
WHERE [u].[username] = @__username_0
2025-08-07 03:21:29.954 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[id], [u].[created_at], [u].[password], [u].[role_id], [u].[username], [r].[id], [r].[name]
FROM [users] AS [u]
INNER JOIN [roles] AS [r] ON [u].[role_id] = [r].[id]
2025-08-07 03:21:30.010 +03:00 [INF] Executing OkObjectResult, writing value of type 'ElectronicsStore.Application.DTOs.LoginResponseDto'.
2025-08-07 03:21:30.063 +03:00 [INF] Executed action ElectronicsStore.API.Controllers.AuthController.Login (ElectronicsStore.API) in 1006.2257ms
2025-08-07 03:21:30.071 +03:00 [INF] Executed endpoint 'ElectronicsStore.API.Controllers.AuthController.Login (ElectronicsStore.API)'
2025-08-07 03:21:30.082 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5226/api/auth/login - 200 null application/json; charset=utf-8 1272.4451ms
2025-08-07 03:21:41.407 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5226/api/sales - application/json 274
2025-08-07 03:21:41.512 +03:00 [INF] Executing endpoint 'ElectronicsStore.API.Controllers.SalesController.CreateSalesInvoice (ElectronicsStore.API)'
2025-08-07 03:21:41.523 +03:00 [INF] Route matched with {action = "CreateSalesInvoice", controller = "Sales"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[ElectronicsStore.Application.DTOs.SalesInvoiceDto]] CreateSalesInvoice(ElectronicsStore.Application.DTOs.CreateSalesInvoiceDto) on controller ElectronicsStore.API.Controllers.SalesController (ElectronicsStore.API).
2025-08-07 03:21:41.629 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__invoiceNumber_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [sales_invoices] AS [s]
        WHERE [s].[invoice_number] = @__invoiceNumber_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-07 03:21:41.644 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__createSalesInvoiceDto_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [users] AS [u]
        WHERE [u].[id] = @__createSalesInvoiceDto_UserId_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-07 03:21:41.835 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@p0='?' (Size = 100), @p1='?' (Precision = 12) (Scale = 2) (DbType = Decimal), @p2='?' (DbType = DateTime2), @p3='?' (Size = 50), @p4='?' (DbType = Int32), @p5='?' (DbType = DateTime2), @p6='?' (Size = 20), @p7='?' (Precision = 14) (Scale = 2) (DbType = Decimal), @p8='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [sales_invoices] ([customer_name], [discount_total], [invoice_date], [invoice_number], [override_by_user_id], [override_date], [payment_method], [total_amount], [user_id])
OUTPUT INSERTED.[id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8);
2025-08-07 03:21:41.900 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[id], [p].[barcode], [p].[category_id], [p].[created_at], [p].[default_cost_price], [p].[default_selling_price], [p].[description], [p].[min_selling_price], [p].[name], [p].[supplier_id]
FROM [products] AS [p]
WHERE [p].[id] = @__p_0
2025-08-07 03:21:42.023 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__productId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT COALESCE(SUM([i].[quantity]), 0)
FROM [inventory_logs] AS [i]
WHERE [i].[product_id] = @__productId_0
2025-08-07 03:21:42.094 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[id], [p].[barcode], [p].[category_id], [p].[created_at], [p].[default_cost_price], [p].[default_selling_price], [p].[description], [p].[min_selling_price], [p].[name], [p].[supplier_id]
FROM [products] AS [p]
WHERE [p].[id] = @__p_0
2025-08-07 03:21:42.103 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__productId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT COALESCE(SUM([i].[quantity]), 0)
FROM [inventory_logs] AS [i]
WHERE [i].[product_id] = @__productId_0
2025-08-07 03:21:42.173 +03:00 [ERR] Failed executing DbCommand (27ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (Size = 20), @p2='?' (Size = 200), @p3='?' (DbType = Int32), @p4='?' (DbType = Int32), @p5='?' (DbType = Int32), @p6='?' (Size = 50), @p7='?' (Precision = 10) (Scale = 2) (DbType = Decimal), @p8='?' (DbType = Int32), @p9='?' (DbType = DateTime2), @p10='?' (Size = 20), @p11='?' (Size = 200), @p12='?' (DbType = Int32), @p13='?' (DbType = Int32), @p14='?' (DbType = Int32), @p15='?' (Size = 50), @p16='?' (Precision = 10) (Scale = 2) (DbType = Decimal), @p17='?' (DbType = Int32), @p18='?' (Precision = 10) (Scale = 2) (DbType = Decimal), @p19='?' (DbType = Int32), @p20='?' (DbType = Int32), @p21='?' (DbType = Int32), @p22='?' (Precision = 10) (Scale = 2) (DbType = Decimal), @p23='?' (Precision = 10) (Scale = 2) (DbType = Decimal), @p24='?' (DbType = Int32), @p25='?' (DbType = Int32), @p26='?' (DbType = Int32), @p27='?' (Precision = 10) (Scale = 2) (DbType = Decimal), @p37='?' (DbType = Int32), @p28='?' (Size = 100), @p29='?' (Precision = 12) (Scale = 2) (DbType = Decimal), @p30='?' (DbType = DateTime2), @p31='?' (Size = 50), @p32='?' (DbType = Int32), @p33='?' (DbType = DateTime2), @p34='?' (Size = 20), @p35='?' (Precision = 14) (Scale = 2) (DbType = Decimal), @p36='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET NOCOUNT ON;
MERGE [inventory_logs] USING (
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, 0),
(@p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, 1)) AS i ([created_at], [movement_type], [note], [product_id], [quantity], [reference_id], [reference_tbl], [unit_cost], [user_id], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([created_at], [movement_type], [note], [product_id], [quantity], [reference_id], [reference_tbl], [unit_cost], [user_id])
VALUES (i.[created_at], i.[movement_type], i.[note], i.[product_id], i.[quantity], i.[reference_id], i.[reference_tbl], i.[unit_cost], i.[user_id])
OUTPUT INSERTED.[id], i._Position;
MERGE [sales_invoice_details] USING (
VALUES (@p18, @p19, @p20, @p21, @p22, 0),
(@p23, @p24, @p25, @p26, @p27, 1)) AS i ([discount_amount], [product_id], [quantity], [sales_invoice_id], [unit_price], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([discount_amount], [product_id], [quantity], [sales_invoice_id], [unit_price])
VALUES (i.[discount_amount], i.[product_id], i.[quantity], i.[sales_invoice_id], i.[unit_price])
OUTPUT INSERTED.[id], INSERTED.[line_total], i._Position;
UPDATE [sales_invoices] SET [customer_name] = @p28, [discount_total] = @p29, [invoice_date] = @p30, [invoice_number] = @p31, [override_by_user_id] = @p32, [override_date] = @p33, [payment_method] = @p34, [total_amount] = @p35, [user_id] = @p36
OUTPUT 1
WHERE [id] = @p37;
2025-08-07 03:21:42.231 +03:00 [ERR] An exception occurred in the database while saving changes for context type 'ElectronicsStore.Persistence.ElectronicsDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: Could not save changes because the target table has database triggers. Please configure your table accordingly, see https://aka.ms/efcore-docs-sqlserver-save-changes-and-output-clause for more information.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): The target table 'sales_invoice_details' of the DML statement cannot have any enabled triggers if the statement contains an OUTPUT clause without INTO clause.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
ClientConnectionId:77a05dd4-75ba-400f-b65f-91657ecc0bd8
Error Number:334,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: Could not save changes because the target table has database triggers. Please configure your table accordingly, see https://aka.ms/efcore-docs-sqlserver-save-changes-and-output-clause for more information.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): The target table 'sales_invoice_details' of the DML statement cannot have any enabled triggers if the statement contains an OUTPUT clause without INTO clause.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
ClientConnectionId:77a05dd4-75ba-400f-b65f-91657ecc0bd8
Error Number:334,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-08-07 03:21:42.302 +03:00 [INF] Executed action ElectronicsStore.API.Controllers.SalesController.CreateSalesInvoice (ElectronicsStore.API) in 771.0494ms
2025-08-07 03:21:42.311 +03:00 [INF] Executed endpoint 'ElectronicsStore.API.Controllers.SalesController.CreateSalesInvoice (ElectronicsStore.API)'
2025-08-07 03:21:42.320 +03:00 [ERR] An unhandled exception occurred. Request: POST /api/sales
Microsoft.EntityFrameworkCore.DbUpdateException: Could not save changes because the target table has database triggers. Please configure your table accordingly, see https://aka.ms/efcore-docs-sqlserver-save-changes-and-output-clause for more information.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): The target table 'sales_invoice_details' of the DML statement cannot have any enabled triggers if the statement contains an OUTPUT clause without INTO clause.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
ClientConnectionId:77a05dd4-75ba-400f-b65f-91657ecc0bd8
Error Number:334,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at ElectronicsStore.Persistence.Repositories.UnitOfWork.SaveChangesAsync() in C:\Users\<USER>\OneDrive\Desktop\Final\first\ElectronicsStore.Persistence\Repositories\UnitOfWork.cs:line 101
   at ElectronicsStore.Application.Services.SalesService.CreateSalesInvoiceAsync(CreateSalesInvoiceDto createSalesInvoiceDto) in C:\Users\<USER>\OneDrive\Desktop\Final\first\ElectronicsStore.Application\Services\SalesService.cs:line 128
   at ElectronicsStore.Application.Services.SalesService.CreateSalesInvoiceAsync(CreateSalesInvoiceDto createSalesInvoiceDto) in C:\Users\<USER>\OneDrive\Desktop\Final\first\ElectronicsStore.Application\Services\SalesService.cs:line 138
   at ElectronicsStore.API.Controllers.SalesController.CreateSalesInvoice(CreateSalesInvoiceDto createSalesInvoiceDto) in C:\Users\<USER>\OneDrive\Desktop\Final\first\ElectronicsStore.API\Controllers\SalesController.cs:line 75
   at lambda_method313(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at ElectronicsStore.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\OneDrive\Desktop\Final\first\ElectronicsStore.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 22
2025-08-07 03:21:42.412 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5226/api/sales - 400 null application/json 1004.6348ms
2025-08-07 03:21:54.103 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5226/index.html - null null
2025-08-07 03:21:54.194 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5226/index.html - 200 null text/html;charset=utf-8 91.0576ms
2025-08-07 03:21:56.056 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5226/swagger/v1/swagger.json - null null
2025-08-07 03:21:56.347 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5226/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 290.7818ms
2025-08-07 03:22:55.987 +03:00 [INF] Executed DbCommand (71ms) [Parameters=[@__role_Name_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[id], [r].[name]
FROM [roles] AS [r]
WHERE [r].[name] = @__role_Name_0
2025-08-07 03:22:56.210 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__role_Name_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[id], [r].[name]
FROM [roles] AS [r]
WHERE [r].[name] = @__role_Name_0
2025-08-07 03:22:56.217 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__role_Name_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[id], [r].[name]
FROM [roles] AS [r]
WHERE [r].[name] = @__role_Name_0
2025-08-07 03:22:56.260 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[id], [r].[name]
FROM [roles] AS [r]
WHERE [r].[name] = N'admin'
2025-08-07 03:22:56.273 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[id], [r].[name]
FROM [roles] AS [r]
WHERE [r].[name] = N'manager'
2025-08-07 03:22:56.284 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[id], [r].[name]
FROM [roles] AS [r]
WHERE [r].[name] = N'cashier'
2025-08-07 03:22:56.299 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__userData_Username_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[id], [u].[created_at], [u].[password], [u].[role_id], [u].[username]
FROM [users] AS [u]
WHERE [u].[username] = @__userData_Username_0
2025-08-07 03:22:56.350 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__userData_Username_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[id], [u].[created_at], [u].[password], [u].[role_id], [u].[username]
FROM [users] AS [u]
WHERE [u].[username] = @__userData_Username_0
2025-08-07 03:22:56.360 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__userData_Username_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[id], [u].[created_at], [u].[password], [u].[role_id], [u].[username]
FROM [users] AS [u]
WHERE [u].[username] = @__userData_Username_0
2025-08-07 03:22:56.389 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__category_Name_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[id], [c].[name]
FROM [categories] AS [c]
WHERE [c].[name] = @__category_Name_0
2025-08-07 03:22:56.404 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__category_Name_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[id], [c].[name]
FROM [categories] AS [c]
WHERE [c].[name] = @__category_Name_0
2025-08-07 03:22:56.409 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__category_Name_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[id], [c].[name]
FROM [categories] AS [c]
WHERE [c].[name] = @__category_Name_0
2025-08-07 03:22:56.415 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__category_Name_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[id], [c].[name]
FROM [categories] AS [c]
WHERE [c].[name] = @__category_Name_0
2025-08-07 03:22:56.422 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__category_Name_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[id], [c].[name]
FROM [categories] AS [c]
WHERE [c].[name] = @__category_Name_0
2025-08-07 03:22:56.451 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-07 03:22:56.583 +03:00 [INF] Now listening on: http://localhost:5226
2025-08-07 03:22:56.592 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-07 03:22:56.596 +03:00 [INF] Hosting environment: Development
2025-08-07 03:22:56.598 +03:00 [INF] Content root path: C:\Users\<USER>\OneDrive\Desktop\Final\first\ElectronicsStore.API
2025-08-07 03:23:10.742 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5226/api/auth/login - application/json 45
2025-08-07 03:23:10.842 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-07 03:23:10.881 +03:00 [INF] Executing endpoint 'ElectronicsStore.API.Controllers.AuthController.Login (ElectronicsStore.API)'
2025-08-07 03:23:10.910 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[ElectronicsStore.Application.DTOs.LoginResponseDto]] Login(ElectronicsStore.Application.DTOs.LoginDto) on controller ElectronicsStore.API.Controllers.AuthController (ElectronicsStore.API).
2025-08-07 03:23:11.183 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__username_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[id], [u].[created_at], [u].[password], [u].[role_id], [u].[username]
FROM [users] AS [u]
WHERE [u].[username] = @__username_0
2025-08-07 03:23:12.193 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[id], [u].[created_at], [u].[password], [u].[role_id], [u].[username], [r].[id], [r].[name]
FROM [users] AS [u]
INNER JOIN [roles] AS [r] ON [u].[role_id] = [r].[id]
2025-08-07 03:23:12.273 +03:00 [INF] Executing OkObjectResult, writing value of type 'ElectronicsStore.Application.DTOs.LoginResponseDto'.
2025-08-07 03:23:12.337 +03:00 [INF] Executed action ElectronicsStore.API.Controllers.AuthController.Login (ElectronicsStore.API) in 1415.4883ms
2025-08-07 03:23:12.346 +03:00 [INF] Executed endpoint 'ElectronicsStore.API.Controllers.AuthController.Login (ElectronicsStore.API)'
2025-08-07 03:23:12.375 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5226/api/auth/login - 200 null application/json; charset=utf-8 1636.074ms
2025-08-07 03:23:22.727 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5226/api/sales - application/json 200
2025-08-07 03:23:22.859 +03:00 [INF] Executing endpoint 'ElectronicsStore.API.Controllers.SalesController.CreateSalesInvoice (ElectronicsStore.API)'
2025-08-07 03:23:22.871 +03:00 [INF] Route matched with {action = "CreateSalesInvoice", controller = "Sales"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[ElectronicsStore.Application.DTOs.SalesInvoiceDto]] CreateSalesInvoice(ElectronicsStore.Application.DTOs.CreateSalesInvoiceDto) on controller ElectronicsStore.API.Controllers.SalesController (ElectronicsStore.API).
2025-08-07 03:23:23.026 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__invoiceNumber_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [sales_invoices] AS [s]
        WHERE [s].[invoice_number] = @__invoiceNumber_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-07 03:23:23.042 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__createSalesInvoiceDto_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [users] AS [u]
        WHERE [u].[id] = @__createSalesInvoiceDto_UserId_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-07 03:23:23.321 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@p0='?' (Size = 100), @p1='?' (Precision = 12) (Scale = 2) (DbType = Decimal), @p2='?' (DbType = DateTime2), @p3='?' (Size = 50), @p4='?' (DbType = Int32), @p5='?' (DbType = DateTime2), @p6='?' (Size = 20), @p7='?' (Precision = 14) (Scale = 2) (DbType = Decimal), @p8='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [sales_invoices] ([customer_name], [discount_total], [invoice_date], [invoice_number], [override_by_user_id], [override_date], [payment_method], [total_amount], [user_id])
OUTPUT INSERTED.[id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8);
2025-08-07 03:23:23.411 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[id], [p].[barcode], [p].[category_id], [p].[created_at], [p].[default_cost_price], [p].[default_selling_price], [p].[description], [p].[min_selling_price], [p].[name], [p].[supplier_id]
FROM [products] AS [p]
WHERE [p].[id] = @__p_0
2025-08-07 03:23:23.511 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__productId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT COALESCE(SUM([i].[quantity]), 0)
FROM [inventory_logs] AS [i]
WHERE [i].[product_id] = @__productId_0
2025-08-07 03:23:23.624 +03:00 [ERR] Failed executing DbCommand (8ms) [Parameters=[@p0='?' (Precision = 10) (Scale = 2) (DbType = Decimal), @p1='?' (DbType = Int32), @p2='?' (DbType = Int32), @p3='?' (DbType = Int32), @p4='?' (Precision = 10) (Scale = 2) (DbType = Decimal)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [sales_invoice_details] ([discount_amount], [product_id], [quantity], [sales_invoice_id], [unit_price])
OUTPUT INSERTED.[id], INSERTED.[line_total]
VALUES (@p0, @p1, @p2, @p3, @p4);
2025-08-07 03:23:23.676 +03:00 [ERR] An exception occurred in the database while saving changes for context type 'ElectronicsStore.Persistence.ElectronicsDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: Could not save changes because the target table has database triggers. Please configure your table accordingly, see https://aka.ms/efcore-docs-sqlserver-save-changes-and-output-clause for more information.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): The target table 'sales_invoice_details' of the DML statement cannot have any enabled triggers if the statement contains an OUTPUT clause without INTO clause.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
ClientConnectionId:9ba76623-4e88-4129-a590-8e73d3ceab61
Error Number:334,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: Could not save changes because the target table has database triggers. Please configure your table accordingly, see https://aka.ms/efcore-docs-sqlserver-save-changes-and-output-clause for more information.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): The target table 'sales_invoice_details' of the DML statement cannot have any enabled triggers if the statement contains an OUTPUT clause without INTO clause.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
ClientConnectionId:9ba76623-4e88-4129-a590-8e73d3ceab61
Error Number:334,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-08-07 03:23:23.728 +03:00 [INF] Executed action ElectronicsStore.API.Controllers.SalesController.CreateSalesInvoice (ElectronicsStore.API) in 849.9369ms
2025-08-07 03:23:23.732 +03:00 [INF] Executed endpoint 'ElectronicsStore.API.Controllers.SalesController.CreateSalesInvoice (ElectronicsStore.API)'
2025-08-07 03:23:23.738 +03:00 [ERR] An unhandled exception occurred. Request: POST /api/sales
Microsoft.EntityFrameworkCore.DbUpdateException: Could not save changes because the target table has database triggers. Please configure your table accordingly, see https://aka.ms/efcore-docs-sqlserver-save-changes-and-output-clause for more information.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): The target table 'sales_invoice_details' of the DML statement cannot have any enabled triggers if the statement contains an OUTPUT clause without INTO clause.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
ClientConnectionId:9ba76623-4e88-4129-a590-8e73d3ceab61
Error Number:334,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at ElectronicsStore.Persistence.Repositories.UnitOfWork.SaveChangesAsync() in C:\Users\<USER>\OneDrive\Desktop\Final\first\ElectronicsStore.Persistence\Repositories\UnitOfWork.cs:line 101
   at ElectronicsStore.Application.Services.SalesService.CreateSalesInvoiceAsync(CreateSalesInvoiceDto createSalesInvoiceDto) in C:\Users\<USER>\OneDrive\Desktop\Final\first\ElectronicsStore.Application\Services\SalesService.cs:line 103
   at ElectronicsStore.Application.Services.SalesService.CreateSalesInvoiceAsync(CreateSalesInvoiceDto createSalesInvoiceDto) in C:\Users\<USER>\OneDrive\Desktop\Final\first\ElectronicsStore.Application\Services\SalesService.cs:line 140
   at ElectronicsStore.API.Controllers.SalesController.CreateSalesInvoice(CreateSalesInvoiceDto createSalesInvoiceDto) in C:\Users\<USER>\OneDrive\Desktop\Final\first\ElectronicsStore.API\Controllers\SalesController.cs:line 75
   at lambda_method313(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at ElectronicsStore.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\OneDrive\Desktop\Final\first\ElectronicsStore.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 22
2025-08-07 03:23:23.795 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5226/api/sales - 400 null application/json 1067.6501ms
2025-08-07 03:23:27.689 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5226/index.html - null null
2025-08-07 03:23:27.807 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5226/index.html - 200 null text/html;charset=utf-8 118.2236ms
2025-08-07 03:23:28.257 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5226/swagger/v1/swagger.json - null null
2025-08-07 03:23:28.715 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5226/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 458.2851ms
2025-08-07 03:23:45.368 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5226/api/inventory/stock/summary - null null
2025-08-07 03:23:45.400 +03:00 [INF] Executing endpoint 'ElectronicsStore.API.Controllers.InventoryController.GetStockSummary (ElectronicsStore.API)'
2025-08-07 03:23:45.417 +03:00 [INF] Route matched with {action = "GetStockSummary", controller = "Inventory"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[ElectronicsStore.Application.DTOs.ProductStockDto]]] GetStockSummary() on controller ElectronicsStore.API.Controllers.InventoryController (ElectronicsStore.API).
2025-08-07 03:23:45.469 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[id], [p].[barcode], [p].[category_id], [p].[created_at], [p].[default_cost_price], [p].[default_selling_price], [p].[description], [p].[min_selling_price], [p].[name], [p].[supplier_id]
FROM [products] AS [p]
2025-08-07 03:23:45.486 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__productId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT COALESCE(SUM([i].[quantity]), 0)
FROM [inventory_logs] AS [i]
WHERE [i].[product_id] = @__productId_0
2025-08-07 03:23:45.588 +03:00 [INF] Executed DbCommand (21ms) [Parameters=[@__productId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [i].[id], [i].[created_at], [i].[movement_type], [i].[note], [i].[product_id], [i].[quantity], [i].[reference_id], [i].[reference_tbl], [i].[unit_cost], [i].[user_id], [p].[id], [p].[barcode], [p].[category_id], [p].[created_at], [p].[default_cost_price], [p].[default_selling_price], [p].[description], [p].[min_selling_price], [p].[name], [p].[supplier_id], [u].[id], [u].[created_at], [u].[password], [u].[role_id], [u].[username]
FROM [inventory_logs] AS [i]
INNER JOIN [products] AS [p] ON [i].[product_id] = [p].[id]
INNER JOIN [users] AS [u] ON [i].[user_id] = [u].[id]
WHERE [i].[product_id] = @__productId_0
ORDER BY [i].[created_at] DESC
2025-08-07 03:23:45.661 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__productId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT COALESCE(SUM([i].[quantity]), 0)
FROM [inventory_logs] AS [i]
WHERE [i].[product_id] = @__productId_0
2025-08-07 03:23:45.674 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__productId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [i].[id], [i].[created_at], [i].[movement_type], [i].[note], [i].[product_id], [i].[quantity], [i].[reference_id], [i].[reference_tbl], [i].[unit_cost], [i].[user_id], [p].[id], [p].[barcode], [p].[category_id], [p].[created_at], [p].[default_cost_price], [p].[default_selling_price], [p].[description], [p].[min_selling_price], [p].[name], [p].[supplier_id], [u].[id], [u].[created_at], [u].[password], [u].[role_id], [u].[username]
FROM [inventory_logs] AS [i]
INNER JOIN [products] AS [p] ON [i].[product_id] = [p].[id]
INNER JOIN [users] AS [u] ON [i].[user_id] = [u].[id]
WHERE [i].[product_id] = @__productId_0
ORDER BY [i].[created_at] DESC
2025-08-07 03:23:45.690 +03:00 [INF] Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[ElectronicsStore.Application.DTOs.ProductStockDto, ElectronicsStore.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-08-07 03:23:45.731 +03:00 [INF] Executed action ElectronicsStore.API.Controllers.InventoryController.GetStockSummary (ElectronicsStore.API) in 304.1736ms
2025-08-07 03:23:45.739 +03:00 [INF] Executed endpoint 'ElectronicsStore.API.Controllers.InventoryController.GetStockSummary (ElectronicsStore.API)'
2025-08-07 03:23:45.746 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5226/api/inventory/stock/summary - 200 null application/json; charset=utf-8 377.3837ms
