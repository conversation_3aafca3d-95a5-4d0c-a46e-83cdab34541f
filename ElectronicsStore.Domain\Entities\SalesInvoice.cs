using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ElectronicsStore.Domain.Entities
{
    [Table("sales_invoices")]
    public class SalesInvoice
    {
        [Key]
        [Column("id")]
        public int Id { get; set; }

        [Required]
        [MaxLength(50)]
        [Column("invoice_number")]
        public string InvoiceNumber { get; set; } = string.Empty;

        [MaxLength(100)]
        [Column("customer_name")]
        public string? CustomerName { get; set; }

        [Column("invoice_date")]
        public DateTime InvoiceDate { get; set; } = DateTime.Now;

        [Column("discount_total", TypeName = "decimal(12,2)")]
        public decimal DiscountTotal { get; set; } = 0;

        [Column("total_amount", TypeName = "decimal(14,2)")]
        public decimal TotalAmount { get; set; }

        [Required]
        [MaxLength(20)]
        [Column("payment_method")]
        public string PaymentMethod { get; set; } = string.Empty; // cash, card, deferred

        [Column("override_by_user_id")]
        public int? OverrideByUserId { get; set; }

        [Column("override_date")]
        public DateTime? OverrideDate { get; set; }

        [Column("user_id")]
        public int UserId { get; set; }

        // Navigation properties
        [ForeignKey("OverrideByUserId")]
        public virtual User? OverrideByUser { get; set; }

        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        public virtual ICollection<SalesInvoiceDetail> SalesInvoiceDetails { get; set; } = new List<SalesInvoiceDetail>();
        public virtual ICollection<SalesReturn> SalesReturns { get; set; } = new List<SalesReturn>();
    }
}
