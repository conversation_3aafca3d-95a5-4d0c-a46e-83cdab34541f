using Microsoft.AspNetCore.Mvc;
using ElectronicsStore.API.Middleware;
using ElectronicsStore.Application.Common;
using ElectronicsStore.API.Attributes;

namespace ElectronicsStore.API.Controllers
{
    /// <summary>
    /// Controller for monitoring system performance and metrics
    /// </summary>
    [Route("api/[controller]")]
    public class PerformanceController : BaseController
    {
        private readonly PerformanceMetricsCollector _metricsCollector;

        public PerformanceController(PerformanceMetricsCollector metricsCollector)
        {
            _metricsCollector = metricsCollector;
        }

        /// <summary>
        /// Get system performance statistics
        /// </summary>
        /// <returns>Performance statistics</returns>
        [HttpGet("statistics")]
        [BusinessAuthorize.SystemAdmin]
        public ActionResult<ApiResponse<PerformanceStatistics>> GetPerformanceStatistics()
        {
            var statistics = _metricsCollector.GetStatistics();
            return CreateSuccessResponse(statistics, "Performance statistics retrieved successfully.");
        }

        /// <summary>
        /// Get recent requests for monitoring
        /// </summary>
        /// <param name="count">Number of recent requests to retrieve (max 500)</param>
        /// <returns>List of recent requests</returns>
        [HttpGet("recent-requests")]
        [BusinessAuthorize.SystemAdmin]
        public ActionResult<ApiResponse<IEnumerable<RequestMetrics>>> GetRecentRequests([FromQuery] int count = 100)
        {
            if (count <= 0 || count > 500)
                return CreateErrorResponse<IEnumerable<RequestMetrics>>("Count must be between 1 and 500.");

            var recentRequests = _metricsCollector.GetRecentRequests(count);
            return CreateSuccessResponse<IEnumerable<RequestMetrics>>(recentRequests, $"Retrieved {recentRequests.Count} recent requests.");
        }

        /// <summary>
        /// Get system health status
        /// </summary>
        /// <returns>System health information</returns>
        [HttpGet("health")]
        [BusinessAuthorize.Analytics]
        public ActionResult<ApiResponse<object>> GetSystemHealth()
        {
            var statistics = _metricsCollector.GetStatistics();
            
            // Determine system health based on metrics
            var health = DetermineSystemHealth(statistics);
            
            var healthInfo = new
            {
                Status = health.Status,
                Message = health.Message,
                Timestamp = DateTime.UtcNow,
                Metrics = new
                {
                    RequestsLast5Minutes = statistics.RequestsLast5Minutes,
                    AverageResponseTime = Math.Round(statistics.AverageResponseTimeLast5Minutes, 2),
                    ErrorRate = statistics.TotalRequests > 0
                        ? Math.Round((double)statistics.ErrorRequestsCount / statistics.TotalRequests * 100, 2)
                        : 0,
                    SlowRequestsCount = statistics.SlowRequestsCount
                },
                Recommendations = health.Recommendations
            };

            return CreateSuccessResponse<object>(healthInfo, "System health status retrieved.");
        }

        /// <summary>
        /// Get performance trends over time
        /// </summary>
        /// <returns>Performance trends</returns>
        [HttpGet("trends")]
        [BusinessAuthorize.Analytics]
        public ActionResult<ApiResponse<object>> GetPerformanceTrends()
        {
            var recentRequests = _metricsCollector.GetRecentRequests(1000);
            var now = DateTime.UtcNow;

            // Group requests by time intervals
            var hourlyTrends = recentRequests
                .Where(r => (now - r.RequestTime).TotalHours <= 24)
                .GroupBy(r => new DateTime(r.RequestTime.Year, r.RequestTime.Month, r.RequestTime.Day, r.RequestTime.Hour, 0, 0))
                .Select(g => new
                {
                    Hour = g.Key,
                    RequestCount = g.Count(),
                    AverageResponseTime = Math.Round(g.Average(r => r.ElapsedMilliseconds), 2),
                    ErrorCount = g.Count(r => r.StatusCode >= 400),
                    SlowRequestCount = g.Count(r => r.ElapsedMilliseconds > 1000)
                })
                .OrderBy(t => t.Hour)
                .ToList();

            var trends = new
            {
                HourlyTrends = hourlyTrends,
                Summary = new
                {
                    TotalHours = hourlyTrends.Count,
                    PeakHour = hourlyTrends.OrderByDescending(h => h.RequestCount).FirstOrDefault(),
                    SlowestHour = hourlyTrends.OrderByDescending(h => h.AverageResponseTime).FirstOrDefault(),
                    MostErrorsHour = hourlyTrends.OrderByDescending(h => h.ErrorCount).FirstOrDefault()
                }
            };

            return CreateSuccessResponse<object>(trends, "Performance trends retrieved successfully.");
        }

        /// <summary>
        /// Get endpoint performance analysis
        /// </summary>
        /// <returns>Endpoint performance data</returns>
        [HttpGet("endpoints")]
        [BusinessAuthorize.Analytics]
        public ActionResult<ApiResponse<object>> GetEndpointPerformance()
        {
            var recentRequests = _metricsCollector.GetRecentRequests(1000);
            
            var endpointAnalysis = recentRequests
                .GroupBy(r => $"{r.Method} {r.Path}")
                .Select(g => new
                {
                    Endpoint = g.Key,
                    RequestCount = g.Count(),
                    AverageResponseTime = Math.Round(g.Average(r => r.ElapsedMilliseconds), 2),
                    MinResponseTime = g.Min(r => r.ElapsedMilliseconds),
                    MaxResponseTime = g.Max(r => r.ElapsedMilliseconds),
                    ErrorCount = g.Count(r => r.StatusCode >= 400),
                    ErrorRate = Math.Round((double)g.Count(r => r.StatusCode >= 400) / g.Count() * 100, 2),
                    SlowRequestCount = g.Count(r => r.ElapsedMilliseconds > 1000),
                    UniqueUsers = g.Where(r => r.UserId.HasValue).Select(r => r.UserId).Distinct().Count()
                })
                .OrderByDescending(e => e.RequestCount)
                .ToList();

            var analysis = new
            {
                Endpoints = endpointAnalysis,
                Summary = new
                {
                    TotalEndpoints = endpointAnalysis.Count,
                    MostUsedEndpoint = endpointAnalysis.FirstOrDefault(),
                    SlowestEndpoint = endpointAnalysis.OrderByDescending(e => e.AverageResponseTime).FirstOrDefault(),
                    MostErrorsEndpoint = endpointAnalysis.OrderByDescending(e => e.ErrorCount).FirstOrDefault()
                }
            };

            return CreateSuccessResponse<object>(analysis, "Endpoint performance analysis retrieved successfully.");
        }

        /// <summary>
        /// Get user activity analysis
        /// </summary>
        /// <returns>User activity data</returns>
        [HttpGet("users")]
        [BusinessAuthorize.SystemAdmin]
        public ActionResult<ApiResponse<object>> GetUserActivity()
        {
            var recentRequests = _metricsCollector.GetRecentRequests(1000);
            
            var userAnalysis = recentRequests
                .Where(r => r.UserId.HasValue)
                .GroupBy(r => r.UserId.Value)
                .Select(g => new
                {
                    UserId = g.Key,
                    RequestCount = g.Count(),
                    AverageResponseTime = Math.Round(g.Average(r => r.ElapsedMilliseconds), 2),
                    ErrorCount = g.Count(r => r.StatusCode >= 400),
                    ErrorRate = Math.Round((double)g.Count(r => r.StatusCode >= 400) / g.Count() * 100, 2),
                    SlowRequestCount = g.Count(r => r.ElapsedMilliseconds > 1000),
                    UniqueEndpoints = g.Select(r => $"{r.Method} {r.Path}").Distinct().Count(),
                    LastActivity = g.Max(r => r.RequestTime),
                    MostUsedEndpoint = g.GroupBy(r => $"{r.Method} {r.Path}")
                                       .OrderByDescending(e => e.Count())
                                       .FirstOrDefault()?.Key
                })
                .OrderByDescending(u => u.RequestCount)
                .ToList();

            var analysis = new
            {
                Users = userAnalysis,
                Summary = new
                {
                    TotalActiveUsers = userAnalysis.Count,
                    MostActiveUser = userAnalysis.FirstOrDefault(),
                    UsersWithErrors = userAnalysis.Count(u => u.ErrorCount > 0),
                    UsersWithSlowRequests = userAnalysis.Count(u => u.SlowRequestCount > 0)
                }
            };

            return CreateSuccessResponse<object>(analysis, "User activity analysis retrieved successfully.");
        }

        private static SystemHealth DetermineSystemHealth(PerformanceStatistics statistics)
        {
            var recommendations = new List<string>();
            
            // Check average response time
            if (statistics.AverageResponseTimeLast5Minutes > 2000)
            {
                recommendations.Add("System is responding slowly. Consider optimizing database queries or scaling resources.");
                return new SystemHealth("Critical", "System performance is degraded", recommendations);
            }
            
            if (statistics.AverageResponseTimeLast5Minutes > 1000)
            {
                recommendations.Add("Response times are elevated. Monitor system resources and database performance.");
                return new SystemHealth("Warning", "System performance is below optimal", recommendations);
            }

            // Check error rate
            var errorRate = statistics.TotalRequests > 0 
                ? (double)statistics.ErrorRequestsCount / statistics.TotalRequests * 100 
                : 0;
            
            if (errorRate > 10)
            {
                recommendations.Add("High error rate detected. Check application logs and fix critical issues.");
                return new SystemHealth("Critical", "High error rate detected", recommendations);
            }
            
            if (errorRate > 5)
            {
                recommendations.Add("Elevated error rate. Monitor application health and investigate errors.");
                return new SystemHealth("Warning", "Elevated error rate", recommendations);
            }

            // Check request volume
            if (statistics.RequestsLast5Minutes > 1000)
            {
                recommendations.Add("High request volume. Monitor system capacity and consider scaling if needed.");
            }

            return new SystemHealth("Healthy", "System is operating normally", recommendations);
        }
    }

    public class SystemHealth
    {
        public string Status { get; }
        public string Message { get; }
        public List<string> Recommendations { get; }

        public SystemHealth(string status, string message, List<string> recommendations)
        {
            Status = status;
            Message = message;
            Recommendations = recommendations;
        }
    }
}
