using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using System.Text.RegularExpressions;
using System.Web;

namespace ElectronicsStore.API.Attributes
{
    /// <summary>
    /// Attribute to validate and sanitize input data to prevent XSS and injection attacks
    /// </summary>
    public class ValidateInputAttribute : ActionFilterAttribute
    {
        private static readonly Regex XssPattern = new Regex(
            @"<script[^>]*>.*?</script>|javascript:|vbscript:|onload=|onerror=|onclick=|onmouseover=",
            RegexOptions.IgnoreCase | RegexOptions.Compiled);

        private static readonly Regex SqlInjectionPattern = new Regex(
            @"(\b(ALTER|CREATE|DELETE|DROP|EXEC(UTE)?|INSERT( +INTO)?|MERGE|SELECT|UPDATE|UNION( +ALL)?)\b)|('|('')|;|--|\*|\|)",
            RegexOptions.IgnoreCase | RegexOptions.Compiled);

        public override void OnActionExecuting(ActionExecutingContext context)
        {
            foreach (var argument in context.ActionArguments)
            {
                if (argument.Value != null)
                {
                    var validationResult = ValidateObject(argument.Value, argument.Key);
                    if (!validationResult.IsValid)
                    {
                        context.Result = new BadRequestObjectResult(new
                        {
                            error = "Invalid input detected",
                            field = validationResult.Field,
                            message = validationResult.Message
                        });
                        return;
                    }
                }
            }

            base.OnActionExecuting(context);
        }

        private ValidationResult ValidateObject(object obj, string fieldName)
        {
            if (obj == null)
                return new ValidationResult { IsValid = true };

            var type = obj.GetType();

            // Handle primitive types and strings
            if (type.IsPrimitive || type == typeof(string) || type == typeof(decimal))
            {
                return ValidateValue(obj.ToString(), fieldName);
            }

            // Handle complex objects
            var properties = type.GetProperties();
            foreach (var property in properties)
            {
                var value = property.GetValue(obj);
                if (value != null)
                {
                    var result = ValidateObject(value, $"{fieldName}.{property.Name}");
                    if (!result.IsValid)
                        return result;
                }
            }

            return new ValidationResult { IsValid = true };
        }

        private ValidationResult ValidateValue(string? value, string fieldName)
        {
            if (string.IsNullOrEmpty(value))
                return new ValidationResult { IsValid = true };

            // Check for XSS patterns
            if (XssPattern.IsMatch(value))
            {
                return new ValidationResult
                {
                    IsValid = false,
                    Field = fieldName,
                    Message = "Potentially malicious script detected"
                };
            }

            // Check for SQL injection patterns
            if (SqlInjectionPattern.IsMatch(value))
            {
                return new ValidationResult
                {
                    IsValid = false,
                    Field = fieldName,
                    Message = "Potentially malicious SQL pattern detected"
                };
            }

            // Check for excessive length
            if (value.Length > 10000)
            {
                return new ValidationResult
                {
                    IsValid = false,
                    Field = fieldName,
                    Message = "Input exceeds maximum allowed length"
                };
            }

            return new ValidationResult { IsValid = true };
        }

        private class ValidationResult
        {
            public bool IsValid { get; set; }
            public string Field { get; set; } = string.Empty;
            public string Message { get; set; } = string.Empty;
        }
    }

    /// <summary>
    /// Attribute to sanitize string inputs by encoding HTML and removing dangerous characters
    /// </summary>
    public class SanitizeInputAttribute : ActionFilterAttribute
    {
        public override void OnActionExecuting(ActionExecutingContext context)
        {
            foreach (var argument in context.ActionArguments.ToList())
            {
                if (argument.Value != null)
                {
                    var sanitizedValue = SanitizeObject(argument.Value);
                    context.ActionArguments[argument.Key] = sanitizedValue;
                }
            }

            base.OnActionExecuting(context);
        }

        private object SanitizeObject(object obj)
        {
            if (obj == null)
                return obj;

            var type = obj.GetType();

            // Handle strings
            if (type == typeof(string))
            {
                return SanitizeString(obj.ToString());
            }

            // Handle primitive types (no sanitization needed)
            if (type.IsPrimitive || type == typeof(decimal) || type == typeof(DateTime))
            {
                return obj;
            }

            // Handle complex objects
            var properties = type.GetProperties().Where(p => p.CanWrite);
            foreach (var property in properties)
            {
                var value = property.GetValue(obj);
                if (value != null)
                {
                    var sanitizedValue = SanitizeObject(value);
                    property.SetValue(obj, sanitizedValue);
                }
            }

            return obj;
        }

        private string SanitizeString(string? input)
        {
            if (string.IsNullOrEmpty(input))
                return input ?? string.Empty;

            // HTML encode to prevent XSS
            var sanitized = HttpUtility.HtmlEncode(input);

            // Remove or replace dangerous characters
            sanitized = sanitized.Replace("<script", "&lt;script")
                                 .Replace("</script>", "&lt;/script&gt;")
                                 .Replace("javascript:", "")
                                 .Replace("vbscript:", "")
                                 .Replace("onload=", "")
                                 .Replace("onerror=", "")
                                 .Replace("onclick=", "")
                                 .Replace("onmouseover=", "");

            // Trim whitespace
            sanitized = sanitized.Trim();

            return sanitized;
        }
    }

    /// <summary>
    /// Attribute to validate specific data formats (email, phone, etc.)
    /// </summary>
    public class ValidateFormatAttribute : ActionFilterAttribute
    {
        private static readonly Regex EmailPattern = new Regex(
            @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$",
            RegexOptions.Compiled);

        private static readonly Regex PhonePattern = new Regex(
            @"^[\+]?[1-9][\d]{0,15}$",
            RegexOptions.Compiled);

        private static readonly Regex BarcodePattern = new Regex(
            @"^[0-9]{8,13}$",
            RegexOptions.Compiled);

        public override void OnActionExecuting(ActionExecutingContext context)
        {
            foreach (var argument in context.ActionArguments)
            {
                if (argument.Value != null)
                {
                    var validationResult = ValidateFormats(argument.Value, argument.Key);
                    if (!validationResult.IsValid)
                    {
                        context.Result = new BadRequestObjectResult(new
                        {
                            error = "Invalid format detected",
                            field = validationResult.Field,
                            message = validationResult.Message
                        });
                        return;
                    }
                }
            }

            base.OnActionExecuting(context);
        }

        private ValidationResult ValidateFormats(object obj, string fieldName)
        {
            if (obj == null)
                return new ValidationResult { IsValid = true };

            var type = obj.GetType();
            var properties = type.GetProperties();

            foreach (var property in properties)
            {
                var value = property.GetValue(obj)?.ToString();
                if (string.IsNullOrEmpty(value))
                    continue;

                var propertyName = property.Name.ToLower();
                var fullFieldName = $"{fieldName}.{property.Name}";

                // Validate email format
                if (propertyName.Contains("email") && !EmailPattern.IsMatch(value))
                {
                    return new ValidationResult
                    {
                        IsValid = false,
                        Field = fullFieldName,
                        Message = "Invalid email format"
                    };
                }

                // Validate phone format
                if (propertyName.Contains("phone") && !PhonePattern.IsMatch(value))
                {
                    return new ValidationResult
                    {
                        IsValid = false,
                        Field = fullFieldName,
                        Message = "Invalid phone number format"
                    };
                }

                // Validate barcode format
                if (propertyName.Contains("barcode") && !BarcodePattern.IsMatch(value))
                {
                    return new ValidationResult
                    {
                        IsValid = false,
                        Field = fullFieldName,
                        Message = "Invalid barcode format (must be 8-13 digits)"
                    };
                }
            }

            return new ValidationResult { IsValid = true };
        }

        private class ValidationResult
        {
            public bool IsValid { get; set; }
            public string Field { get; set; } = string.Empty;
            public string Message { get; set; } = string.Empty;
        }
    }
}
