using Microsoft.EntityFrameworkCore;
using ElectronicsStore.Application.Interfaces;
using ElectronicsStore.Domain.Entities;

namespace ElectronicsStore.Persistence.Repositories
{
    public class SalesRepository : GenericRepository<SalesInvoice>, ISalesRepository
    {
        public SalesRepository(ElectronicsDbContext context) : base(context)
        {
        }

        public async Task<SalesInvoice?> GetByInvoiceNumberAsync(string invoiceNumber)
        {
            return await _dbSet
                .Include(s => s.User)
                .Include(s => s.OverrideByUser)
                .Include(s => s.SalesInvoiceDetails)
                .ThenInclude(d => d.Product)
                .FirstOrDefaultAsync(s => s.InvoiceNumber == invoiceNumber);
        }

        public async Task<IEnumerable<SalesInvoice>> GetByCustomerAsync(string customerName)
        {
            return await _dbSet
                .Include(s => s.User)
                .Include(s => s.SalesInvoiceDetails)
                .Where(s => s.CustomerName != null && s.CustomerName.Contains(customerName))
                .OrderByDescending(s => s.InvoiceDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<SalesInvoice>> GetByUserAsync(int userId)
        {
            return await _dbSet
                .Include(s => s.User)
                .Include(s => s.SalesInvoiceDetails)
                .Where(s => s.UserId == userId)
                .OrderByDescending(s => s.InvoiceDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<SalesInvoice>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate)
        {
            return await _dbSet
                .Include(s => s.User)
                .Include(s => s.SalesInvoiceDetails)
                .ThenInclude(d => d.Product)
                .Where(s => s.InvoiceDate >= fromDate && s.InvoiceDate <= toDate)
                .OrderByDescending(s => s.InvoiceDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<SalesInvoice>> GetByPaymentMethodAsync(string paymentMethod)
        {
            return await _dbSet
                .Include(s => s.User)
                .Include(s => s.SalesInvoiceDetails)
                .Where(s => s.PaymentMethod == paymentMethod)
                .OrderByDescending(s => s.InvoiceDate)
                .ToListAsync();
        }

        public async Task<SalesInvoice?> GetWithDetailsAsync(int id)
        {
            return await _dbSet
                .Include(s => s.User)
                .Include(s => s.OverrideByUser)
                .Include(s => s.SalesInvoiceDetails)
                .ThenInclude(d => d.Product)
                .ThenInclude(p => p.Category)
                .FirstOrDefaultAsync(s => s.Id == id);
        }

        public async Task<IEnumerable<SalesInvoice>> GetWithDetailsAsync(DateTime fromDate, DateTime toDate)
        {
            return await _dbSet
                .Include(s => s.User)
                .Include(s => s.SalesInvoiceDetails)
                .ThenInclude(d => d.Product)
                .Where(s => s.InvoiceDate >= fromDate && s.InvoiceDate <= toDate)
                .OrderByDescending(s => s.InvoiceDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<SalesInvoice>> GetTodaysSalesAsync()
        {
            var today = DateTime.Today;
            var tomorrow = today.AddDays(1);
            
            return await _dbSet
                .Include(s => s.User)
                .Include(s => s.SalesInvoiceDetails)
                .ThenInclude(d => d.Product)
                .Where(s => s.InvoiceDate >= today && s.InvoiceDate < tomorrow)
                .OrderByDescending(s => s.InvoiceDate)
                .ToListAsync();
        }

        public async Task<decimal> GetTotalSalesAmountAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _dbSet.AsQueryable();
            
            if (fromDate.HasValue)
                query = query.Where(s => s.InvoiceDate >= fromDate.Value);
            
            if (toDate.HasValue)
                query = query.Where(s => s.InvoiceDate <= toDate.Value);

            return await query.SumAsync(s => s.TotalAmount);
        }

        public async Task<int> GetTotalSalesCountAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _dbSet.AsQueryable();
            
            if (fromDate.HasValue)
                query = query.Where(s => s.InvoiceDate >= fromDate.Value);
            
            if (toDate.HasValue)
                query = query.Where(s => s.InvoiceDate <= toDate.Value);

            return await query.CountAsync();
        }

        public async Task<decimal> GetAverageSaleAmountAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _dbSet.AsQueryable();
            
            if (fromDate.HasValue)
                query = query.Where(s => s.InvoiceDate >= fromDate.Value);
            
            if (toDate.HasValue)
                query = query.Where(s => s.InvoiceDate <= toDate.Value);

            return await query.AverageAsync(s => s.TotalAmount);
        }

        public async Task<IEnumerable<object>> GetSalesByPaymentMethodAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _dbSet.AsQueryable();
            
            if (fromDate.HasValue)
                query = query.Where(s => s.InvoiceDate >= fromDate.Value);
            
            if (toDate.HasValue)
                query = query.Where(s => s.InvoiceDate <= toDate.Value);

            return await query
                .GroupBy(s => s.PaymentMethod)
                .Select(g => new
                {
                    PaymentMethod = g.Key,
                    Count = g.Count(),
                    TotalAmount = g.Sum(s => s.TotalAmount),
                    AverageAmount = g.Average(s => s.TotalAmount)
                })
                .ToListAsync();
        }

        public async Task<IEnumerable<object>> GetDailySalesReportAsync(DateTime fromDate, DateTime toDate)
        {
            return await _dbSet
                .Where(s => s.InvoiceDate >= fromDate && s.InvoiceDate <= toDate)
                .GroupBy(s => s.InvoiceDate.Date)
                .Select(g => new
                {
                    Date = g.Key,
                    Count = g.Count(),
                    TotalAmount = g.Sum(s => s.TotalAmount),
                    TotalDiscount = g.Sum(s => s.DiscountTotal)
                })
                .OrderBy(x => x.Date)
                .ToListAsync();
        }

        public async Task<IEnumerable<object>> GetMonthlySalesReportAsync(int year)
        {
            return await _dbSet
                .Where(s => s.InvoiceDate.Year == year)
                .GroupBy(s => s.InvoiceDate.Month)
                .Select(g => new
                {
                    Month = g.Key,
                    Count = g.Count(),
                    TotalAmount = g.Sum(s => s.TotalAmount),
                    TotalDiscount = g.Sum(s => s.DiscountTotal)
                })
                .OrderBy(x => x.Month)
                .ToListAsync();
        }

        public async Task<IEnumerable<object>> GetTopCustomersAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _dbSet.Where(s => s.CustomerName != null);
            
            if (fromDate.HasValue)
                query = query.Where(s => s.InvoiceDate >= fromDate.Value);
            
            if (toDate.HasValue)
                query = query.Where(s => s.InvoiceDate <= toDate.Value);

            return await query
                .GroupBy(s => s.CustomerName)
                .Select(g => new
                {
                    CustomerName = g.Key,
                    TotalPurchases = g.Count(),
                    TotalAmount = g.Sum(s => s.TotalAmount),
                    AverageAmount = g.Average(s => s.TotalAmount),
                    LastPurchase = g.Max(s => s.InvoiceDate)
                })
                .OrderByDescending(x => x.TotalAmount)
                .Take(count)
                .ToListAsync();
        }

        public async Task<IEnumerable<object>> GetTopSellingProductsAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _context.SalesInvoiceDetails
                .Include(d => d.Product)
                .Include(d => d.SalesInvoice)
                .AsQueryable();

            if (fromDate.HasValue)
                query = query.Where(d => d.SalesInvoice.InvoiceDate >= fromDate.Value);
            
            if (toDate.HasValue)
                query = query.Where(d => d.SalesInvoice.InvoiceDate <= toDate.Value);

            return await query
                .GroupBy(d => new { d.Product.Id, d.Product.Name })
                .Select(g => new
                {
                    ProductId = g.Key.Id,
                    ProductName = g.Key.Name,
                    TotalQuantity = g.Sum(d => d.Quantity),
                    TotalAmount = g.Sum(d => d.LineTotal),
                    AveragePrice = g.Average(d => d.UnitPrice)
                })
                .OrderByDescending(x => x.TotalQuantity)
                .Take(count)
                .ToListAsync();
        }

        public async Task<IEnumerable<SalesInvoice>> GetOverriddenSalesAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _dbSet
                .Include(s => s.User)
                .Include(s => s.OverrideByUser)
                .Include(s => s.SalesInvoiceDetails)
                .Where(s => s.OverrideByUserId != null);
            
            if (fromDate.HasValue)
                query = query.Where(s => s.InvoiceDate >= fromDate.Value);
            
            if (toDate.HasValue)
                query = query.Where(s => s.InvoiceDate <= toDate.Value);

            return await query
                .OrderByDescending(s => s.OverrideDate)
                .ToListAsync();
        }

        public async Task<decimal> GetTotalDiscountsAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _dbSet.AsQueryable();
            
            if (fromDate.HasValue)
                query = query.Where(s => s.InvoiceDate >= fromDate.Value);
            
            if (toDate.HasValue)
                query = query.Where(s => s.InvoiceDate <= toDate.Value);

            var invoiceDiscounts = await query.SumAsync(s => s.DiscountTotal);
            
            var detailDiscounts = await _context.SalesInvoiceDetails
                .Include(d => d.SalesInvoice)
                .Where(d => fromDate == null || d.SalesInvoice.InvoiceDate >= fromDate.Value)
                .Where(d => toDate == null || d.SalesInvoice.InvoiceDate <= toDate.Value)
                .SumAsync(d => d.DiscountAmount * d.Quantity);

            return invoiceDiscounts + detailDiscounts;
        }

        public async Task<(IEnumerable<SalesInvoice> Sales, int TotalCount)> GetPagedSalesAsync(
            int pageNumber, int pageSize, string? searchTerm = null, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _dbSet
                .Include(s => s.User)
                .Include(s => s.SalesInvoiceDetails)
                .AsQueryable();

            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(s => s.InvoiceNumber.Contains(searchTerm) ||
                                        (s.CustomerName != null && s.CustomerName.Contains(searchTerm)));
            }

            if (fromDate.HasValue)
                query = query.Where(s => s.InvoiceDate >= fromDate.Value);
            
            if (toDate.HasValue)
                query = query.Where(s => s.InvoiceDate <= toDate.Value);

            var totalCount = await query.CountAsync();
            var sales = await query
                .OrderByDescending(s => s.InvoiceDate)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return (sales, totalCount);
        }
    }
}
