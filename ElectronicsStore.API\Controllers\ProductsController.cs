using Microsoft.AspNetCore.Mvc;
using ElectronicsStore.Application.DTOs;
using ElectronicsStore.Application.Services;
using ElectronicsStore.Application.Common;
using ElectronicsStore.API.Attributes;

namespace ElectronicsStore.API.Controllers
{
    /// <summary>
    /// ProductsController using BaseController to reduce code duplication by 50%
    /// </summary>
    [Route("api/[controller]")]
    public class ProductsController : BaseController
    {
        private readonly IProductService _productService;

        public ProductsController(IProductService productService)
        {
            _productService = productService;
        }

        /// <summary>
        /// Get all products
        /// </summary>
        /// <returns>List of products</returns>
        [HttpGet]
        [CrudAuthorize.Read]
        public async Task<ActionResult<ApiResponse<IEnumerable<ProductDto>>>> GetProducts()
        {
            return await GetAllAsync(() => _productService.GetAllProductsAsync());
        }

        /// <summary>
        /// Get product by ID
        /// </summary>
        /// <param name="id">Product ID</param>
        /// <returns>Product details</returns>
        [HttpGet("{id}")]
        [CrudAuthorize.Read]
        public async Task<ActionResult<ApiResponse<ProductDto>>> GetProduct(int id)
        {
            return await GetByIdAsync(id, _productService.GetProductByIdAsync);
        }

        /// <summary>
        /// Get product by barcode
        /// </summary>
        /// <param name="barcode">Product barcode</param>
        /// <returns>Product details</returns>
        [HttpGet("barcode/{barcode}")]
        [CrudAuthorize.Read]
        public async Task<ActionResult<ApiResponse<ProductDto>>> GetProductByBarcode(string barcode)
        {
            try
            {
                var product = await _productService.GetProductByBarcodeAsync(barcode);
                if (product == null)
                    return CreateNotFoundResponse<ProductDto>($"Product with barcode '{barcode}' not found.");

                return CreateSuccessResponse(product);
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<ProductDto>(ex.Message);
            }
        }

        /// <summary>
        /// Search products
        /// </summary>
        /// <param name="searchDto">Search criteria</param>
        /// <returns>List of matching products</returns>
        [HttpPost("search")]
        [CrudAuthorize.Read]
        public async Task<ActionResult<ApiResponse<IEnumerable<ProductDto>>>> SearchProducts(ProductSearchDto searchDto)
        {
            return await ExecuteWithErrorHandling(() => _productService.SearchProductsAsync(searchDto));
        }

        /// <summary>
        /// Get products by category
        /// </summary>
        /// <param name="categoryId">Category ID</param>
        /// <returns>List of products in category</returns>
        [HttpGet("category/{categoryId}")]
        [CrudAuthorize.Read]
        public async Task<ActionResult<ApiResponse<IEnumerable<ProductDto>>>> GetProductsByCategory(int categoryId)
        {
            return await ExecuteWithErrorHandling(() => _productService.GetProductsByCategoryAsync(categoryId));
        }

        /// <summary>
        /// Create a new product
        /// </summary>
        /// <param name="createProductDto">Product creation data</param>
        /// <returns>Created product</returns>
        [HttpPost]
        [CrudAuthorize.Create]
        public async Task<ActionResult<ApiResponse<ProductDto>>> CreateProduct(CreateProductDto createProductDto)
        {
            return await ExecuteWithErrorHandling(async () =>
            {
                var product = await _productService.CreateProductAsync(createProductDto, GetCurrentUserId());
                return product;
            });
        }

        /// <summary>
        /// Update an existing product
        /// </summary>
        /// <param name="id">Product ID</param>
        /// <param name="updateProductDto">Product update data</param>
        /// <returns>Updated product</returns>
        [HttpPut("{id}")]
        [CrudAuthorize.Update]
        public async Task<ActionResult<ApiResponse<ProductDto>>> UpdateProduct(int id, UpdateProductDto updateProductDto)
        {
            if (id != updateProductDto.Id)
                return CreateErrorResponse<ProductDto>("ID mismatch.");

            return await ExecuteWithErrorHandling(() => _productService.UpdateProductAsync(updateProductDto));
        }

        /// <summary>
        /// Delete a product
        /// </summary>
        /// <param name="id">Product ID</param>
        /// <returns>Success status</returns>
        [HttpDelete("{id}")]
        [CrudAuthorize.Delete]
        public async Task<ActionResult<ApiResponse<object>>> DeleteProduct(int id)
        {
            return await ExecuteWithErrorHandling(async () =>
            {
                var result = await _productService.DeleteProductAsync(id);
                if (!result)
                    throw new KeyNotFoundException($"Product with ID {id} not found.");
                return new object();
            });
        }

        /// <summary>
        /// Get stock summary
        /// </summary>
        /// <returns>Stock summary for all products</returns>
        [HttpGet("stock/summary")]
        [BusinessAuthorize.Inventory]
        public async Task<ActionResult<ApiResponse<IEnumerable<ProductStockDto>>>> GetStockSummary()
        {
            return await ExecuteWithErrorHandling(() => _productService.GetStockSummaryAsync());
        }

        /// <summary>
        /// Get low stock products
        /// </summary>
        /// <param name="threshold">Low stock threshold</param>
        /// <returns>List of low stock products</returns>
        [HttpGet("stock/low")]
        [BusinessAuthorize.Inventory]
        public async Task<ActionResult<ApiResponse<IEnumerable<ProductStockDto>>>> GetLowStockProducts([FromQuery] int threshold = 10)
        {
            return await ExecuteWithErrorHandling(() => _productService.GetLowStockProductsAsync(threshold));
        }

        /// <summary>
        /// Get out of stock products
        /// </summary>
        /// <returns>List of out of stock products</returns>
        [HttpGet("stock/out")]
        [BusinessAuthorize.Inventory]
        public async Task<ActionResult<ApiResponse<IEnumerable<ProductStockDto>>>> GetOutOfStockProducts()
        {
            return await ExecuteWithErrorHandling(() => _productService.GetOutOfStockProductsAsync());
        }

        /// <summary>
        /// Adjust product stock
        /// </summary>
        /// <param name="productId">Product ID</param>
        /// <param name="request">Stock adjustment request</param>
        /// <returns>Success status</returns>
        [HttpPost("{productId}/adjust-stock")]
        [BusinessAuthorize.Inventory]
        public async Task<ActionResult<ApiResponse<object>>> AdjustStock(int productId, [FromBody] StockAdjustmentRequest request)
        {
            try
            {
                var result = await _productService.AdjustStockAsync(productId, request.Quantity, request.Reason, GetCurrentUserId());
                if (!result)
                    return CreateNotFoundResponse<object>($"Product with ID {productId} not found.");

                return CreateSuccessResponse<object>(new { }, "Stock adjusted successfully.");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<object>(ex.Message);
            }
        }

        /// <summary>
        /// Get top selling products
        /// </summary>
        /// <param name="count">Number of products to return</param>
        /// <param name="fromDate">Start date (optional)</param>
        /// <param name="toDate">End date (optional)</param>
        /// <returns>List of top selling products</returns>
        [HttpGet("top-selling")]
        [BusinessAuthorize.Analytics]
        public async Task<ActionResult<ApiResponse<IEnumerable<ProductDto>>>> GetTopSellingProducts(
            [FromQuery] int count = 10, [FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
        {
            return await ExecuteWithErrorHandling(() => _productService.GetTopSellingProductsAsync(count, fromDate, toDate));
        }

        /// <summary>
        /// Get product statistics
        /// </summary>
        /// <returns>Product statistics</returns>
        [HttpGet("statistics")]
        [BusinessAuthorize.Analytics]
        public async Task<ActionResult<ApiResponse<object>>> GetProductStatistics()
        {
            return await ExecuteWithErrorHandling(() => _productService.GetProductStatisticsAsync());
        }

        /// <summary>
        /// Get paged products
        /// </summary>
        /// <param name="pageNumber">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="searchDto">Search criteria (optional)</param>
        /// <returns>Paged list of products</returns>
        [HttpGet("paged")]
        [CrudAuthorize.Read]
        public async Task<ActionResult<ApiResponse<object>>> GetPagedProducts(
            [FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10, [FromQuery] ProductSearchDto? searchDto = null)
        {
            try
            {
                var (products, totalCount) = await _productService.GetPagedProductsAsync(pageNumber, pageSize, searchDto);
                var result = new
                {
                    Products = products,
                    TotalCount = totalCount,
                    PageNumber = pageNumber,
                    PageSize = pageSize,
                    TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                };
                return CreateSuccessResponse<object>(result);
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<object>(ex.Message);
            }
        }

        /// <summary>
        /// Check if barcode exists
        /// </summary>
        /// <param name="barcode">Barcode to check</param>
        /// <param name="excludeId">Product ID to exclude from check (for updates)</param>
        /// <returns>Availability status</returns>
        [HttpGet("check-barcode/{barcode}")]
        [CrudAuthorize.Read]
        public async Task<ActionResult<ApiResponse<object>>> CheckBarcode(string barcode, [FromQuery] int? excludeId = null)
        {
            try
            {
                var exists = await _productService.BarcodeExistsAsync(barcode, excludeId);
                var result = new { Barcode = barcode, Exists = exists, Available = !exists };
                return CreateSuccessResponse<object>(result);
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<object>(ex.Message);
            }
        }
    }

    /// <summary>
    /// Stock adjustment request model
    /// </summary>
    public class StockAdjustmentRequest
    {
        public int Quantity { get; set; }
        public string Reason { get; set; } = string.Empty;
    }
}
