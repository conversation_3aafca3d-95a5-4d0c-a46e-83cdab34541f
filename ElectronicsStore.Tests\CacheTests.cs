using Xunit;
using FluentAssertions;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Moq;
using ElectronicsStore.Infrastructure.Services;

namespace ElectronicsStore.Tests
{
    /// <summary>
    /// Tests for Cache functionality
    /// </summary>
    public class CacheTests
    {
        private readonly IMemoryCache _memoryCache;
        private readonly Mock<ILogger<MemoryCacheService>> _mockLogger;
        private readonly MemoryCacheService _cacheService;

        public CacheTests()
        {
            _memoryCache = new MemoryCache(new MemoryCacheOptions());
            _mockLogger = new Mock<ILogger<MemoryCacheService>>();
            _cacheService = new MemoryCacheService(_memoryCache, _mockLogger.Object);
        }

        [Fact]
        public async Task SetAsync_ShouldStoreValueInCache()
        {
            // Arrange
            var key = "test_key";
            var value = new TestObject { Id = 1, Name = "Test" };

            // Act
            await _cacheService.SetAsync(key, value, TimeSpan.FromMinutes(5));

            // Assert
            var cachedValue = await _cacheService.GetAsync<TestObject>(key);
            cachedValue.Should().NotBeNull();
            cachedValue!.Id.Should().Be(1);
            cachedValue.Name.Should().Be("Test");
        }

        [Fact]
        public async Task GetAsync_WithNonExistentKey_ShouldReturnNull()
        {
            // Arrange
            var key = "non_existent_key";

            // Act
            var result = await _cacheService.GetAsync<TestObject>(key);

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public async Task RemoveAsync_ShouldRemoveValueFromCache()
        {
            // Arrange
            var key = "test_key_to_remove";
            var value = new TestObject { Id = 2, Name = "Test Remove" };
            await _cacheService.SetAsync(key, value);

            // Act
            await _cacheService.RemoveAsync(key);

            // Assert
            var cachedValue = await _cacheService.GetAsync<TestObject>(key);
            cachedValue.Should().BeNull();
        }

        [Fact]
        public async Task GetOrSetAsync_WithExistingKey_ShouldReturnCachedValue()
        {
            // Arrange
            var key = "existing_key";
            var existingValue = new TestObject { Id = 3, Name = "Existing" };
            await _cacheService.SetAsync(key, existingValue);

            var callCount = 0;
            Func<Task<TestObject>> factory = () =>
            {
                callCount++;
                return Task.FromResult(new TestObject { Id = 999, Name = "Should not be called" });
            };

            // Act
            var result = await _cacheService.GetOrSetAsync(key, factory);

            // Assert
            result.Should().NotBeNull();
            result.Id.Should().Be(3);
            result.Name.Should().Be("Existing");
            callCount.Should().Be(0); // Factory should not be called
        }

        [Fact]
        public async Task GetOrSetAsync_WithNonExistentKey_ShouldCallFactoryAndCache()
        {
            // Arrange
            var key = "new_key";
            var newValue = new TestObject { Id = 4, Name = "New Value" };

            var callCount = 0;
            Func<Task<TestObject>> factory = () =>
            {
                callCount++;
                return Task.FromResult(newValue);
            };

            // Act
            var result = await _cacheService.GetOrSetAsync(key, factory, TimeSpan.FromMinutes(5));

            // Assert
            result.Should().NotBeNull();
            result.Id.Should().Be(4);
            result.Name.Should().Be("New Value");
            callCount.Should().Be(1); // Factory should be called once

            // Verify it's cached
            var cachedValue = await _cacheService.GetAsync<TestObject>(key);
            cachedValue.Should().NotBeNull();
            cachedValue!.Id.Should().Be(4);
        }

        [Fact]
        public async Task RemoveByPatternAsync_ShouldRemoveMatchingKeys()
        {
            // Arrange
            await _cacheService.SetAsync("category_1", new TestObject { Id = 1, Name = "Category 1" });
            await _cacheService.SetAsync("category_2", new TestObject { Id = 2, Name = "Category 2" });
            await _cacheService.SetAsync("product_1", new TestObject { Id = 3, Name = "Product 1" });

            // Act
            await _cacheService.RemoveByPatternAsync("category");

            // Assert
            var category1 = await _cacheService.GetAsync<TestObject>("category_1");
            var category2 = await _cacheService.GetAsync<TestObject>("category_2");
            var product1 = await _cacheService.GetAsync<TestObject>("product_1");

            category1.Should().BeNull();
            category2.Should().BeNull();
            product1.Should().NotBeNull(); // Should not be removed
        }

        [Fact]
        public void CacheKeys_ShouldGenerateCorrectKeys()
        {
            // Act & Assert
            CacheKeys.GetCategoryByIdKey(1).Should().Be("category_1");
            CacheKeys.GetProductByIdKey(5).Should().Be("product_5");
            CacheKeys.GetSupplierByIdKey(10).Should().Be("supplier_10");
            CacheKeys.GetUserByIdKey(3).Should().Be("user_3");
            CacheKeys.GetSalesStatisticsKey("2024-01").Should().Be("sales_statistics_2024-01");
        }

        [Theory]
        [InlineData("categories")]
        [InlineData("products")]
        [InlineData("suppliers")]
        public async Task Cache_ShouldHandleMultipleDataTypes(string keyPrefix)
        {
            // Arrange
            var key = $"{keyPrefix}_test";
            var value = new TestObject { Id = 100, Name = $"Test {keyPrefix}" };

            // Act
            await _cacheService.SetAsync(key, value);
            var result = await _cacheService.GetAsync<TestObject>(key);

            // Assert
            result.Should().NotBeNull();
            result!.Name.Should().Be($"Test {keyPrefix}");
        }

        [Fact]
        public async Task Cache_ShouldHandleNullValues()
        {
            // Arrange
            var key = "null_test";

            // Act & Assert - Should not throw exception
            var result = await _cacheService.GetAsync<TestObject>(key);
            result.Should().BeNull();
        }

        [Fact]
        public async Task Cache_ShouldExpireAfterTimeout()
        {
            // Arrange
            var key = "expiring_key";
            var value = new TestObject { Id = 999, Name = "Expiring" };

            // Act
            await _cacheService.SetAsync(key, value, TimeSpan.FromMilliseconds(100));
            
            // Wait for expiration
            await Task.Delay(200);

            // Assert
            var result = await _cacheService.GetAsync<TestObject>(key);
            // Note: This test might be flaky due to timing, but demonstrates the concept
        }

        private class TestObject
        {
            public int Id { get; set; }
            public string Name { get; set; } = string.Empty;
        }
    }
}
