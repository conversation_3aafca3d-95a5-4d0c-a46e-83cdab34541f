using ElectronicsStore.Application.DTOs;
using ElectronicsStore.Application.Interfaces;
using ElectronicsStore.Domain.Entities;

namespace ElectronicsStore.Application.Services
{
    public class SalesService : ISalesService
    {
        private readonly IUnitOfWork _unitOfWork;

        public SalesService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<SalesInvoiceDto?> GetSalesInvoiceByIdAsync(int id)
        {
            var salesInvoice = await _unitOfWork.Sales.GetWithDetailsAsync(id);
            return salesInvoice != null ? MapToSalesInvoiceDto(salesInvoice) : null;
        }

        public async Task<SalesInvoiceDto?> GetSalesInvoiceByNumberAsync(string invoiceNumber)
        {
            var salesInvoice = await _unitOfWork.Sales.GetByInvoiceNumberAsync(invoiceNumber);
            return salesInvoice != null ? MapToSalesInvoiceDto(salesInvoice) : null;
        }

        public async Task<IEnumerable<SalesInvoiceDto>> GetAllSalesInvoicesAsync()
        {
            var salesInvoices = await _unitOfWork.Sales.GetAllAsync();
            return salesInvoices.Select(MapToSalesInvoiceDto);
        }

        public async Task<SalesInvoiceDto> CreateSalesInvoiceAsync(CreateSalesInvoiceDto createSalesInvoiceDto)
        {
            // Validation
            if (await InvoiceNumberExistsAsync(createSalesInvoiceDto.InvoiceNumber))
                throw new InvalidOperationException($"Invoice number '{createSalesInvoiceDto.InvoiceNumber}' already exists.");

            if (!await _unitOfWork.Users.AnyAsync(u => u.Id == createSalesInvoiceDto.UserId))
                throw new InvalidOperationException($"User with ID {createSalesInvoiceDto.UserId} not found.");

            if (!createSalesInvoiceDto.Details.Any())
                throw new InvalidOperationException("Sales invoice must have at least one detail item.");

            // Validate payment method
            var validPaymentMethods = new[] { "cash", "card", "deferred" };
            if (!validPaymentMethods.Contains(createSalesInvoiceDto.PaymentMethod.ToLower()))
                throw new InvalidOperationException("Invalid payment method. Valid options: cash, card, deferred.");

            await _unitOfWork.BeginTransactionAsync();
            try
            {
                // Create sales invoice
                var salesInvoice = new SalesInvoice
                {
                    InvoiceNumber = createSalesInvoiceDto.InvoiceNumber,
                    CustomerName = createSalesInvoiceDto.CustomerName,
                    InvoiceDate = DateTime.Now,
                    DiscountTotal = createSalesInvoiceDto.DiscountTotal,
                    PaymentMethod = createSalesInvoiceDto.PaymentMethod.ToLower(),
                    UserId = createSalesInvoiceDto.UserId,
                    TotalAmount = 0 // Will be calculated
                };

                await _unitOfWork.Sales.AddAsync(salesInvoice);
                await _unitOfWork.SaveChangesAsync();

                decimal totalAmount = 0;

                // Create sales invoice details and update inventory
                foreach (var detailDto in createSalesInvoiceDto.Details)
                {
                    // Validate product exists
                    var product = await _unitOfWork.Products.GetByIdAsync(detailDto.ProductId);
                    if (product == null)
                        throw new InvalidOperationException($"Product with ID {detailDto.ProductId} not found.");

                    // Check stock availability
                    var currentStock = await _unitOfWork.Inventory.GetCurrentStockAsync(detailDto.ProductId);
                    if (currentStock < detailDto.Quantity)
                        throw new InvalidOperationException($"Insufficient stock for product '{product.Name}'. Available: {currentStock}, Required: {detailDto.Quantity}");

                    // Validate price
                    if (detailDto.UnitPrice < product.MinSellingPrice)
                        throw new InvalidOperationException($"Unit price {detailDto.UnitPrice} is below minimum selling price {product.MinSellingPrice} for product '{product.Name}'.");

                    // Create sales invoice detail
                    var detail = new SalesInvoiceDetail
                    {
                        SalesInvoiceId = salesInvoice.Id,
                        ProductId = detailDto.ProductId,
                        Quantity = detailDto.Quantity,
                        UnitPrice = detailDto.UnitPrice,
                        DiscountAmount = detailDto.DiscountAmount
                    };

                    await _unitOfWork.SalesInvoiceDetails.AddAsync(detail);

                    // Calculate line total
                    var lineTotal = (detailDto.UnitPrice - detailDto.DiscountAmount) * detailDto.Quantity;
                    totalAmount += lineTotal;

                    // Create inventory movement (negative quantity for sale)
                    var inventoryLog = new InventoryLog
                    {
                        ProductId = detailDto.ProductId,
                        MovementType = "sale",
                        Quantity = -detailDto.Quantity, // Negative for outgoing
                        UnitCost = product.DefaultCostPrice, // Use cost price for COGS calculation
                        ReferenceTable = "sales_invoices",
                        ReferenceId = salesInvoice.Id,
                        Note = $"Sale - Invoice #{salesInvoice.InvoiceNumber}",
                        UserId = createSalesInvoiceDto.UserId,
                        CreatedAt = DateTime.Now
                    };

                    await _unitOfWork.Inventory.AddAsync(inventoryLog);
                }

                // Update total amount
                salesInvoice.TotalAmount = totalAmount - createSalesInvoiceDto.DiscountTotal;
                _unitOfWork.Sales.Update(salesInvoice);

                await _unitOfWork.SaveChangesAsync();
                await _unitOfWork.CommitTransactionAsync();

                // Return the created sales invoice with details
                var createdSalesInvoice = await _unitOfWork.Sales.GetWithDetailsAsync(salesInvoice.Id);
                return MapToSalesInvoiceDto(createdSalesInvoice!);
            }
            catch
            {
                await _unitOfWork.RollbackTransactionAsync();
                throw;
            }
        }

        public async Task<bool> DeleteSalesInvoiceAsync(int id)
        {
            var salesInvoice = await _unitOfWork.Sales.GetWithDetailsAsync(id);
            if (salesInvoice == null)
                return false;

            if (!await CanDeleteSalesInvoiceAsync(id))
                throw new InvalidOperationException("Cannot delete sales invoice that has returns or other related records.");

            await _unitOfWork.BeginTransactionAsync();
            try
            {
                // Reverse inventory movements
                foreach (var detail in salesInvoice.SalesInvoiceDetails)
                {
                    var inventoryLog = new InventoryLog
                    {
                        ProductId = detail.ProductId,
                        MovementType = "sale_reversal",
                        Quantity = detail.Quantity, // Positive to add back to stock
                        UnitCost = detail.Product.DefaultCostPrice,
                        ReferenceTable = "sales_invoices",
                        ReferenceId = salesInvoice.Id,
                        Note = $"Sale reversal - Invoice #{salesInvoice.InvoiceNumber} deleted",
                        UserId = salesInvoice.UserId,
                        CreatedAt = DateTime.Now
                    };

                    await _unitOfWork.Inventory.AddAsync(inventoryLog);
                }

                // Delete sales invoice details
                _unitOfWork.SalesInvoiceDetails.RemoveRange(salesInvoice.SalesInvoiceDetails);

                // Delete sales invoice
                _unitOfWork.Sales.Remove(salesInvoice);

                await _unitOfWork.SaveChangesAsync();
                await _unitOfWork.CommitTransactionAsync();

                return true;
            }
            catch
            {
                await _unitOfWork.RollbackTransactionAsync();
                throw;
            }
        }

        public async Task<IEnumerable<SalesInvoiceDto>> SearchSalesInvoicesAsync(SalesSearchDto searchDto)
        {
            var salesInvoices = await _unitOfWork.Sales.GetAllAsync();
            var filteredSales = salesInvoices.AsQueryable();

            if (!string.IsNullOrEmpty(searchDto.SearchTerm))
            {
                filteredSales = filteredSales.Where(s => 
                    s.InvoiceNumber.Contains(searchDto.SearchTerm) ||
                    (s.CustomerName != null && s.CustomerName.Contains(searchDto.SearchTerm)));
            }

            if (!string.IsNullOrEmpty(searchDto.CustomerName))
                filteredSales = filteredSales.Where(s => s.CustomerName != null && s.CustomerName.Contains(searchDto.CustomerName));

            if (!string.IsNullOrEmpty(searchDto.PaymentMethod))
                filteredSales = filteredSales.Where(s => s.PaymentMethod == searchDto.PaymentMethod);

            if (searchDto.UserId.HasValue)
                filteredSales = filteredSales.Where(s => s.UserId == searchDto.UserId.Value);

            if (searchDto.FromDate.HasValue)
                filteredSales = filteredSales.Where(s => s.InvoiceDate >= searchDto.FromDate.Value);

            if (searchDto.ToDate.HasValue)
                filteredSales = filteredSales.Where(s => s.InvoiceDate <= searchDto.ToDate.Value);

            if (searchDto.MinAmount.HasValue)
                filteredSales = filteredSales.Where(s => s.TotalAmount >= searchDto.MinAmount.Value);

            if (searchDto.MaxAmount.HasValue)
                filteredSales = filteredSales.Where(s => s.TotalAmount <= searchDto.MaxAmount.Value);

            return filteredSales.Select(MapToSalesInvoiceDto);
        }

        public async Task<IEnumerable<SalesInvoiceDto>> GetSalesInvoicesByCustomerAsync(string customerName)
        {
            var salesInvoices = await _unitOfWork.Sales.GetByCustomerAsync(customerName);
            return salesInvoices.Select(MapToSalesInvoiceDto);
        }

        public async Task<IEnumerable<SalesInvoiceDto>> GetSalesInvoicesByUserAsync(int userId)
        {
            var salesInvoices = await _unitOfWork.Sales.GetByUserAsync(userId);
            return salesInvoices.Select(MapToSalesInvoiceDto);
        }

        public async Task<IEnumerable<SalesInvoiceDto>> GetSalesInvoicesByDateRangeAsync(DateTime fromDate, DateTime toDate)
        {
            var salesInvoices = await _unitOfWork.Sales.GetByDateRangeAsync(fromDate, toDate);
            return salesInvoices.Select(MapToSalesInvoiceDto);
        }

        public async Task<IEnumerable<SalesInvoiceDto>> GetSalesInvoicesByPaymentMethodAsync(string paymentMethod)
        {
            var salesInvoices = await _unitOfWork.Sales.GetByPaymentMethodAsync(paymentMethod);
            return salesInvoices.Select(MapToSalesInvoiceDto);
        }

        public async Task<IEnumerable<SalesInvoiceDto>> GetTodaysSalesAsync()
        {
            var salesInvoices = await _unitOfWork.Sales.GetTodaysSalesAsync();
            return salesInvoices.Select(MapToSalesInvoiceDto);
        }

        private SalesInvoiceDto MapToSalesInvoiceDto(SalesInvoice salesInvoice)
        {
            return new SalesInvoiceDto
            {
                Id = salesInvoice.Id,
                InvoiceNumber = salesInvoice.InvoiceNumber,
                CustomerName = salesInvoice.CustomerName,
                InvoiceDate = salesInvoice.InvoiceDate,
                DiscountTotal = salesInvoice.DiscountTotal,
                TotalAmount = salesInvoice.TotalAmount,
                PaymentMethod = salesInvoice.PaymentMethod,
                OverrideByUserId = salesInvoice.OverrideByUserId,
                OverrideByUserName = salesInvoice.OverrideByUser?.Username,
                OverrideDate = salesInvoice.OverrideDate,
                UserId = salesInvoice.UserId,
                UserName = salesInvoice.User?.Username ?? "",
                Details = salesInvoice.SalesInvoiceDetails?.Select(d => new SalesInvoiceDetailDto
                {
                    Id = d.Id,
                    SalesInvoiceId = d.SalesInvoiceId,
                    ProductId = d.ProductId,
                    ProductName = d.Product?.Name ?? "",
                    Quantity = d.Quantity,
                    UnitPrice = d.UnitPrice,
                    DiscountAmount = d.DiscountAmount,
                    LineTotal = d.LineTotal
                }).ToList() ?? new List<SalesInvoiceDetailDto>()
            };
        }

        public async Task<decimal> GetTotalSalesAmountAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            return await _unitOfWork.Sales.GetTotalSalesAmountAsync(fromDate, toDate);
        }

        public async Task<int> GetTotalSalesCountAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            return await _unitOfWork.Sales.GetTotalSalesCountAsync(fromDate, toDate);
        }

        public async Task<decimal> GetAverageSaleAmountAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            return await _unitOfWork.Sales.GetAverageSaleAmountAsync(fromDate, toDate);
        }

        public async Task<decimal> GetTotalDiscountsAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            return await _unitOfWork.Sales.GetTotalDiscountsAsync(fromDate, toDate);
        }

        public async Task<IEnumerable<SalesReportDto>> GetDailySalesReportAsync(DateTime fromDate, DateTime toDate)
        {
            var report = await _unitOfWork.Sales.GetDailySalesReportAsync(fromDate, toDate);
            return report.Cast<dynamic>().Select(item => new SalesReportDto
            {
                Date = item.Date,
                TotalSales = item.Count,
                TotalAmount = item.TotalAmount,
                TotalDiscount = item.TotalDiscount,
                NetAmount = item.TotalAmount - item.TotalDiscount
            });
        }

        public async Task<IEnumerable<SalesReportDto>> GetMonthlySalesReportAsync(int year)
        {
            var report = await _unitOfWork.Sales.GetMonthlySalesReportAsync(year);
            return report.Cast<dynamic>().Select(item => new SalesReportDto
            {
                Date = new DateTime(year, item.Month, 1),
                TotalSales = item.Count,
                TotalAmount = item.TotalAmount,
                TotalDiscount = item.TotalDiscount,
                NetAmount = item.TotalAmount - item.TotalDiscount
            });
        }

        public async Task<IEnumerable<object>> GetSalesByPaymentMethodAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            return await _unitOfWork.Sales.GetSalesByPaymentMethodAsync(fromDate, toDate);
        }

        public async Task<IEnumerable<TopCustomerDto>> GetTopCustomersAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var topCustomers = await _unitOfWork.Sales.GetTopCustomersAsync(count, fromDate, toDate);
            return topCustomers.Cast<dynamic>().Select(item => new TopCustomerDto
            {
                CustomerName = item.CustomerName,
                TotalPurchases = item.TotalPurchases,
                TotalAmount = item.TotalAmount,
                AverageAmount = item.AverageAmount,
                LastPurchase = item.LastPurchase
            });
        }

        public async Task<IEnumerable<TopProductDto>> GetTopSellingProductsAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var topProducts = await _unitOfWork.Sales.GetTopSellingProductsAsync(count, fromDate, toDate);
            return topProducts.Cast<dynamic>().Select(item => new TopProductDto
            {
                ProductId = item.ProductId,
                ProductName = item.ProductName,
                TotalQuantity = item.TotalQuantity,
                TotalAmount = item.TotalAmount,
                AveragePrice = item.AveragePrice
            });
        }

        public async Task<IEnumerable<SalesInvoiceDto>> GetOverriddenSalesAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var overriddenSales = await _unitOfWork.Sales.GetOverriddenSalesAsync(fromDate, toDate);
            return overriddenSales.Select(MapToSalesInvoiceDto);
        }

        public async Task<bool> OverrideSaleAsync(int salesInvoiceId, int overrideByUserId, string reason)
        {
            var salesInvoice = await _unitOfWork.Sales.GetByIdAsync(salesInvoiceId);
            if (salesInvoice == null)
                return false;

            if (!await _unitOfWork.Users.AnyAsync(u => u.Id == overrideByUserId))
                throw new InvalidOperationException($"Override user with ID {overrideByUserId} not found.");

            salesInvoice.OverrideByUserId = overrideByUserId;
            salesInvoice.OverrideDate = DateTime.Now;

            _unitOfWork.Sales.Update(salesInvoice);
            await _unitOfWork.SaveChangesAsync();

            return true;
        }

        public async Task<bool> ProcessReturnAsync(int salesInvoiceId, int productId, int quantity, string reason, int userId)
        {
            var salesInvoice = await _unitOfWork.Sales.GetWithDetailsAsync(salesInvoiceId);
            if (salesInvoice == null)
                return false;

            var detail = salesInvoice.SalesInvoiceDetails.FirstOrDefault(d => d.ProductId == productId);
            if (detail == null)
                throw new InvalidOperationException("Product not found in the sales invoice.");

            if (quantity > detail.Quantity)
                throw new InvalidOperationException("Return quantity cannot exceed sold quantity.");

            await _unitOfWork.BeginTransactionAsync();
            try
            {
                // Create sales return record
                var salesReturn = new SalesReturn
                {
                    SalesInvoiceId = salesInvoiceId,
                    ProductId = productId,
                    Quantity = quantity,
                    Reason = reason,
                    UserId = userId,
                    CreatedAt = DateTime.Now
                };

                await _unitOfWork.SalesReturns.AddAsync(salesReturn);

                // Create inventory movement (positive quantity for return)
                var inventoryLog = new InventoryLog
                {
                    ProductId = productId,
                    MovementType = "return_sale",
                    Quantity = quantity, // Positive for incoming
                    UnitCost = detail.Product.DefaultCostPrice,
                    ReferenceTable = "sales_returns",
                    ReferenceId = salesReturn.Id,
                    Note = $"Sales return - Invoice #{salesInvoice.InvoiceNumber}, Reason: {reason}",
                    UserId = userId,
                    CreatedAt = DateTime.Now
                };

                await _unitOfWork.Inventory.AddAsync(inventoryLog);
                await _unitOfWork.SaveChangesAsync();
                await _unitOfWork.CommitTransactionAsync();

                return true;
            }
            catch
            {
                await _unitOfWork.RollbackTransactionAsync();
                throw;
            }
        }

        public async Task<IEnumerable<object>> GetSalesReturnsAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = await _unitOfWork.SalesReturns.GetWithIncludeAsync(
                sr => sr.SalesInvoice, sr => sr.Product, sr => sr.User);

            var filteredReturns = query.AsQueryable();

            if (fromDate.HasValue)
                filteredReturns = filteredReturns.Where(sr => sr.CreatedAt >= fromDate.Value);

            if (toDate.HasValue)
                filteredReturns = filteredReturns.Where(sr => sr.CreatedAt <= toDate.Value);

            return filteredReturns.Select(sr => new
            {
                Id = sr.Id,
                InvoiceNumber = sr.SalesInvoice.InvoiceNumber,
                ProductName = sr.Product.Name,
                Quantity = sr.Quantity,
                Reason = sr.Reason,
                UserName = sr.User.Username,
                ReturnDate = sr.CreatedAt
            }).ToList();
        }

        public async Task<bool> SalesInvoiceExistsAsync(int id)
        {
            return await _unitOfWork.Sales.AnyAsync(s => s.Id == id);
        }

        public async Task<bool> InvoiceNumberExistsAsync(string invoiceNumber)
        {
            return await _unitOfWork.Sales.AnyAsync(s => s.InvoiceNumber == invoiceNumber);
        }

        public async Task<bool> CanDeleteSalesInvoiceAsync(int id)
        {
            // Check if sales invoice has returns
            var hasReturns = await _unitOfWork.SalesReturns.AnyAsync(sr => sr.SalesInvoiceId == id);
            return !hasReturns;
        }

        public async Task<(IEnumerable<SalesInvoiceDto> Sales, int TotalCount)> GetPagedSalesInvoicesAsync(
            int pageNumber, int pageSize, SalesSearchDto? searchDto = null)
        {
            var (sales, totalCount) = await _unitOfWork.Sales.GetPagedSalesAsync(
                pageNumber, pageSize, searchDto?.SearchTerm, searchDto?.FromDate, searchDto?.ToDate);

            var salesDtos = sales.Select(MapToSalesInvoiceDto);
            return (salesDtos, totalCount);
        }

        public async Task<object> GetSalesStatisticsAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var totalAmount = await GetTotalSalesAmountAsync(fromDate, toDate);
            var totalCount = await GetTotalSalesCountAsync(fromDate, toDate);
            var averageAmount = totalCount > 0 ? await GetAverageSaleAmountAsync(fromDate, toDate) : 0;
            var totalDiscounts = await GetTotalDiscountsAsync(fromDate, toDate);

            var paymentMethodStats = await GetSalesByPaymentMethodAsync(fromDate, toDate);

            return new
            {
                TotalSales = totalCount,
                TotalAmount = totalAmount,
                AverageAmount = averageAmount,
                TotalDiscounts = totalDiscounts,
                NetAmount = totalAmount - totalDiscounts,
                PaymentMethodBreakdown = paymentMethodStats
            };
        }
    }
}
