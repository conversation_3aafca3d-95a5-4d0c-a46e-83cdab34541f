using ElectronicsStore.Application.DTOs;

namespace ElectronicsStore.Application.Services
{
    public interface ISupplierService
    {
        // Basic CRUD operations
        Task<SupplierDto?> GetSupplierByIdAsync(int id);
        Task<IEnumerable<SupplierDto>> GetAllSuppliersAsync();
        Task<SupplierDto> CreateSupplierAsync(CreateSupplierDto createSupplierDto);
        Task<SupplierDto> UpdateSupplierAsync(UpdateSupplierDto updateSupplierDto);
        Task<bool> DeleteSupplierAsync(int id);

        // Search and filtering
        Task<IEnumerable<SupplierDto>> SearchSuppliersAsync(SupplierSearchDto searchDto);
        Task<IEnumerable<SupplierDto>> GetSuppliersByNameAsync(string name);

        // Validation
        Task<bool> SupplierExistsAsync(int id);
        Task<bool> SupplierNameExistsAsync(string name, int? excludeId = null);
        Task<bool> CanDeleteSupplierAsync(int id);

        // Analytics
        Task<IEnumerable<SupplierDto>> GetTopSuppliersAsync(int count = 10);
        Task<object> GetSupplierStatisticsAsync();
        Task<IEnumerable<object>> GetSupplierProductsAsync(int supplierId);

        // Pagination
        Task<(IEnumerable<SupplierDto> Suppliers, int TotalCount)> GetPagedSuppliersAsync(
            int pageNumber, int pageSize, SupplierSearchDto? searchDto = null);
    }
}
