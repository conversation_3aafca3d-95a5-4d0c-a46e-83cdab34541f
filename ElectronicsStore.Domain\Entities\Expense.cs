using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ElectronicsStore.Domain.Entities
{
    [Table("expenses")]
    public class Expense
    {
        [Key]
        [Column("id")]
        public int Id { get; set; }

        [Required]
        [MaxLength(100)]
        [Column("expense_type")]
        public string ExpenseType { get; set; } = string.Empty;

        [Column("amount", TypeName = "decimal(12,2)")]
        public decimal Amount { get; set; }

        [MaxLength(200)]
        [Column("note")]
        public string? Note { get; set; }

        [Column("user_id")]
        public int UserId { get; set; }

        [Column("created_at")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation properties
        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;
    }
}
