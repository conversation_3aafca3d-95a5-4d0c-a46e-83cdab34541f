using Microsoft.EntityFrameworkCore;
using ElectronicsStore.Domain.Entities;

namespace ElectronicsStore.Persistence
{
    public class ElectronicsDbContext : DbContext
    {
        public ElectronicsDbContext(DbContextOptions<ElectronicsDbContext> options) : base(options)
        {
        }

        // DbSets for all entities
        public DbSet<Category> Categories { get; set; }
        public DbSet<Supplier> Suppliers { get; set; }
        public DbSet<Role> Roles { get; set; }
        public DbSet<Permission> Permissions { get; set; }
        public DbSet<RolePermission> RolePermissions { get; set; }
        public DbSet<User> Users { get; set; }
        public DbSet<Product> Products { get; set; }
        public DbSet<PurchaseInvoice> PurchaseInvoices { get; set; }
        public DbSet<PurchaseInvoiceDetail> PurchaseInvoiceDetails { get; set; }
        public DbSet<SalesInvoice> SalesInvoices { get; set; }
        public DbSet<SalesInvoiceDetail> SalesInvoiceDetails { get; set; }
        public DbSet<InventoryLog> InventoryLogs { get; set; }
        public DbSet<SalesReturn> SalesReturns { get; set; }
        public DbSet<PurchaseReturn> PurchaseReturns { get; set; }
        public DbSet<Expense> Expenses { get; set; }

        // Views (commented out to work with existing database)
        // public DbSet<InventoryView> InventoryViews { get; set; }
        // public DbSet<InventoryValuationView> InventoryValuationViews { get; set; }
        // public DbSet<CogsView> CogsViews { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure composite key for RolePermission
            modelBuilder.Entity<RolePermission>()
                .HasKey(rp => new { rp.RoleId, rp.PermissionId });

            // Configure relationships
            ConfigureRelationships(modelBuilder);

            // Configure views as keyless entities (commented out)
            // ConfigureViews(modelBuilder);

            // Configure computed columns
            ConfigureComputedColumns(modelBuilder);
        }

        private void ConfigureRelationships(ModelBuilder modelBuilder)
        {
            // User - Role relationship
            modelBuilder.Entity<User>()
                .HasOne(u => u.Role)
                .WithMany(r => r.Users)
                .HasForeignKey(u => u.RoleId)
                .OnDelete(DeleteBehavior.Restrict);

            // Product - Category relationship
            modelBuilder.Entity<Product>()
                .HasOne(p => p.Category)
                .WithMany(c => c.Products)
                .HasForeignKey(p => p.CategoryId)
                .OnDelete(DeleteBehavior.Restrict);

            // Product - Supplier relationship
            modelBuilder.Entity<Product>()
                .HasOne(p => p.Supplier)
                .WithMany(s => s.Products)
                .HasForeignKey(p => p.SupplierId)
                .OnDelete(DeleteBehavior.SetNull);

            // SalesInvoice - User relationships
            modelBuilder.Entity<SalesInvoice>()
                .HasOne(si => si.User)
                .WithMany(u => u.SalesInvoices)
                .HasForeignKey(si => si.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<SalesInvoice>()
                .HasOne(si => si.OverrideByUser)
                .WithMany(u => u.OverriddenSalesInvoices)
                .HasForeignKey(si => si.OverrideByUserId)
                .OnDelete(DeleteBehavior.SetNull);

            // PurchaseInvoice relationships
            modelBuilder.Entity<PurchaseInvoice>()
                .HasOne(pi => pi.Supplier)
                .WithMany(s => s.PurchaseInvoices)
                .HasForeignKey(pi => pi.SupplierId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<PurchaseInvoice>()
                .HasOne(pi => pi.User)
                .WithMany(u => u.PurchaseInvoices)
                .HasForeignKey(pi => pi.UserId)
                .OnDelete(DeleteBehavior.Restrict);
        }

        // private void ConfigureViews(ModelBuilder modelBuilder)
        // {
        //     // Configure views as read-only
        //     modelBuilder.Entity<InventoryView>().ToView("inventory_view");
        //     modelBuilder.Entity<InventoryValuationView>().ToView("inventory_valuation_view");
        //     modelBuilder.Entity<CogsView>().ToView("cogs_view");
        // }

        private void ConfigureComputedColumns(ModelBuilder modelBuilder)
        {
            // Configure computed columns
            modelBuilder.Entity<PurchaseInvoiceDetail>()
                .Property(p => p.LineTotal)
                .HasComputedColumnSql("[quantity] * [unit_cost]")
                .ValueGeneratedOnAddOrUpdate();

            modelBuilder.Entity<SalesInvoiceDetail>()
                .Property(s => s.LineTotal)
                .HasComputedColumnSql("([unit_price] - [discount_amount]) * [quantity]")
                .ValueGeneratedOnAddOrUpdate();

            // Configure to handle SQL Server triggers properly
            modelBuilder.Entity<SalesInvoiceDetail>()
                .HasAnnotation("SqlServer:UseSqlOutputClause", false);

            modelBuilder.Entity<PurchaseInvoiceDetail>()
                .HasAnnotation("SqlServer:UseSqlOutputClause", false);
        }
    }
}
