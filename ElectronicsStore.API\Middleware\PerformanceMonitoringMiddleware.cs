using System.Diagnostics;
using System.Text.Json;

namespace ElectronicsStore.API.Middleware
{
    /// <summary>
    /// Middleware for monitoring API performance and collecting metrics
    /// </summary>
    public class PerformanceMonitoringMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<PerformanceMonitoringMiddleware> _logger;
        private readonly PerformanceMetricsCollector _metricsCollector;

        public PerformanceMonitoringMiddleware(
            RequestDelegate next, 
            ILogger<PerformanceMonitoringMiddleware> logger,
            PerformanceMetricsCollector metricsCollector)
        {
            _next = next;
            _logger = logger;
            _metricsCollector = metricsCollector;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            var stopwatch = Stopwatch.StartNew();
            var requestStartTime = DateTime.UtcNow;
            
            // Extract request information
            var method = context.Request.Method;
            var path = context.Request.Path;
            var userAgent = context.Request.Headers.UserAgent.ToString();
            var ipAddress = context.Connection.RemoteIpAddress?.ToString();
            var userId = GetUserIdFromContext(context);

            // Log request start
            _logger.LogInformation("Request started: {Method} {Path} by User {UserId} from IP {IPAddress}",
                method, path, userId, ipAddress);

            Exception? requestException = null;
            
            try
            {
                await _next(context);
            }
            catch (Exception ex)
            {
                requestException = ex;
                throw; // Re-throw to let global exception handler deal with it
            }
            finally
            {
                stopwatch.Stop();
                var elapsedMs = stopwatch.ElapsedMilliseconds;
                var statusCode = context.Response.StatusCode;

                // Add performance headers (only if response hasn't started)
                if (!context.Response.HasStarted)
                {
                    context.Response.Headers.TryAdd("X-Response-Time", $"{elapsedMs}ms");
                    context.Response.Headers.TryAdd("X-Request-Id", context.TraceIdentifier);
                }

                // Collect metrics
                await _metricsCollector.RecordRequestAsync(new RequestMetrics
                {
                    Method = method,
                    Path = path,
                    StatusCode = statusCode,
                    ElapsedMilliseconds = elapsedMs,
                    UserId = userId,
                    IPAddress = ipAddress,
                    UserAgent = userAgent,
                    RequestTime = requestStartTime,
                    HasException = requestException != null,
                    ExceptionType = requestException?.GetType().Name
                });

                // Log request completion
                if (requestException != null)
                {
                    _logger.LogError("Request failed: {Method} {Path} returned {StatusCode} in {ElapsedMs}ms with exception {ExceptionType}",
                        method, path, statusCode, elapsedMs, requestException.GetType().Name);
                }
                else if (statusCode >= 400)
                {
                    _logger.LogWarning("Request completed with error: {Method} {Path} returned {StatusCode} in {ElapsedMs}ms",
                        method, path, statusCode, elapsedMs);
                }
                else if (elapsedMs > 1000) // Log slow requests (> 1 second)
                {
                    _logger.LogWarning("Slow request detected: {Method} {Path} returned {StatusCode} in {ElapsedMs}ms",
                        method, path, statusCode, elapsedMs);
                }
                else
                {
                    _logger.LogInformation("Request completed: {Method} {Path} returned {StatusCode} in {ElapsedMs}ms",
                        method, path, statusCode, elapsedMs);
                }
            }
        }

        private static int? GetUserIdFromContext(HttpContext context)
        {
            try
            {
                var userIdClaim = context.User?.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ??
                                 context.User?.FindFirst("UserId")?.Value;
                
                return int.TryParse(userIdClaim, out int userId) ? userId : null;
            }
            catch
            {
                return null;
            }
        }
    }

    /// <summary>
    /// Request metrics data structure
    /// </summary>
    public class RequestMetrics
    {
        public string Method { get; set; } = string.Empty;
        public string Path { get; set; } = string.Empty;
        public int StatusCode { get; set; }
        public long ElapsedMilliseconds { get; set; }
        public int? UserId { get; set; }
        public string? IPAddress { get; set; }
        public string? UserAgent { get; set; }
        public DateTime RequestTime { get; set; }
        public bool HasException { get; set; }
        public string? ExceptionType { get; set; }
    }

    /// <summary>
    /// Service for collecting and storing performance metrics
    /// </summary>
    public class PerformanceMetricsCollector
    {
        private readonly ILogger<PerformanceMetricsCollector> _logger;
        private readonly List<RequestMetrics> _recentRequests = new();
        private readonly object _lock = new();
        private const int MaxRecentRequests = 1000; // Keep last 1000 requests in memory

        public PerformanceMetricsCollector(ILogger<PerformanceMetricsCollector> logger)
        {
            _logger = logger;
        }

        public Task RecordRequestAsync(RequestMetrics metrics)
        {
            lock (_lock)
            {
                _recentRequests.Add(metrics);
                
                // Keep only recent requests
                if (_recentRequests.Count > MaxRecentRequests)
                {
                    _recentRequests.RemoveAt(0);
                }
            }

            // Log performance metrics
            if (metrics.ElapsedMilliseconds > 5000) // Very slow requests (> 5 seconds)
            {
                _logger.LogError("Very slow request: {Method} {Path} took {ElapsedMs}ms",
                    metrics.Method, metrics.Path, metrics.ElapsedMilliseconds);
            }
            else if (metrics.ElapsedMilliseconds > 2000) // Slow requests (> 2 seconds)
            {
                _logger.LogWarning("Slow request: {Method} {Path} took {ElapsedMs}ms",
                    metrics.Method, metrics.Path, metrics.ElapsedMilliseconds);
            }

            return Task.CompletedTask;
        }

        public PerformanceStatistics GetStatistics()
        {
            lock (_lock)
            {
                if (_recentRequests.Count == 0)
                {
                    return new PerformanceStatistics();
                }

                var now = DateTime.UtcNow;
                var last5Minutes = _recentRequests.Where(r => (now - r.RequestTime).TotalMinutes <= 5).ToList();
                var lastHour = _recentRequests.Where(r => (now - r.RequestTime).TotalHours <= 1).ToList();

                return new PerformanceStatistics
                {
                    TotalRequests = _recentRequests.Count,
                    RequestsLast5Minutes = last5Minutes.Count,
                    RequestsLastHour = lastHour.Count,
                    AverageResponseTime = _recentRequests.Any() ? _recentRequests.Average(r => r.ElapsedMilliseconds) : 0,
                    AverageResponseTimeLast5Minutes = last5Minutes.Any() ? last5Minutes.Average(r => r.ElapsedMilliseconds) : 0,
                    SlowRequestsCount = _recentRequests.Count(r => r.ElapsedMilliseconds > 1000),
                    ErrorRequestsCount = _recentRequests.Count(r => r.StatusCode >= 400),
                    ExceptionRequestsCount = _recentRequests.Count(r => r.HasException),
                    TopSlowEndpoints = _recentRequests
                        .Where(r => r.ElapsedMilliseconds > 500)
                        .GroupBy(r => $"{r.Method} {r.Path}")
                        .Select(g => new EndpointPerformance
                        {
                            Endpoint = g.Key,
                            AverageResponseTime = g.Average(r => r.ElapsedMilliseconds),
                            RequestCount = g.Count(),
                            MaxResponseTime = g.Max(r => r.ElapsedMilliseconds)
                        })
                        .OrderByDescending(e => e.AverageResponseTime)
                        .Take(10)
                        .ToList(),
                    MostActiveUsers = _recentRequests
                        .Where(r => r.UserId.HasValue)
                        .GroupBy(r => r.UserId.Value)
                        .Select(g => new UserActivity
                        {
                            UserId = g.Key,
                            RequestCount = g.Count(),
                            AverageResponseTime = g.Average(r => r.ElapsedMilliseconds),
                            ErrorCount = g.Count(r => r.StatusCode >= 400)
                        })
                        .OrderByDescending(u => u.RequestCount)
                        .Take(10)
                        .ToList()
                };
            }
        }

        public List<RequestMetrics> GetRecentRequests(int count = 100)
        {
            lock (_lock)
            {
                return _recentRequests.TakeLast(count).ToList();
            }
        }
    }

    /// <summary>
    /// Performance statistics data structure
    /// </summary>
    public class PerformanceStatistics
    {
        public int TotalRequests { get; set; }
        public int RequestsLast5Minutes { get; set; }
        public int RequestsLastHour { get; set; }
        public double AverageResponseTime { get; set; }
        public double AverageResponseTimeLast5Minutes { get; set; }
        public int SlowRequestsCount { get; set; }
        public int ErrorRequestsCount { get; set; }
        public int ExceptionRequestsCount { get; set; }
        public List<EndpointPerformance> TopSlowEndpoints { get; set; } = new();
        public List<UserActivity> MostActiveUsers { get; set; } = new();
    }

    public class EndpointPerformance
    {
        public string Endpoint { get; set; } = string.Empty;
        public double AverageResponseTime { get; set; }
        public int RequestCount { get; set; }
        public long MaxResponseTime { get; set; }
    }

    public class UserActivity
    {
        public int UserId { get; set; }
        public int RequestCount { get; set; }
        public double AverageResponseTime { get; set; }
        public int ErrorCount { get; set; }
    }

    /// <summary>
    /// Extension methods for registering performance monitoring
    /// </summary>
    public static class PerformanceMonitoringExtensions
    {
        public static IServiceCollection AddPerformanceMonitoring(this IServiceCollection services)
        {
            services.AddSingleton<PerformanceMetricsCollector>();
            return services;
        }

        public static IApplicationBuilder UsePerformanceMonitoring(this IApplicationBuilder app)
        {
            return app.UseMiddleware<PerformanceMonitoringMiddleware>();
        }
    }
}
