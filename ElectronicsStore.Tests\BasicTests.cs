using Xunit;
using FluentAssertions;
using ElectronicsStore.Application.DTOs;
using ElectronicsStore.Domain.Entities;

namespace ElectronicsStore.Tests
{
    /// <summary>
    /// Basic tests to verify core functionality
    /// </summary>
    public class BasicTests
    {
        [Fact]
        public void CreateCategoryDto_ShouldHaveCorrectProperties()
        {
            // Arrange
            var categoryName = "Electronics";

            // Act
            var dto = new CreateCategoryDto { Name = categoryName };

            // Assert
            dto.Name.Should().Be(categoryName);
            dto.Name.Should().NotBeNullOrEmpty();
        }

        [Fact]
        public void Category_ShouldHaveCorrectProperties()
        {
            // Arrange
            var categoryId = 1;
            var categoryName = "Electronics";

            // Act
            var category = new Category 
            { 
                Id = categoryId, 
                Name = categoryName 
            };

            // Assert
            category.Id.Should().Be(categoryId);
            category.Name.Should().Be(categoryName);
        }

        [Fact]
        public void UpdateCategoryDto_ShouldValidateCorrectly()
        {
            // Arrange
            var dto = new UpdateCategoryDto 
            { 
                Id = 1, 
                Name = "Updated Electronics" 
            };

            // Act & Assert
            dto.Id.Should().BeGreaterThan(0);
            dto.Name.Should().NotBeNullOrEmpty();
            dto.Name.Should().Be("Updated Electronics");
        }

        [Theory]
        [InlineData("Electronics")]
        [InlineData("Clothing")]
        [InlineData("Books")]
        public void CategoryDto_ShouldAcceptValidNames(string categoryName)
        {
            // Arrange & Act
            var dto = new CategoryDto 
            { 
                Id = 1, 
                Name = categoryName 
            };

            // Assert
            dto.Name.Should().Be(categoryName);
            dto.Name.Should().NotBeNullOrEmpty();
        }

        [Fact]
        public void Product_ShouldHaveCorrectProperties()
        {
            // Arrange
            var product = new Product
            {
                Id = 1,
                Name = "Laptop",
                DefaultSellingPrice = 1000.00m,
                DefaultCostPrice = 800.00m,
                CategoryId = 1,
                Barcode = "123456789"
            };

            // Assert
            product.Id.Should().Be(1);
            product.Name.Should().Be("Laptop");
            product.DefaultSellingPrice.Should().Be(1000.00m);
            product.DefaultCostPrice.Should().Be(800.00m);
            product.CategoryId.Should().Be(1);
            product.Barcode.Should().Be("123456789");
        }

        [Fact]
        public void CreateProductDto_ShouldValidateCorrectly()
        {
            // Arrange
            var dto = new CreateProductDto
            {
                Name = "Test Product",
                CategoryId = 1,
                Barcode = "987654321"
            };

            // Assert
            dto.Name.Should().NotBeNullOrEmpty();
            dto.CategoryId.Should().BeGreaterThan(0);
            dto.Barcode.Should().NotBeNullOrEmpty();
        }

        [Theory]
        [InlineData(0)]
        [InlineData(-1)]
        [InlineData(-100)]
        public void Product_PriceShouldNotBeNegative_ValidationTest(decimal price)
        {
            // Arrange
            var product = new Product
            {
                Id = 1,
                Name = "Test Product",
                DefaultSellingPrice = price,
                CategoryId = 1
            };

            // Assert - In a real scenario, this would be validated by business logic
            if (price <= 0)
            {
                // This test demonstrates that we should validate prices
                price.Should().BeLessOrEqualTo(0);
            }
        }

        [Fact]
        public void User_ShouldHaveCorrectProperties()
        {
            // Arrange
            var user = new User
            {
                Id = 1,
                Username = "testuser",
                Password = "hashedpassword",
                RoleId = 1
            };

            // Assert
            user.Id.Should().Be(1);
            user.Username.Should().Be("testuser");
            user.Password.Should().NotBeNullOrEmpty();
            user.RoleId.Should().BeGreaterThan(0);
        }

        [Fact]
        public void Role_ShouldHaveCorrectProperties()
        {
            // Arrange
            var role = new Role
            {
                Id = 1,
                Name = "admin"
            };

            // Assert
            role.Id.Should().Be(1);
            role.Name.Should().Be("admin");
        }

        [Theory]
        [InlineData("admin")]
        [InlineData("manager")]
        [InlineData("cashier")]
        public void Role_ShouldAcceptValidRoleNames(string roleName)
        {
            // Arrange & Act
            var role = new Role { Id = 1, Name = roleName };

            // Assert
            role.Name.Should().Be(roleName);
            role.Name.Should().NotBeNullOrEmpty();
        }

        [Fact]
        public void SalesInvoice_ShouldCalculateTotalCorrectly()
        {
            // Arrange
            var invoice = new SalesInvoice
            {
                Id = 1,
                InvoiceDate = DateTime.Now,
                TotalAmount = 1500.00m,
                UserId = 1
            };

            // Assert
            invoice.TotalAmount.Should().Be(1500.00m);
            invoice.TotalAmount.Should().BeGreaterThan(0);
            invoice.InvoiceDate.Should().BeCloseTo(DateTime.Now, TimeSpan.FromMinutes(1));
        }

        [Fact]
        public void PurchaseInvoice_ShouldHaveCorrectProperties()
        {
            // Arrange
            var invoice = new PurchaseInvoice
            {
                Id = 1,
                InvoiceDate = DateTime.Now,
                TotalAmount = 800.00m,
                SupplierId = 1,
                UserId = 1
            };

            // Assert
            invoice.Id.Should().Be(1);
            invoice.TotalAmount.Should().Be(800.00m);
            invoice.SupplierId.Should().Be(1);
            invoice.UserId.Should().Be(1);
        }
    }
}
