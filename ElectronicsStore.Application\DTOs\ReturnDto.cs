namespace ElectronicsStore.Application.DTOs
{
    public class SalesReturnDto
    {
        public int Id { get; set; }
        public int SalesInvoiceId { get; set; }
        public string SalesInvoiceNumber { get; set; } = string.Empty;
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public string? ProductBarcode { get; set; }
        public int Quantity { get; set; }
        public decimal UnitPrice { get; set; } // سعر البيع الأصلي
        public decimal TotalAmount { get; set; } // إجمالي المبلغ المرتجع
        public string? Reason { get; set; }
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
    }

    public class CreateSalesReturnDto
    {
        public int SalesInvoiceId { get; set; }
        public int ProductId { get; set; }
        public int Quantity { get; set; }
        public string? Reason { get; set; }
    }

    public class PurchaseReturnDto
    {
        public int Id { get; set; }
        public int PurchaseInvoiceId { get; set; }
        public string PurchaseInvoiceNumber { get; set; } = string.Empty;
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public string? ProductBarcode { get; set; }
        public int Quantity { get; set; }
        public decimal UnitCost { get; set; } // تكلفة الشراء الأصلية
        public decimal TotalAmount { get; set; } // إجمالي المبلغ المرتجع
        public string? Reason { get; set; }
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
    }

    public class CreatePurchaseReturnDto
    {
        public int PurchaseInvoiceId { get; set; }
        public int ProductId { get; set; }
        public int Quantity { get; set; }
        public string? Reason { get; set; }
    }

    public class ReturnSearchDto
    {
        public string? ReturnType { get; set; } // "sales" or "purchase"
        public int? InvoiceId { get; set; }
        public int? ProductId { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string? Reason { get; set; }
        public int? UserId { get; set; }
    }

    public class ReturnStatisticsDto
    {
        public int TotalSalesReturns { get; set; }
        public int TotalPurchaseReturns { get; set; }
        public decimal TotalSalesReturnAmount { get; set; }
        public decimal TotalPurchaseReturnAmount { get; set; }
        public int TotalReturnedItems { get; set; }
        public decimal TodaySalesReturns { get; set; }
        public decimal TodayPurchaseReturns { get; set; }
        public decimal MonthSalesReturns { get; set; }
        public decimal MonthPurchaseReturns { get; set; }
        public decimal ReturnRate { get; set; } // نسبة المرتجعات
    }

    public class ReturnReportDto
    {
        public DateTime Date { get; set; }
        public int SalesReturnsCount { get; set; }
        public int PurchaseReturnsCount { get; set; }
        public decimal SalesReturnAmount { get; set; }
        public decimal PurchaseReturnAmount { get; set; }
        public int TotalReturnedItems { get; set; }
    }

    public class TopReturnedProductDto
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public string? ProductBarcode { get; set; }
        public int SalesReturnsCount { get; set; }
        public int PurchaseReturnsCount { get; set; }
        public int TotalReturnsCount { get; set; }
        public decimal TotalReturnAmount { get; set; }
        public string TopReturnReason { get; set; } = string.Empty;
    }

    public class ReturnReasonAnalysisDto
    {
        public string Reason { get; set; } = string.Empty;
        public int SalesReturnsCount { get; set; }
        public int PurchaseReturnsCount { get; set; }
        public int TotalCount { get; set; }
        public decimal Percentage { get; set; }
    }
}
