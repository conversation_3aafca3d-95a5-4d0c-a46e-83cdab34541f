using AutoMapper;
using ElectronicsStore.Domain.Entities;
using ElectronicsStore.Application.DTOs;

namespace ElectronicsStore.Application.Mappings
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            // User mappings
            CreateMap<User, UserDto>()
                .ForMember(dest => dest.RoleName, opt => opt.MapFrom(src => src.Role.Name));
            CreateMap<UserDto, User>()
                .ForMember(dest => dest.Role, opt => opt.Ignore());

            // Category mappings
            CreateMap<Category, CategoryDto>().ReverseMap();

            // Supplier mappings
            CreateMap<Supplier, SupplierDto>().ReverseMap();
            CreateMap<CreateSupplierDto, Supplier>();
            CreateMap<UpdateSupplierDto, Supplier>();

            // Product mappings
            CreateMap<Product, ProductDto>()
                .ForMember(dest => dest.CategoryName, opt => opt.MapFrom(src => src.Category.Name))
                .ForMember(dest => dest.SupplierName, opt => opt.MapFrom(src => src.Supplier != null ? src.Supplier.Name : null));
            CreateMap<ProductDto, Product>()
                .ForMember(dest => dest.Category, opt => opt.Ignore())
                .ForMember(dest => dest.Supplier, opt => opt.Ignore());

            // Sales Invoice mappings
            CreateMap<SalesInvoice, SalesInvoiceDto>()
                .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.User.Username))
                .ForMember(dest => dest.OverrideByUserName, opt => opt.MapFrom(src => src.OverrideByUser != null ? src.OverrideByUser.Username : null));
            CreateMap<SalesInvoiceDto, SalesInvoice>()
                .ForMember(dest => dest.User, opt => opt.Ignore())
                .ForMember(dest => dest.OverrideByUser, opt => opt.Ignore());

            // Sales Invoice Detail mappings
            CreateMap<SalesInvoiceDetail, SalesInvoiceDetailDto>()
                .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product.Name));
            CreateMap<SalesInvoiceDetailDto, SalesInvoiceDetail>()
                .ForMember(dest => dest.Product, opt => opt.Ignore());

            // Inventory mappings
            CreateMap<InventoryLog, InventoryLogDto>()
                .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product.Name))
                .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.User.Username));
            CreateMap<InventoryLogDto, InventoryLog>()
                .ForMember(dest => dest.Product, opt => opt.Ignore())
                .ForMember(dest => dest.User, opt => opt.Ignore());
        }
    }
}
