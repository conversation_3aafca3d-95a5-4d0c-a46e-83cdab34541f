using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ElectronicsStore.Domain.Entities
{
    [Table("cogs_view")]
    public class CogsView
    {
        [Key]
        [Column("sales_invoice_id")]
        public int SalesInvoiceId { get; set; }

        [Column("invoice_number")]
        public string InvoiceNumber { get; set; } = string.Empty;

        [Column("cost_of_goods_sold", TypeName = "decimal(18,2)")]
        public decimal CostOfGoodsSold { get; set; }
    }
}
