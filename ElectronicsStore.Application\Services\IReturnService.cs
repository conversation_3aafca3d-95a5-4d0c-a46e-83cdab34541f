using ElectronicsStore.Application.DTOs;

namespace ElectronicsStore.Application.Services
{
    public interface IReturnService
    {
        // Sales Returns
        Task<SalesReturnDto> CreateSalesReturnAsync(CreateSalesReturnDto createDto, int userId);
        Task<SalesReturnDto?> GetSalesReturnByIdAsync(int id);
        Task<IEnumerable<SalesReturnDto>> GetAllSalesReturnsAsync();
        Task<IEnumerable<SalesReturnDto>> GetSalesReturnsByInvoiceAsync(int salesInvoiceId);
        Task<IEnumerable<SalesReturnDto>> GetSalesReturnsByProductAsync(int productId);
        Task<IEnumerable<SalesReturnDto>> GetSalesReturnsByUserAsync(int userId);
        Task<bool> DeleteSalesReturnAsync(int id);

        // Purchase Returns
        Task<PurchaseReturnDto> CreatePurchaseReturnAsync(CreatePurchaseReturnDto createDto, int userId);
        Task<PurchaseReturnDto?> GetPurchaseReturnByIdAsync(int id);
        Task<IEnumerable<PurchaseReturnDto>> GetAllPurchaseReturnsAsync();
        Task<IEnumerable<PurchaseReturnDto>> GetPurchaseReturnsByInvoiceAsync(int purchaseInvoiceId);
        Task<IEnumerable<PurchaseReturnDto>> GetPurchaseReturnsByProductAsync(int productId);
        Task<IEnumerable<PurchaseReturnDto>> GetPurchaseReturnsByUserAsync(int userId);
        Task<bool> DeletePurchaseReturnAsync(int id);

        // Combined operations
        Task<IEnumerable<object>> GetAllReturnsAsync(); // Combined sales and purchase returns
        Task<IEnumerable<object>> SearchReturnsAsync(ReturnSearchDto searchDto);
        Task<IEnumerable<object>> GetReturnsByDateRangeAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<object>> GetTodayReturnsAsync();

        // Statistics and reports
        Task<ReturnStatisticsDto> GetReturnStatisticsAsync();
        Task<IEnumerable<ReturnReportDto>> GetDailyReturnReportAsync(DateTime date);
        Task<IEnumerable<ReturnReportDto>> GetMonthlyReturnReportAsync(int year, int month);
        Task<IEnumerable<ReturnReportDto>> GetYearlyReturnReportAsync(int year);

        // Analytics
        Task<IEnumerable<TopReturnedProductDto>> GetTopReturnedProductsAsync(int count = 10);
        Task<IEnumerable<ReturnReasonAnalysisDto>> GetReturnReasonAnalysisAsync();
        Task<decimal> GetReturnRateAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<object> GetReturnTrendsAsync(int months = 12);

        // Validation
        Task<bool> CanReturnSalesItemAsync(int salesInvoiceId, int productId, int quantity);
        Task<bool> CanReturnPurchaseItemAsync(int purchaseInvoiceId, int productId, int quantity);
        Task<int> GetMaxReturnableQuantityAsync(int invoiceId, int productId, string returnType);

        // Pagination
        Task<(IEnumerable<SalesReturnDto> Returns, int TotalCount)> GetPagedSalesReturnsAsync(
            int pageNumber, int pageSize, ReturnSearchDto? searchDto = null);
        Task<(IEnumerable<PurchaseReturnDto> Returns, int TotalCount)> GetPagedPurchaseReturnsAsync(
            int pageNumber, int pageSize, ReturnSearchDto? searchDto = null);

        // Invoice-specific operations
        Task<object> GetInvoiceReturnSummaryAsync(int invoiceId, string returnType);
        Task<bool> HasReturnsAsync(int invoiceId, string returnType);
        Task<decimal> GetInvoiceReturnAmountAsync(int invoiceId, string returnType);
    }
}
