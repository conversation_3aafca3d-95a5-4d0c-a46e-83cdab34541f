using System.ComponentModel.DataAnnotations;

namespace ElectronicsStore.Application.DTOs
{
    public class SupplierDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Phone { get; set; }
        public string? Email { get; set; }
        public string? Address { get; set; }
        public int ProductCount { get; set; } // عدد المنتجات المرتبطة
    }

    public class CreateSupplierDto
    {
        [Required(ErrorMessage = "اسم المورد مطلوب")]
        [MaxLength(100, ErrorMessage = "اسم المورد لا يمكن أن يتجاوز 100 حرف")]
        [MinLength(2, ErrorMessage = "اسم المورد يجب أن يكون على الأقل حرفين")]
        public string Name { get; set; } = string.Empty;

        [MaxLength(20, ErrorMessage = "رقم الهاتف لا يمكن أن يتجاوز 20 حرف")]
        [RegularExpression(@"^[\+]?[0-9\-\(\)\s]+$", ErrorMessage = "رقم الهاتف غير صحيح")]
        public string? Phone { get; set; }

        [MaxLength(100, ErrorMessage = "البريد الإلكتروني لا يمكن أن يتجاوز 100 حرف")]
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        public string? Email { get; set; }

        [MaxLength(200, ErrorMessage = "العنوان لا يمكن أن يتجاوز 200 حرف")]
        public string? Address { get; set; }
    }

    public class UpdateSupplierDto
    {
        [Range(1, int.MaxValue, ErrorMessage = "معرف المورد مطلوب")]
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم المورد مطلوب")]
        [MaxLength(100, ErrorMessage = "اسم المورد لا يمكن أن يتجاوز 100 حرف")]
        [MinLength(2, ErrorMessage = "اسم المورد يجب أن يكون على الأقل حرفين")]
        public string Name { get; set; } = string.Empty;

        [MaxLength(20, ErrorMessage = "رقم الهاتف لا يمكن أن يتجاوز 20 حرف")]
        [RegularExpression(@"^[\+]?[0-9\-\(\)\s]+$", ErrorMessage = "رقم الهاتف غير صحيح")]
        public string? Phone { get; set; }

        [MaxLength(100, ErrorMessage = "البريد الإلكتروني لا يمكن أن يتجاوز 100 حرف")]
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        public string? Email { get; set; }

        [MaxLength(200, ErrorMessage = "العنوان لا يمكن أن يتجاوز 200 حرف")]
        public string? Address { get; set; }
    }

    public class SupplierSearchDto
    {
        public string? SearchTerm { get; set; }
        public string? Phone { get; set; }
        public string? Email { get; set; }
    }
}
