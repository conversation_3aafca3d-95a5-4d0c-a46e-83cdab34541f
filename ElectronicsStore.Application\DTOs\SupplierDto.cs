namespace ElectronicsStore.Application.DTOs
{
    public class SupplierDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Phone { get; set; }
        public string? Email { get; set; }
        public string? Address { get; set; }
        public int ProductCount { get; set; } // عدد المنتجات المرتبطة
    }

    public class CreateSupplierDto
    {
        public string Name { get; set; } = string.Empty;
        public string? Phone { get; set; }
        public string? Email { get; set; }
        public string? Address { get; set; }
    }

    public class UpdateSupplierDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Phone { get; set; }
        public string? Email { get; set; }
        public string? Address { get; set; }
    }

    public class SupplierSearchDto
    {
        public string? SearchTerm { get; set; }
        public string? Phone { get; set; }
        public string? Email { get; set; }
    }
}
