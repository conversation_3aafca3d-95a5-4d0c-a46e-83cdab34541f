using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System.Security.Claims;
using ElectronicsStore.Application.DTOs;
using ElectronicsStore.Application.Services;
using ElectronicsStore.Application.Configuration;

namespace ElectronicsStore.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly IUserService _userService;
        private readonly IJwtService _jwtService;
        private readonly JwtSettings _jwtSettings;

        public AuthController(
            IUserService userService, 
            IJwtService jwtService,
            IOptions<JwtSettings> jwtSettings)
        {
            _userService = userService;
            _jwtService = jwtService;
            _jwtSettings = jwtSettings.Value;
        }

        /// <summary>
        /// User login
        /// </summary>
        /// <param name="loginDto">Login credentials</param>
        /// <returns>JWT token and user information</returns>
        [HttpPost("login")]
        public async Task<ActionResult<LoginResponseDto>> Login(LoginDto loginDto)
        {
            try
            {
                // Validate credentials
                var isValid = await _userService.ValidateUserCredentialsAsync(loginDto.Username, loginDto.Password);
                if (!isValid)
                    return Unauthorized("Invalid username or password.");

                // Get user with role
                var user = await _userService.GetUserByUsernameAsync(loginDto.Username);
                if (user == null)
                    return Unauthorized("User not found.");

                // Generate tokens
                var token = _jwtService.GenerateToken(new Domain.Entities.User 
                { 
                    Id = user.Id, 
                    Username = user.Username, 
                    RoleId = user.RoleId 
                }, user.RoleName);
                
                var refreshToken = _jwtService.GenerateRefreshToken();

                var response = new LoginResponseDto
                {
                    Token = token,
                    RefreshToken = refreshToken,
                    User = user,
                    ExpiresAt = DateTime.UtcNow.AddMinutes(_jwtSettings.ExpirationInMinutes)
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest($"Login failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Get current authenticated user information
        /// </summary>
        /// <returns>Current user details</returns>
        [HttpGet("me")]
        [Authorize]
        public async Task<ActionResult<UserDto>> GetCurrentUser()
        {
            try
            {
                var userIdClaim = User.FindFirst("UserId")?.Value;
                if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
                    return Unauthorized("Invalid token.");

                var user = await _userService.GetUserByIdAsync(userId);
                if (user == null)
                    return NotFound("User not found.");

                return Ok(user);
            }
            catch (Exception ex)
            {
                return BadRequest($"Failed to get current user: {ex.Message}");
            }
        }

        /// <summary>
        /// Refresh JWT token
        /// </summary>
        /// <param name="refreshTokenDto">Current token and refresh token</param>
        /// <returns>New JWT token</returns>
        [HttpPost("refresh")]
        public async Task<ActionResult<RefreshTokenResponseDto>> RefreshToken(RefreshTokenDto refreshTokenDto)
        {
            try
            {
                var principal = _jwtService.GetPrincipalFromExpiredToken(refreshTokenDto.Token);
                if (principal == null)
                    return BadRequest("Invalid token.");

                var userIdClaim = principal.FindFirst("UserId")?.Value;
                if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
                    return BadRequest("Invalid token claims.");

                var user = await _userService.GetUserByIdAsync(userId);
                if (user == null)
                    return NotFound("User not found.");

                // Generate new tokens
                var newToken = _jwtService.GenerateToken(new Domain.Entities.User 
                { 
                    Id = user.Id, 
                    Username = user.Username, 
                    RoleId = user.RoleId 
                }, user.RoleName);
                
                var newRefreshToken = _jwtService.GenerateRefreshToken();

                var response = new RefreshTokenResponseDto
                {
                    Token = newToken,
                    RefreshToken = newRefreshToken,
                    ExpiresAt = DateTime.UtcNow.AddMinutes(_jwtSettings.ExpirationInMinutes)
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest($"Token refresh failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Validate token
        /// </summary>
        /// <param name="token">JWT token to validate</param>
        /// <returns>Token validation result</returns>
        [HttpPost("validate")]
        public IActionResult ValidateToken([FromBody] string token)
        {
            try
            {
                var isValid = _jwtService.ValidateToken(token);
                return Ok(new { IsValid = isValid });
            }
            catch (Exception ex)
            {
                return BadRequest($"Token validation failed: {ex.Message}");
            }
        }
    }
}
