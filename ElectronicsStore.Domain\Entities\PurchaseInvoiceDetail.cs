using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ElectronicsStore.Domain.Entities
{
    [Table("purchase_invoice_details")]
    public class PurchaseInvoiceDetail
    {
        [Key]
        [Column("id")]
        public int Id { get; set; }

        [Column("purchase_invoice_id")]
        public int PurchaseInvoiceId { get; set; }

        [Column("product_id")]
        public int ProductId { get; set; }

        [Column("quantity")]
        public int Quantity { get; set; }

        [Column("unit_cost", TypeName = "decimal(10,2)")]
        public decimal UnitCost { get; set; }

        [Column("line_total", TypeName = "decimal(10,2)")]
        [DatabaseGenerated(DatabaseGeneratedOption.Computed)]
        public decimal LineTotal { get; set; }

        // Navigation properties
        [ForeignKey("PurchaseInvoiceId")]
        public virtual PurchaseInvoice PurchaseInvoice { get; set; } = null!;

        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; } = null!;
    }
}
