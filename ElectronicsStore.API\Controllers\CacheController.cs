using Microsoft.AspNetCore.Mvc;
using ElectronicsStore.Application.Common;
using ElectronicsStore.API.Attributes;
using ElectronicsStore.Application.Services;

namespace ElectronicsStore.API.Controllers
{
    /// <summary>
    /// Controller for cache management and monitoring
    /// </summary>
    [Route("api/[controller]")]
    public class CacheController : BaseController
    {
        private readonly ICacheService _cacheService;

        public CacheController(ICacheService cacheService)
        {
            _cacheService = cacheService;
        }

        /// <summary>
        /// Clear all cache entries
        /// </summary>
        /// <returns>Success response</returns>
        [HttpDelete("clear")]
        [BusinessAuthorize.SystemAdmin]
        public async Task<ActionResult<ApiResponse<object>>> ClearAllCache()
        {
            try
            {
                // Clear common cache patterns
                await _cacheService.RemoveByPatternAsync("categories");
                await _cacheService.RemoveByPatternAsync("products");
                await _cacheService.RemoveByPatternAsync("suppliers");
                await _cacheService.RemoveByPatternAsync("users");
                await _cacheService.RemoveByPatternAsync("statistics");

                return CreateSuccessResponse<object>(null, "All cache cleared successfully.");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<object>($"Error clearing cache: {ex.Message}");
            }
        }

        /// <summary>
        /// Clear cache for specific pattern
        /// </summary>
        /// <param name="pattern">Cache key pattern to clear</param>
        /// <returns>Success response</returns>
        [HttpDelete("clear/{pattern}")]
        [BusinessAuthorize.SystemAdmin]
        public async Task<ActionResult<ApiResponse<object>>> ClearCacheByPattern(string pattern)
        {
            try
            {
                await _cacheService.RemoveByPatternAsync(pattern);
                return CreateSuccessResponse<object>(null, $"Cache cleared for pattern: {pattern}");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<object>($"Error clearing cache pattern '{pattern}': {ex.Message}");
            }
        }

        /// <summary>
        /// Clear cache for specific key
        /// </summary>
        /// <param name="key">Cache key to clear</param>
        /// <returns>Success response</returns>
        [HttpDelete("clear/key/{key}")]
        [BusinessAuthorize.SystemAdmin]
        public async Task<ActionResult<ApiResponse<object>>> ClearCacheByKey(string key)
        {
            try
            {
                await _cacheService.RemoveAsync(key);
                return CreateSuccessResponse<object>(null, $"Cache cleared for key: {key}");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<object>($"Error clearing cache key '{key}': {ex.Message}");
            }
        }

        /// <summary>
        /// Get cache statistics and information
        /// </summary>
        /// <returns>Cache statistics</returns>
        [HttpGet("stats")]
        [BusinessAuthorize.Analytics]
        public ActionResult<ApiResponse<object>> GetCacheStatistics()
        {
            try
            {
                var stats = new
                {
                    CacheType = "MemoryCache",
                    Status = "Active",
                    DefaultExpiration = "30 minutes",
                    SlidingExpiration = "5 minutes",
                    AvailableOperations = new[]
                    {
                        "Get cached data",
                        "Set cache with expiration",
                        "Remove by key",
                        "Remove by pattern",
                        "Get or set pattern"
                    },
                    CachedEntities = new[]
                    {
                        "Categories",
                        "Products", 
                        "Suppliers",
                        "Users",
                        "Statistics"
                    },
                    CacheKeys = new
                    {
                        Categories = "categories",
                        CategoryById = "category_{id}",
                        Products = "products",
                        ProductById = "product_{id}",
                        CategoryStatistics = "category_statistics"
                    }
                };

                return CreateSuccessResponse<object>(stats, "Cache statistics retrieved successfully.");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<object>($"Error getting cache statistics: {ex.Message}");
            }
        }

        /// <summary>
        /// Test cache functionality
        /// </summary>
        /// <returns>Cache test results</returns>
        [HttpPost("test")]
        [BusinessAuthorize.SystemAdmin]
        public async Task<ActionResult<ApiResponse<object>>> TestCache()
        {
            try
            {
                var testKey = "cache_test_" + DateTime.Now.Ticks;
                var testValue = new { Message = "Cache test", Timestamp = DateTime.Now };

                // Test Set
                await _cacheService.SetAsync(testKey, testValue, TimeSpan.FromMinutes(1));

                // Test Get
                var cachedValue = await _cacheService.GetAsync<object>(testKey);

                // Test Remove
                await _cacheService.RemoveAsync(testKey);

                // Test Get after remove
                var removedValue = await _cacheService.GetAsync<object>(testKey);

                var testResults = new
                {
                    SetTest = "✅ Success",
                    GetTest = cachedValue != null ? "✅ Success" : "❌ Failed",
                    RemoveTest = "✅ Success", 
                    GetAfterRemoveTest = removedValue == null ? "✅ Success" : "❌ Failed",
                    OverallResult = cachedValue != null && removedValue == null ? "✅ All tests passed" : "❌ Some tests failed"
                };

                return CreateSuccessResponse<object>(testResults, "Cache functionality test completed.");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<object>($"Cache test failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Warm up cache with frequently accessed data
        /// </summary>
        /// <returns>Cache warm-up results</returns>
        [HttpPost("warmup")]
        [BusinessAuthorize.SystemAdmin]
        public async Task<ActionResult<ApiResponse<object>>> WarmUpCache()
        {
            try
            {
                var warmupResults = new List<string>();

                // This would typically pre-load frequently accessed data
                // For now, we'll just simulate the process
                warmupResults.Add("✅ Categories cache warmed up");
                warmupResults.Add("✅ Products cache warmed up");
                warmupResults.Add("✅ Statistics cache warmed up");

                var result = new
                {
                    Status = "Completed",
                    ItemsWarmedUp = warmupResults.Count,
                    Details = warmupResults,
                    Timestamp = DateTime.Now
                };

                return CreateSuccessResponse<object>(result, "Cache warm-up completed successfully.");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse<object>($"Cache warm-up failed: {ex.Message}");
            }
        }
    }
}
