using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ElectronicsStore.Application.DTOs;
using ElectronicsStore.Application.Services;

namespace ElectronicsStore.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class SalesController : ControllerBase
    {
        private readonly ISalesService _salesService;

        public SalesController(ISalesService salesService)
        {
            _salesService = salesService;
        }

        /// <summary>
        /// Get all sales invoices
        /// </summary>
        /// <returns>List of sales invoices</returns>
        [HttpGet]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<SalesInvoiceDto>>> GetSalesInvoices()
        {
            var salesInvoices = await _salesService.GetAllSalesInvoicesAsync();
            return Ok(salesInvoices);
        }

        /// <summary>
        /// Get sales invoice by ID
        /// </summary>
        /// <param name="id">Sales invoice ID</param>
        /// <returns>Sales invoice details</returns>
        [HttpGet("{id}")]
        [Authorize(Roles = "admin,manager,cashier")]
        public async Task<ActionResult<SalesInvoiceDto>> GetSalesInvoice(int id)
        {
            var salesInvoice = await _salesService.GetSalesInvoiceByIdAsync(id);
            if (salesInvoice == null)
                return NotFound($"Sales invoice with ID {id} not found.");

            return Ok(salesInvoice);
        }

        /// <summary>
        /// Get sales invoice by invoice number
        /// </summary>
        /// <param name="invoiceNumber">Invoice number</param>
        /// <returns>Sales invoice details</returns>
        [HttpGet("number/{invoiceNumber}")]
        [Authorize(Roles = "admin,manager,cashier")]
        public async Task<ActionResult<SalesInvoiceDto>> GetSalesInvoiceByNumber(string invoiceNumber)
        {
            var salesInvoice = await _salesService.GetSalesInvoiceByNumberAsync(invoiceNumber);
            if (salesInvoice == null)
                return NotFound($"Sales invoice with number '{invoiceNumber}' not found.");

            return Ok(salesInvoice);
        }

        /// <summary>
        /// Create a new sales invoice
        /// </summary>
        /// <param name="createSalesInvoiceDto">Sales invoice creation data</param>
        /// <returns>Created sales invoice</returns>
        [HttpPost]
        [Authorize(Roles = "admin,manager,cashier")]
        public async Task<ActionResult<SalesInvoiceDto>> CreateSalesInvoice(CreateSalesInvoiceDto createSalesInvoiceDto)
        {
            try
            {
                // Validate model state
                if (!ModelState.IsValid)
                {
                    var errors = ModelState
                        .Where(x => x.Value?.Errors.Count > 0)
                        .ToDictionary(
                            kvp => kvp.Key,
                            kvp => kvp.Value?.Errors.Select(e => e.ErrorMessage).ToArray()
                        );
                    return BadRequest(new { message = "Validation failed", errors });
                }

                // Set user ID from token if not provided or invalid
                if (createSalesInvoiceDto.UserId <= 0)
                {
                    var userIdClaim = User.FindFirst("UserId")?.Value;
                    if (int.TryParse(userIdClaim, out int userId))
                    {
                        createSalesInvoiceDto.UserId = userId;
                    }
                    else
                    {
                        return BadRequest("معرف المستخدم غير صحيح في الرمز المميز");
                    }
                }

                var salesInvoice = await _salesService.CreateSalesInvoiceAsync(createSalesInvoiceDto);
                return CreatedAtAction(nameof(GetSalesInvoice), new { id = salesInvoice.Id }, salesInvoice);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                // Log the exception (you might want to use ILogger here)
                return StatusCode(500, new { message = "حدث خطأ داخلي في الخادم", details = ex.Message });
            }
        }

        /// <summary>
        /// Delete a sales invoice
        /// </summary>
        /// <param name="id">Sales invoice ID</param>
        /// <returns>Success status</returns>
        [HttpDelete("{id}")]
        [Authorize(Roles = "admin")]
        public async Task<IActionResult> DeleteSalesInvoice(int id)
        {
            try
            {
                var result = await _salesService.DeleteSalesInvoiceAsync(id);
                if (!result)
                    return NotFound($"Sales invoice with ID {id} not found.");

                return NoContent();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Search sales invoices
        /// </summary>
        /// <param name="searchDto">Search criteria</param>
        /// <returns>List of matching sales invoices</returns>
        [HttpPost("search")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<SalesInvoiceDto>>> SearchSalesInvoices(SalesSearchDto searchDto)
        {
            var salesInvoices = await _salesService.SearchSalesInvoicesAsync(searchDto);
            return Ok(salesInvoices);
        }

        /// <summary>
        /// Get sales invoices by customer
        /// </summary>
        /// <param name="customerName">Customer name</param>
        /// <returns>List of customer's sales invoices</returns>
        [HttpGet("customer/{customerName}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<SalesInvoiceDto>>> GetSalesInvoicesByCustomer(string customerName)
        {
            var salesInvoices = await _salesService.GetSalesInvoicesByCustomerAsync(customerName);
            return Ok(salesInvoices);
        }

        /// <summary>
        /// Get sales invoices by user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>List of user's sales invoices</returns>
        [HttpGet("user/{userId}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<SalesInvoiceDto>>> GetSalesInvoicesByUser(int userId)
        {
            var salesInvoices = await _salesService.GetSalesInvoicesByUserAsync(userId);
            return Ok(salesInvoices);
        }

        /// <summary>
        /// Get today's sales
        /// </summary>
        /// <returns>List of today's sales invoices</returns>
        [HttpGet("today")]
        [Authorize(Roles = "admin,manager,cashier")]
        public async Task<ActionResult<IEnumerable<SalesInvoiceDto>>> GetTodaysSales()
        {
            var salesInvoices = await _salesService.GetTodaysSalesAsync();
            return Ok(salesInvoices);
        }

        /// <summary>
        /// Get sales statistics
        /// </summary>
        /// <param name="fromDate">Start date (optional)</param>
        /// <param name="toDate">End date (optional)</param>
        /// <returns>Sales statistics</returns>
        [HttpGet("statistics")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<object>> GetSalesStatistics([FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
        {
            var statistics = await _salesService.GetSalesStatisticsAsync(fromDate, toDate);
            return Ok(statistics);
        }

        /// <summary>
        /// Get daily sales report
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <returns>Daily sales report</returns>
        [HttpGet("reports/daily")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<SalesReportDto>>> GetDailySalesReport([FromQuery] DateTime fromDate, [FromQuery] DateTime toDate)
        {
            try
            {
                // Validate date range
                if (fromDate == default(DateTime) || toDate == default(DateTime))
                {
                    return BadRequest(new { message = "تواريخ البداية والنهاية مطلوبة" });
                }

                if (fromDate > toDate)
                {
                    return BadRequest(new { message = "تاريخ البداية يجب أن يكون قبل تاريخ النهاية" });
                }

                // Limit date range to prevent performance issues
                var daysDifference = (toDate - fromDate).Days;
                if (daysDifference > 365)
                {
                    return BadRequest(new { message = "نطاق التاريخ لا يمكن أن يتجاوز 365 يوم" });
                }

                var report = await _salesService.GetDailySalesReportAsync(fromDate, toDate);
                return Ok(new { success = true, data = report, message = "تم استرجاع التقرير بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ أثناء إنشاء التقرير", details = ex.Message });
            }
        }

        /// <summary>
        /// Get monthly sales report
        /// </summary>
        /// <param name="year">Year</param>
        /// <returns>Monthly sales report</returns>
        [HttpGet("reports/monthly/{year}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<SalesReportDto>>> GetMonthlySalesReport(int year)
        {
            try
            {
                // Validate year
                if (year < 2020 || year > DateTime.Now.Year + 1)
                {
                    return BadRequest(new { message = $"السنة يجب أن تكون بين 2020 و {DateTime.Now.Year + 1}" });
                }

                var report = await _salesService.GetMonthlySalesReportAsync(year);
                return Ok(new { success = true, data = report, message = "تم استرجاع التقرير بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ أثناء إنشاء التقرير", details = ex.Message });
            }
        }

        /// <summary>
        /// Get sales by payment method
        /// </summary>
        /// <param name="fromDate">Start date (optional)</param>
        /// <param name="toDate">End date (optional)</param>
        /// <returns>Sales breakdown by payment method</returns>
        [HttpGet("reports/payment-methods")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<object>>> GetSalesByPaymentMethod([FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
        {
            try
            {
                // Validate date range if provided
                if (fromDate.HasValue && toDate.HasValue && fromDate > toDate)
                {
                    return BadRequest(new { message = "تاريخ البداية يجب أن يكون قبل تاريخ النهاية" });
                }

                var report = await _salesService.GetSalesByPaymentMethodAsync(fromDate, toDate);
                return Ok(new { success = true, data = report, message = "تم استرجاع التقرير بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ أثناء إنشاء التقرير", details = ex.Message });
            }
        }

        /// <summary>
        /// Get top customers
        /// </summary>
        /// <param name="limit">Number of customers to return</param>
        /// <param name="fromDate">Start date (optional)</param>
        /// <param name="toDate">End date (optional)</param>
        /// <returns>List of top customers</returns>
        [HttpGet("reports/top-customers")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<TopCustomerDto>>> GetTopCustomers(
            [FromQuery] int limit = 10, [FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
        {
            try
            {
                // Validate limit
                if (limit <= 0 || limit > 100)
                {
                    return BadRequest(new { message = "عدد العملاء يجب أن يكون بين 1 و 100" });
                }

                // Validate date range if provided
                if (fromDate.HasValue && toDate.HasValue && fromDate > toDate)
                {
                    return BadRequest(new { message = "تاريخ البداية يجب أن يكون قبل تاريخ النهاية" });
                }

                var topCustomers = await _salesService.GetTopCustomersAsync(limit, fromDate, toDate);
                return Ok(topCustomers);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ أثناء استرجاع أفضل العملاء", details = ex.Message });
            }
        }

        /// <summary>
        /// Get top selling products
        /// </summary>
        /// <param name="limit">Number of products to return</param>
        /// <param name="fromDate">Start date (optional)</param>
        /// <param name="toDate">End date (optional)</param>
        /// <returns>List of top selling products</returns>
        [HttpGet("reports/top-products")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<TopProductDto>>> GetTopSellingProducts(
            [FromQuery] int limit = 10, [FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
        {
            try
            {
                // Validate limit
                if (limit <= 0 || limit > 100)
                {
                    return BadRequest(new { message = "عدد المنتجات يجب أن يكون بين 1 و 100" });
                }

                // Validate date range if provided
                if (fromDate.HasValue && toDate.HasValue && fromDate > toDate)
                {
                    return BadRequest(new { message = "تاريخ البداية يجب أن يكون قبل تاريخ النهاية" });
                }

                var topProducts = await _salesService.GetTopSellingProductsAsync(limit, fromDate, toDate);
                return Ok(topProducts);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ أثناء استرجاع أفضل المنتجات", details = ex.Message });
            }
        }

        /// <summary>
        /// Override a sales invoice
        /// </summary>
        /// <param name="id">Sales invoice ID</param>
        /// <param name="reason">Override reason</param>
        /// <returns>Success status</returns>
        [HttpPost("{id}/override")]
        [Authorize(Roles = "admin,manager")]
        public async Task<IActionResult> OverrideSale(int id, [FromBody] string reason)
        {
            try
            {
                var currentUserIdClaim = User.FindFirst("UserId")?.Value;
                if (string.IsNullOrEmpty(currentUserIdClaim) || !int.TryParse(currentUserIdClaim, out int currentUserId))
                    return Unauthorized("Invalid token.");

                var result = await _salesService.OverrideSaleAsync(id, currentUserId, reason);
                if (!result)
                    return NotFound($"Sales invoice with ID {id} not found.");

                return Ok(new { Message = "Sales invoice overridden successfully." });
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Process a sales return
        /// </summary>
        /// <param name="salesInvoiceId">Sales invoice ID</param>
        /// <param name="request">Return request data</param>
        /// <returns>Success status</returns>
        [HttpPost("{salesInvoiceId}/return")]
        [Authorize(Roles = "admin,manager,cashier")]
        public async Task<IActionResult> ProcessReturn(int salesInvoiceId, [FromBody] object request)
        {
            try
            {
                var currentUserIdClaim = User.FindFirst("UserId")?.Value;
                if (string.IsNullOrEmpty(currentUserIdClaim) || !int.TryParse(currentUserIdClaim, out int currentUserId))
                    return Unauthorized("Invalid token.");

                // Parse request body
                var requestData = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(request.ToString()!);
                var productId = Convert.ToInt32(requestData!["productId"]);
                var quantity = Convert.ToInt32(requestData["quantity"]);
                var reason = requestData["reason"].ToString() ?? "";

                var result = await _salesService.ProcessReturnAsync(salesInvoiceId, productId, quantity, reason, currentUserId);
                if (!result)
                    return NotFound($"Sales invoice with ID {salesInvoiceId} not found.");

                return Ok(new { Message = "Return processed successfully." });
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Get paged sales invoices
        /// </summary>
        /// <param name="pageNumber">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="searchDto">Search criteria (optional)</param>
        /// <returns>Paged list of sales invoices</returns>
        [HttpGet("paged")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<object>> GetPagedSalesInvoices(
            [FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10, [FromQuery] SalesSearchDto? searchDto = null)
        {
            var (sales, totalCount) = await _salesService.GetPagedSalesInvoicesAsync(pageNumber, pageSize, searchDto);
            
            return Ok(new
            {
                Sales = sales,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            });
        }
    }
}
