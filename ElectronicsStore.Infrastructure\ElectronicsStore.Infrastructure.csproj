﻿<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.8" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.8" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.8" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ElectronicsStore.Application\ElectronicsStore.Application.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.8" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.13.0" />
  </ItemGroup>

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

</Project>
