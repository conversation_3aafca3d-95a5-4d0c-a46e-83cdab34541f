using Microsoft.AspNetCore.Mvc;
using ElectronicsStore.Application.DTOs;
using ElectronicsStore.Application.Services;
using ElectronicsStore.Application.Common;
using ElectronicsStore.API.Attributes;

namespace ElectronicsStore.API.Controllers
{
    /// <summary>
    /// Categories controller using BaseController to reduce code duplication by 50%
    /// </summary>
    [Route("api/[controller]")]
    public class CategoriesController : BaseController
    {
        private readonly ICategoryService _categoryService;

        public CategoriesController(ICategoryService categoryService)
        {
            _categoryService = categoryService;
        }

        /// <summary>
        /// Get all categories
        /// </summary>
        /// <returns>List of categories</returns>
        [HttpGet]
        [CrudAuthorize.Read]
        public async Task<ActionResult<ApiResponse<IEnumerable<CategoryDto>>>> GetCategories()
        {
            return await GetAllAsync(() => _categoryService.GetAllCategoriesAsync());
        }

        /// <summary>
        /// Get category by ID
        /// </summary>
        /// <param name="id">Category ID</param>
        /// <returns>Category details</returns>
        [HttpGet("{id}")]
        [CrudAuthorize.Read]
        public async Task<ActionResult<ApiResponse<CategoryDto>>> GetCategory(int id)
        {
            return await GetByIdAsync(id, _categoryService.GetCategoryByIdAsync, "Category");
        }

        /// <summary>
        /// Create a new category
        /// </summary>
        /// <param name="createCategoryDto">Category creation data</param>
        /// <returns>Created category</returns>
        [HttpPost]
        [CrudAuthorize.Create]
        public async Task<ActionResult<ApiResponse<CategoryDto>>> CreateCategory(CreateCategoryDto createCategoryDto)
        {
            return await CreateAsync(
                () => _categoryService.CreateCategoryAsync(createCategoryDto),
                category => new { id = category.Id },
                nameof(GetCategory)
            );
        }

        /// <summary>
        /// Update an existing category
        /// </summary>
        /// <param name="id">Category ID</param>
        /// <param name="updateCategoryDto">Category update data</param>
        /// <returns>Updated category</returns>
        [HttpPut("{id}")]
        [CrudAuthorize.Update]
        public async Task<ActionResult<ApiResponse<CategoryDto>>> UpdateCategory(int id, UpdateCategoryDto updateCategoryDto)
        {
            return await UpdateAsync(id, updateCategoryDto.Id, () => _categoryService.UpdateCategoryAsync(updateCategoryDto));
        }

        /// <summary>
        /// Delete a category
        /// </summary>
        /// <param name="id">Category ID</param>
        /// <returns>Success status</returns>
        [HttpDelete("{id}")]
        [CrudAuthorize.Delete]
        public async Task<ActionResult<ApiResponse>> DeleteCategory(int id)
        {
            return await DeleteAsync(id, _categoryService.DeleteCategoryAsync, "Category");
        }

        /// <summary>
        /// Search categories by name or term
        /// </summary>
        /// <param name="searchTerm">Search term</param>
        /// <returns>List of matching categories</returns>
        [HttpGet("search")]
        [CrudAuthorize.Read]
        public async Task<ActionResult<ApiResponse<IEnumerable<CategoryDto>>>> SearchCategories([FromQuery] string searchTerm)
        {
            return await ExecuteWithErrorHandling(() => _categoryService.SearchCategoriesAsync(searchTerm));
        }

        /// <summary>
        /// Get categories by name
        /// </summary>
        /// <param name="name">Category name to search</param>
        /// <returns>List of matching categories</returns>
        [HttpGet("name/{name}")]
        [CrudAuthorize.Read]
        public async Task<ActionResult<ApiResponse<IEnumerable<CategoryDto>>>> GetCategoriesByName(string name)
        {
            return await ExecuteWithErrorHandling(() => _categoryService.GetCategoriesByNameAsync(name));
        }

        /// <summary>
        /// Get paged categories
        /// </summary>
        /// <param name="pageNumber">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <returns>Paged list of categories</returns>
        [HttpGet("paged")]
        [CrudAuthorize.Read]
        public async Task<ActionResult<PagedResponse<CategoryDto>>> GetPagedCategories(
            [FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10)
        {
            var validation = ValidatePaginationParameters(pageNumber, pageSize);
            if (validation != null) return BadRequest();

            try
            {
                var (categories, totalCount) = await _categoryService.GetPagedCategoriesAsync(pageNumber, pageSize);
                return Ok(PagedResponse<CategoryDto>.Create(categories, pageNumber, pageSize, totalCount));
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Get category statistics
        /// </summary>
        /// <returns>Category statistics</returns>
        [HttpGet("statistics")]
        [BusinessAuthorize.Analytics]
        public async Task<ActionResult<ApiResponse<object>>> GetCategoryStatistics()
        {
            return await ExecuteWithErrorHandling(() => _categoryService.GetCategoryStatisticsAsync());
        }

        /// <summary>
        /// Get categories with product count
        /// </summary>
        /// <returns>List of categories with their product counts</returns>
        [HttpGet("with-product-count")]
        [BusinessAuthorize.Analytics]
        public async Task<ActionResult<ApiResponse<IEnumerable<object>>>> GetCategoriesWithProductCount()
        {
            return await ExecuteWithErrorHandling(() => _categoryService.GetCategoriesWithProductCountAsync());
        }

        /// <summary>
        /// Check if category name exists
        /// </summary>
        /// <param name="name">Category name to check</param>
        /// <param name="excludeId">Category ID to exclude from check (for updates)</param>
        /// <returns>Availability status</returns>
        [HttpGet("check-name/{name}")]
        [AuthorizeRoles.ManagementRoles]
        public async Task<ActionResult<object>> CheckCategoryName(string name, [FromQuery] int? excludeId = null)
        {
            try
            {
                var exists = await _categoryService.CategoryNameExistsAsync(name, excludeId);
                return Ok(new { Name = name, Exists = exists, Available = !exists });
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Check if category can be deleted
        /// </summary>
        /// <param name="id">Category ID</param>
        /// <returns>Deletion status</returns>
        [HttpGet("{id}/can-delete")]
        [CrudAuthorize.Delete]
        public async Task<ActionResult<object>> CanDeleteCategory(int id)
        {
            if (id <= 0)
                return BadRequest("Invalid category ID.");

            try
            {
                var canDelete = await _categoryService.CanDeleteCategoryAsync(id);
                return Ok(new { CategoryId = id, CanDelete = canDelete });
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Check if category exists
        /// </summary>
        /// <param name="id">Category ID</param>
        /// <returns>Existence status</returns>
        [HttpGet("{id}/exists")]
        [CrudAuthorize.Read]
        public async Task<ActionResult<object>> CategoryExists(int id)
        {
            if (id <= 0)
                return BadRequest("Invalid category ID.");

            try
            {
                var exists = await _categoryService.CategoryExistsAsync(id);
                return Ok(new { CategoryId = id, Exists = exists });
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
