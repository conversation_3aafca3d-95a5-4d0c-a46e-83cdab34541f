{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"ElectronicsStore.Persistence/1.0.0": {"dependencies": {"ElectronicsStore.Application": "1.0.0", "Microsoft.EntityFrameworkCore.SqlServer": "9.0.8"}, "runtime": {"ElectronicsStore.Persistence.dll": {}}}, "AutoMapper/12.0.1": {"dependencies": {"Microsoft.CSharp": "4.7.0"}, "runtime": {"lib/netstandard2.1/AutoMapper.dll": {"assemblyVersion": "12.0.0.0", "fileVersion": "12.0.1.0"}}}, "AutoMapper.Extensions.Microsoft.DependencyInjection/12.0.1": {"dependencies": {"AutoMapper": "12.0.1", "Microsoft.Extensions.Options": "9.0.8"}, "runtime": {"lib/netstandard2.1/AutoMapper.Extensions.Microsoft.DependencyInjection.dll": {"assemblyVersion": "12.0.0.0", "fileVersion": "12.0.1.0"}}}, "Azure.Core/1.38.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.ClientModel": "1.0.0", "System.Diagnostics.DiagnosticSource": "9.0.8", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "9.0.8", "System.Text.Json": "9.0.8", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "1.38.0.0", "fileVersion": "1.3800.24.12602"}}}, "Azure.Identity/1.11.4": {"dependencies": {"Azure.Core": "1.38.0", "Microsoft.Identity.Client": "4.61.3", "Microsoft.Identity.Client.Extensions.Msal": "4.61.3", "System.Memory": "4.5.4", "System.Security.Cryptography.ProtectedData": "6.0.0", "System.Text.Json": "9.0.8", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "1.11.4.0", "fileVersion": "1.1100.424.31005"}}}, "BCrypt.Net-Next/4.0.3": {"runtime": {"lib/net6.0/BCrypt.Net-Next.dll": {"assemblyVersion": "4.0.3.0", "fileVersion": "4.0.3.0"}}}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.21406"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Data.SqlClient/5.1.6": {"dependencies": {"Azure.Identity": "1.11.4", "Microsoft.Data.SqlClient.SNI.runtime": "5.1.1", "Microsoft.Identity.Client": "4.61.3", "Microsoft.IdentityModel.JsonWebTokens": "6.35.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.35.0", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "6.0.1", "System.Diagnostics.DiagnosticSource": "9.0.8", "System.Runtime.Caching": "6.0.0", "System.Security.Cryptography.Cng": "5.0.0", "System.Security.Principal.Windows": "5.0.0", "System.Text.Encoding.CodePages": "6.0.0", "System.Text.Encodings.Web": "9.0.8"}, "runtime": {"lib/net6.0/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "5.16.24240.5"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.16.24240.5"}, "runtimes/win/lib/net6.0/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.16.24240.5"}}}, "Microsoft.Data.SqlClient.SNI.runtime/5.1.1": {"runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*******"}}}, "Microsoft.EntityFrameworkCore/9.0.8": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.8", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.8", "Microsoft.Extensions.Caching.Memory": "9.0.8", "Microsoft.Extensions.Logging": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "9.0.8.0", "fileVersion": "9.0.825.36802"}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.8": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "9.0.8.0", "fileVersion": "9.0.825.36802"}}}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.8": {}, "Microsoft.EntityFrameworkCore.Relational/9.0.8": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.8", "Microsoft.Extensions.Caching.Memory": "9.0.8", "Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.Logging": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "9.0.8.0", "fileVersion": "9.0.825.36802"}}}, "Microsoft.EntityFrameworkCore.SqlServer/9.0.8": {"dependencies": {"Microsoft.Data.SqlClient": "5.1.6", "Microsoft.EntityFrameworkCore.Relational": "9.0.8", "Microsoft.Extensions.Caching.Memory": "9.0.8", "Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.Logging": "9.0.8", "System.Formats.Asn1": "9.0.8", "System.Text.Json": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {"assemblyVersion": "9.0.8.0", "fileVersion": "9.0.825.36802"}}}, "Microsoft.Extensions.Caching.Abstractions/9.0.8": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Caching.Memory/9.0.8": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.8", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Logging.Abstractions": "9.0.8", "Microsoft.Extensions.Options": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.8": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.DependencyInjection/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.8": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Logging/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.8", "Microsoft.Extensions.Logging.Abstractions": "9.0.8", "Microsoft.Extensions.Options": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "System.Diagnostics.DiagnosticSource": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Options/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Primitives/9.0.8": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Identity.Client/4.61.3": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0", "System.Diagnostics.DiagnosticSource": "9.0.8"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.61.3.0", "fileVersion": "4.61.3.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"dependencies": {"Microsoft.Identity.Client": "4.61.3", "System.Security.Cryptography.ProtectedData": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "4.61.3.0", "fileVersion": "4.61.3.0"}}}, "Microsoft.IdentityModel.Abstractions/6.35.0": {"runtime": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "6.35.0", "System.Text.Encoding": "4.3.0", "System.Text.Encodings.Web": "9.0.8", "System.Text.Json": "9.0.8"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Logging/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Protocols/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "6.35.0", "Microsoft.IdentityModel.Tokens": "6.35.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "6.35.0", "System.IdentityModel.Tokens.Jwt": "6.35.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Tokens/6.35.0": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Microsoft.IdentityModel.Logging": "6.35.0", "System.Security.Cryptography.Cng": "5.0.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.SqlServer.Server/1.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Win32.SystemEvents/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.ClientModel/1.0.0": {"dependencies": {"System.Memory.Data": "1.0.2", "System.Text.Json": "9.0.8"}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.24.5302"}}}, "System.Configuration.ConfigurationManager/6.0.1": {"dependencies": {"System.Security.Cryptography.ProtectedData": "6.0.0", "System.Security.Permissions": "6.0.0"}, "runtime": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.922.41905"}}}, "System.Diagnostics.DiagnosticSource/9.0.8": {"runtime": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "System.Drawing.Common/6.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}, "runtime": {"lib/net6.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/System.Drawing.Common.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}, "runtimes/win/lib/net6.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Formats.Asn1/9.0.8": {"runtime": {"lib/net8.0/System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "System.IdentityModel.Tokens.Jwt/6.35.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.35.0", "Microsoft.IdentityModel.Tokens": "6.35.0"}, "runtime": {"lib/net6.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "System.IO.Pipelines/9.0.8": {"runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "System.Memory/4.5.4": {}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "9.0.8", "System.Text.Json": "9.0.8"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.221.20802"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.Caching/6.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "6.0.1"}, "runtime": {"lib/net6.0/System.Runtime.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Security.AccessControl/6.0.0": {}, "System.Security.Cryptography.Cng/5.0.0": {"dependencies": {"System.Formats.Asn1": "9.0.8"}}, "System.Security.Cryptography.ProtectedData/6.0.0": {"runtime": {"lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Permissions/6.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Windows.Extensions": "6.0.0"}, "runtime": {"lib/net6.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Principal.Windows/5.0.0": {}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.CodePages/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Encodings.Web/9.0.8": {"runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}, "runtimeTargets": {"runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll": {"rid": "browser", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "System.Text.Json/9.0.8": {"dependencies": {"System.IO.Pipelines": "9.0.8", "System.Text.Encodings.Web": "9.0.8"}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.Windows.Extensions/6.0.0": {"dependencies": {"System.Drawing.Common": "6.0.0"}, "runtime": {"lib/net6.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "ElectronicsStore.Application/1.0.0": {"dependencies": {"AutoMapper": "12.0.1", "AutoMapper.Extensions.Microsoft.DependencyInjection": "12.0.1", "BCrypt.Net-Next": "4.0.3", "ElectronicsStore.Domain": "1.0.0", "Microsoft.EntityFrameworkCore": "9.0.8"}, "runtime": {"ElectronicsStore.Application.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "ElectronicsStore.Domain/1.0.0": {"runtime": {"ElectronicsStore.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"ElectronicsStore.Persistence/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AutoMapper/12.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-hvV62vl6Hp/WfQ24yzo3Co9+OPl8wH8hApwVtgWpiAynVJkUcs7xvehnSftawL8Pe8FrPffBRM3hwzLQqWDNjA==", "path": "automapper/12.0.1", "hashPath": "automapper.12.0.1.nupkg.sha512"}, "AutoMapper.Extensions.Microsoft.DependencyInjection/12.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-+g/K+Vpe3gGMKGzjslMOdqNlkikScDjWfVvmWTayrDHaG/n2pPmFBMa+jKX1r/h6BDGFdkyRjAuhFE3ykW+r1g==", "path": "automapper.extensions.microsoft.dependencyinjection/12.0.1", "hashPath": "automapper.extensions.microsoft.dependencyinjection.12.0.1.nupkg.sha512"}, "Azure.Core/1.38.0": {"type": "package", "serviceable": true, "sha512": "sha512-IuEgCoVA0ef7E4pQtpC3+TkPbzaoQfa77HlfJDmfuaJUCVJmn7fT0izamZiryW5sYUFKizsftIxMkXKbgIcPMQ==", "path": "azure.core/1.38.0", "hashPath": "azure.core.1.38.0.nupkg.sha512"}, "Azure.Identity/1.11.4": {"type": "package", "serviceable": true, "sha512": "sha512-Sf4BoE6Q3jTgFkgBkx7qztYOFELBCo+wQgpYDwal/qJ1unBH73ywPztIJKXBXORRzAeNijsuxhk94h0TIMvfYg==", "path": "azure.identity/1.11.4", "hashPath": "azure.identity.1.11.4.nupkg.sha512"}, "BCrypt.Net-Next/4.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-W+U9WvmZQgi5cX6FS5GDtDoPzUCV4LkBLkywq/kRZhuDwcbavOzcDAr3LXJFqHUi952Yj3LEYoWW0jbEUQChsA==", "path": "bcrypt.net-next/4.0.3", "hashPath": "bcrypt.net-next.4.0.3.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-yuvf07qFWFqtK3P/MRkEKLhn5r2UbSpVueRziSqj0yJQIKFwG1pq9mOayK3zE5qZCTs0CbrwL9M6R8VwqyGy2w==", "path": "microsoft.bcl.asyncinterfaces/1.1.1", "hashPath": "microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/5.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-+pz7gIPh5ydsBcQvivt4R98PwJXer86fyQBBToIBLxZ5kuhW4N13Ijz87s9WpuPtF1vh4JesYCgpDPAOgkMhdg==", "path": "microsoft.data.sqlclient/5.1.6", "hashPath": "microsoft.data.sqlclient.5.1.6.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/5.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-wNGM5ZTQCa2blc9ikXQouybGiyMd6IHPVJvAlBEPtr6JepZEOYeDxGyprYvFVeOxlCXs7avridZQ0nYkHzQWCQ==", "path": "microsoft.data.sqlclient.sni.runtime/5.1.1", "hashPath": "microsoft.data.sqlclient.sni.runtime.5.1.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-bNGdPhN762+BIIO5MFYLjafRqkSS1MqLOc/erd55InvLnFxt9H3N5JNsuag1ZHyBor1VtD42U0CHpgqkWeAYgQ==", "path": "microsoft.entityframeworkcore/9.0.8", "hashPath": "microsoft.entityframeworkcore.9.0.8.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-B2yfAIQRRAQ4zvvWqh+HudD+juV3YoLlpXnrog3tU0PM9AFpuq6xo0+mEglN1P43WgdcUiF+65CWBcZe35s15Q==", "path": "microsoft.entityframeworkcore.abstractions/9.0.8", "hashPath": "microsoft.entityframeworkcore.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-2EYStCXt4Hi9p3J3EYMQbItJDtASJd064Kcs8C8hj8Jt5srILrR9qlaL0Ryvk8NrWQoCQvIELsmiuqLEZMLvGA==", "path": "microsoft.entityframeworkcore.analyzers/9.0.8", "hashPath": "microsoft.entityframeworkcore.analyzers.9.0.8.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-OVhfyxiHxMvYpwQ8Jy3YZi4koy6TK5/Q7C1oq3z6db+HEGuu6x9L1BX5zDIdJxxlRePMyO4D8ORiXj/D7+MUqw==", "path": "microsoft.entityframeworkcore.relational/9.0.8", "hashPath": "microsoft.entityframeworkcore.relational.9.0.8.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.SqlServer/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-yNZJIdLQTTHj6FTv9+IUQwmQvOwvUanTBOG1ibeTaaB1zfTtOqrSFQnjMOkcKOgxu+ofsBEDcuctb/f5xj/Oog==", "path": "microsoft.entityframeworkcore.sqlserver/9.0.8", "hashPath": "microsoft.entityframeworkcore.sqlserver.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-4h7bsVoKoiK+SlPM+euX/ayGnKZhl47pPCidLTiio9xyG+vgVVfcYxcYQgjm0SCrdSxjG0EGIAKF8EFr3G8Ifw==", "path": "microsoft.extensions.caching.abstractions/9.0.8", "hashPath": "microsoft.extensions.caching.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-grR+oPyj8HVn4DT8CFUUdSw2pZZKS13KjytFe4txpHQliGM1GEDotohmjgvyl3hm7RFB3FRqvbouEX3/1ewp5A==", "path": "microsoft.extensions.caching.memory/9.0.8", "hashPath": "microsoft.extensions.caching.memory.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-yNou2KM35RvzOh4vUFtl2l33rWPvOCoba+nzEDJ+BgD8aOL/jew4WPCibQvntRfOJ2pJU8ARygSMD+pdjvDHuA==", "path": "microsoft.extensions.configuration.abstractions/9.0.8", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-JJjI2Fa+QtZcUyuNjbKn04OjIUX5IgFGFu/Xc+qvzh1rXdZHLcnqqVXhR4093bGirTwacRlHiVg1XYI9xum6QQ==", "path": "microsoft.extensions.dependencyinjection/9.0.8", "hashPath": "microsoft.extensions.dependencyinjection.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-xY3lTjj4+ZYmiKIkyWitddrp1uL5uYiweQjqo4BKBw01ZC4HhcfgLghDpPZcUlppgWAFqFy9SgkiYWOMx365pw==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.8", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-Z/7ze+0iheT7FJeZPqJKARYvyC2bmwu3whbm/48BJjdlGVvgDguoCqJIkI/67NkroTYobd5geai1WheNQvWrgA==", "path": "microsoft.extensions.logging/9.0.8", "hashPath": "microsoft.extensions.logging.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-pYnAffJL7ARD/HCnnPvnFKSIHnTSmWz84WIlT9tPeQ4lHNiu0Az7N/8itihWvcF8sT+VVD5lq8V+ckMzu4SbOw==", "path": "microsoft.extensions.logging.abstractions/9.0.8", "hashPath": "microsoft.extensions.logging.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-OmTaQ0v4gxGQkehpwWIqPoEiwsPuG/u4HUsbOFoWGx4DKET2AXzopnFe/fE608FIhzc/kcg2p8JdyMRCCUzitQ==", "path": "microsoft.extensions.options/9.0.8", "hashPath": "microsoft.extensions.options.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-tizSIOEsIgSNSSh+hKeUVPK7xmTIjR8s+mJWOu1KXV3htvNQiPMFRMO17OdI1y/4ZApdBVk49u/08QGC9yvLug==", "path": "microsoft.extensions.primitives/9.0.8", "hashPath": "microsoft.extensions.primitives.9.0.8.nupkg.sha512"}, "Microsoft.Identity.Client/4.61.3": {"type": "package", "serviceable": true, "sha512": "sha512-naJo/Qm35Caaoxp5utcw+R8eU8ZtLz2ALh8S+gkekOYQ1oazfCQMWVT4NJ/FnHzdIJlm8dMz0oMpMGCabx5odA==", "path": "microsoft.identity.client/4.61.3", "hashPath": "microsoft.identity.client.4.61.3.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"type": "package", "serviceable": true, "sha512": "sha512-PWnJcznrSGr25MN8ajlc2XIDW4zCFu0U6FkpaNLEWLgd1NgFCp5uDY3mqLDgM8zCN8hqj8yo5wHYfLB2HjcdGw==", "path": "microsoft.identity.client.extensions.msal/4.61.3", "hashPath": "microsoft.identity.client.extensions.msal.4.61.3.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-xuR8E4Rd96M41CnUSCiOJ2DBh+z+zQSmyrYHdYhD6K4fXBcQGVnRCFQ0efROUYpP+p0zC1BLKr0JRpVuujTZSg==", "path": "microsoft.identitymodel.abstractions/6.35.0", "hashPath": "microsoft.identitymodel.abstractions.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-9wxai3hKgZUb4/NjdRKfQd0QJvtXKDlvmGMYACbEC8DFaicMFCFhQFZq9ZET1kJLwZahf2lfY5Gtcpsx8zYzbg==", "path": "microsoft.identitymodel.jsonwebtokens/6.35.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-jePrSfGAmqT81JDCNSY+fxVWoGuJKt9e6eJ+vT7+quVS55nWl//jGjUQn4eFtVKt4rt5dXaleZdHRB9J9AJZ7Q==", "path": "microsoft.identitymodel.logging/6.35.0", "hashPath": "microsoft.identitymodel.logging.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-BPQhlDzdFvv1PzaUxNSk+VEPwezlDEVADIKmyxubw7IiELK18uJ06RQ9QKKkds30XI+gDu9n8j24XQ8w7fjWcg==", "path": "microsoft.identitymodel.protocols/6.35.0", "hashPath": "microsoft.identitymodel.protocols.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-LMtVqnECCCdSmyFoCOxIE5tXQqkOLrvGrL7OxHg41DIm1bpWtaCdGyVcTAfOQpJXvzND9zUKIN/lhngPkYR8vg==", "path": "microsoft.identitymodel.protocols.openidconnect/6.35.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-RN7lvp7s3Boucg1NaNAbqDbxtlLj5Qeb+4uSS1TeK5FSBVM40P4DKaTKChT43sHyKfh7V0zkrMph6DdHvyA4bg==", "path": "microsoft.identitymodel.tokens/6.35.0", "hashPath": "microsoft.identitymodel.tokens.6.35.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "path": "microsoft.sqlserver.server/1.0.0", "hashPath": "microsoft.sqlserver.server.1.0.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A==", "path": "microsoft.win32.systemevents/6.0.0", "hashPath": "microsoft.win32.systemevents.6.0.0.nupkg.sha512"}, "System.ClientModel/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-I3CVkvxeqFYjIVEP59DnjbeoGNfo/+SZrCLpRz2v/g0gpCHaEMPtWSY0s9k/7jR1rAsLNg2z2u1JRB76tPjnIw==", "path": "system.clientmodel/1.0.0", "hashPath": "system.clientmodel.1.0.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-jXw9MlUu/kRfEU0WyTptAVueupqIeE3/rl0EZDMlf8pcvJnitQ8HeVEp69rZdaStXwTV72boi/Bhw8lOeO+U2w==", "path": "system.configuration.configurationmanager/6.0.1", "hashPath": "system.configuration.configurationmanager.6.0.1.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-Lj8/a1Hzli1z6jo8H9urc16GxkpVJtJM+W9fmivXMNu7nwzHziGkxn4vO0DFscMbudkEVKSezdDuHk5kgM0X/g==", "path": "system.diagnostics.diagnosticsource/9.0.8", "hashPath": "system.diagnostics.diagnosticsource.9.0.8.nupkg.sha512"}, "System.Drawing.Common/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "path": "system.drawing.common/6.0.0", "hashPath": "system.drawing.common.6.0.0.nupkg.sha512"}, "System.Formats.Asn1/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-gGL0gt2nAArsF2oOMFzClll6QN2FhtooTxEQ+K26uer4lrhahnYIo/qOn5HUSfjHlM91646L5/7dYIMJ86fHkQ==", "path": "system.formats.asn1/9.0.8", "hashPath": "system.formats.asn1.9.0.8.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-yxGIQd3BFK7F6S62/7RdZk3C/mfwyVxvh6ngd1VYMBmbJ1YZZA9+Ku6suylVtso0FjI0wbElpJ0d27CdsyLpBQ==", "path": "system.identitymodel.tokens.jwt/6.35.0", "hashPath": "system.identitymodel.tokens.jwt.6.35.0.nupkg.sha512"}, "System.IO.Pipelines/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-6vPmJt73mgUo1gzc/OcXlJvClz/2jxZ4TQPRfriVaLoGRH2mye530D9WHJYbFQRNMxF3PWCoeofsFdCyN7fLzA==", "path": "system.io.pipelines/9.0.8", "hashPath": "system.io.pipelines.9.0.8.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Caching/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-E0e03kUp5X2k+UAoVl6efmI7uU7JRBWi5EIdlQ7cr0NpBGjHG4fWII35PgsBY9T4fJQ8E4QPsL0rKksU9gcL5A==", "path": "system.runtime.caching/6.0.0", "hashPath": "system.runtime.caching.6.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Security.AccessControl/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "path": "system.security.accesscontrol/6.0.0", "hashPath": "system.security.accesscontrol.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jIMXsKn94T9JY7PvPq/tMfqa6GAaHpElRDpmG+SuL+D3+sTw2M8VhnibKnN8Tq+4JqbPJ/f+BwtLeDMEnzAvRg==", "path": "system.security.cryptography.cng/5.0.0", "hashPath": "system.security.cryptography.cng.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rp1gMNEZpvx9vP0JW0oHLxlf8oSiQgtno77Y4PLUBjSiDYoD77Y8uXHr1Ea5XG4/pIKhqAdxZ8v8OTUtqo9PeQ==", "path": "system.security.cryptography.protecteddata/6.0.0", "hashPath": "system.security.cryptography.protecteddata.6.0.0.nupkg.sha512"}, "System.Security.Permissions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T/uuc7AklkDoxmcJ7LGkyX1CcSviZuLCa4jg3PekfJ7SU0niF0IVTXwUiNVP9DSpzou2PpxJ+eNY2IfDM90ZCg==", "path": "system.security.permissions/6.0.0", "hashPath": "system.security.permissions.6.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZFCILZuOvtKPauZ/j/swhvw68ZRi9ATCfvGbk1QfydmcXBkIWecWKn/250UH7rahZ5OoDBaiAudJtPvLwzw85A==", "path": "system.text.encoding.codepages/6.0.0", "hashPath": "system.text.encoding.codepages.6.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-W+LotQsM4wBJ4PG7uRkyul4wqL4Fz7R4ty6uXrCNZUhbaHYANgrPaYR2ZpMVpdCjQEJ17Jr1NMN8hv4SHaHY4A==", "path": "system.text.encodings.web/9.0.8", "hashPath": "system.text.encodings.web.9.0.8.nupkg.sha512"}, "System.Text.Json/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-mIQir9jBqk0V7X0Nw5hzPJZC8DuGdf+2DS3jAVsr6rq5+/VyH5rza0XGcONJUWBrZ+G6BCwNyjWYd9lncBu48A==", "path": "system.text.json/9.0.8", "hashPath": "system.text.json.9.0.8.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Windows.Extensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IXoJOXIqc39AIe+CIR7koBtRGMiCt/LPM3lI+PELtDIy9XdyeSrwXFdWV9dzJ2Awl0paLWUaknLxFQ5HpHZUog==", "path": "system.windows.extensions/6.0.0", "hashPath": "system.windows.extensions.6.0.0.nupkg.sha512"}, "ElectronicsStore.Application/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "ElectronicsStore.Domain/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}