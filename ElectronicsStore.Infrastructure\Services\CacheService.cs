using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using ElectronicsStore.Application.Services;

namespace ElectronicsStore.Infrastructure.Services
{

    public class MemoryCacheService : ICacheService
    {
        private readonly IMemoryCache _memoryCache;
        private readonly ILogger<MemoryCacheService> _logger;
        private readonly HashSet<string> _cacheKeys;
        private readonly object _lock = new object();

        public MemoryCacheService(IMemoryCache memoryCache, ILogger<MemoryCacheService> logger)
        {
            _memoryCache = memoryCache;
            _logger = logger;
            _cacheKeys = new HashSet<string>();
        }

        public Task<T?> GetAsync<T>(string key) where T : class
        {
            try
            {
                if (_memoryCache.TryGetValue(key, out var cachedValue) && cachedValue is T value)
                {
                    _logger.LogDebug("Cache hit for key: {Key}", key);
                    return Task.FromResult<T?>(value);
                }

                _logger.LogDebug("Cache miss for key: {Key}", key);
                return Task.FromResult<T?>(null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cache value for key: {Key}", key);
                return Task.FromResult<T?>(null);
            }
        }

        public Task SetAsync<T>(string key, T value, TimeSpan? expiration = null) where T : class
        {
            try
            {
                var cacheEntryOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = expiration ?? TimeSpan.FromMinutes(30),
                    SlidingExpiration = TimeSpan.FromMinutes(5),
                    Priority = CacheItemPriority.Normal
                };

                cacheEntryOptions.RegisterPostEvictionCallback((key, value, reason, state) =>
                {
                    lock (_lock)
                    {
                        _cacheKeys.Remove(key.ToString()!);
                    }
                    _logger.LogDebug("Cache entry evicted. Key: {Key}, Reason: {Reason}", key, reason);
                });

                _memoryCache.Set(key, value, cacheEntryOptions);

                lock (_lock)
                {
                    _cacheKeys.Add(key);
                }

                _logger.LogDebug("Cache set for key: {Key}, Expiration: {Expiration}", key, expiration);
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting cache value for key: {Key}", key);
                return Task.CompletedTask;
            }
        }

        public Task RemoveAsync(string key)
        {
            try
            {
                _memoryCache.Remove(key);
                lock (_lock)
                {
                    _cacheKeys.Remove(key);
                }
                _logger.LogDebug("Cache removed for key: {Key}", key);
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing cache value for key: {Key}", key);
                return Task.CompletedTask;
            }
        }

        public Task RemoveByPatternAsync(string pattern)
        {
            try
            {
                List<string> keysToRemove;
                lock (_lock)
                {
                    keysToRemove = _cacheKeys.Where(key => key.Contains(pattern)).ToList();
                }

                foreach (var key in keysToRemove)
                {
                    _memoryCache.Remove(key);
                    lock (_lock)
                    {
                        _cacheKeys.Remove(key);
                    }
                }

                _logger.LogDebug("Cache removed for pattern: {Pattern}, Keys removed: {Count}", pattern, keysToRemove.Count);
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing cache values for pattern: {Pattern}", pattern);
                return Task.CompletedTask;
            }
        }

        public async Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> getItem, TimeSpan? expiration = null) where T : class
        {
            var cachedValue = await GetAsync<T>(key);
            if (cachedValue != null)
            {
                return cachedValue;
            }

            var item = await getItem();
            if (item != null)
            {
                await SetAsync(key, item, expiration);
            }

            return item;
        }
    }

    public static class CacheKeys
    {
        public const string Categories = "categories";
        public const string CategoryById = "category_{0}";
        public const string Products = "products";
        public const string ProductById = "product_{0}";
        public const string ProductsByCategory = "products_category_{0}";
        public const string Suppliers = "suppliers";
        public const string SupplierById = "supplier_{0}";
        public const string Users = "users";
        public const string UserById = "user_{0}";
        public const string Roles = "roles";
        public const string InventorySummary = "inventory_summary";
        public const string SalesStatistics = "sales_statistics_{0}";
        public const string CategoryStatistics = "category_statistics";

        public static string GetCategoryByIdKey(int id) => string.Format(CategoryById, id);
        public static string GetProductByIdKey(int id) => string.Format(ProductById, id);
        public static string GetProductsByCategoryKey(int categoryId) => string.Format(ProductsByCategory, categoryId);
        public static string GetSupplierByIdKey(int id) => string.Format(SupplierById, id);
        public static string GetUserByIdKey(int id) => string.Format(UserById, id);
        public static string GetSalesStatisticsKey(string dateRange) => string.Format(SalesStatistics, dateRange);
    }

    public static class CacheServiceExtensions
    {
        public static IServiceCollection AddCaching(this IServiceCollection services)
        {
            services.AddMemoryCache();
            services.AddSingleton<ICacheService, MemoryCacheService>();
            return services;
        }
    }
}
