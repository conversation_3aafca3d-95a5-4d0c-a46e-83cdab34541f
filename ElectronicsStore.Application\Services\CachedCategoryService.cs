using ElectronicsStore.Application.DTOs;
using ElectronicsStore.Application.Interfaces;

namespace ElectronicsStore.Application.Services
{
    // Temporarily disable cached service until Infrastructure reference is fixed
    /*
    public class CachedCategoryService : ICategoryService
    {
        private readonly ICategoryService _categoryService;
        private readonly ICacheService _cacheService;

        public CachedCategoryService(CategoryService categoryService, ICacheService cacheService)
        {
            _categoryService = categoryService;
            _cacheService = cacheService;
        }

        public async Task<IEnumerable<CategoryDto>> GetAllCategoriesAsync()
        {
            return await _cacheService.GetOrSetAsync(
                CacheKeys.Categories,
                () => _categoryService.GetAllCategoriesAsync(),
                TimeSpan.FromMinutes(30)
            );
        }

        public async Task<CategoryDto?> GetCategoryByIdAsync(int id)
        {
            var cacheKey = CacheKeys.GetCategoryByIdKey(id);
            return await _cacheService.GetOrSetAsync(
                cacheKey,
                () => _categoryService.GetCategoryByIdAsync(id),
                TimeSpan.FromMinutes(15)
            );
        }

        public async Task<CategoryDto> CreateCategoryAsync(CreateCategoryDto createCategoryDto)
        {
            var result = await _categoryService.CreateCategoryAsync(createCategoryDto);
            
            // Invalidate cache
            await _cacheService.RemoveAsync(CacheKeys.Categories);
            await _cacheService.RemoveAsync(CacheKeys.CategoryStatistics);
            
            return result;
        }

        public async Task<CategoryDto> UpdateCategoryAsync(UpdateCategoryDto updateCategoryDto)
        {
            var result = await _categoryService.UpdateCategoryAsync(updateCategoryDto);
            
            // Invalidate cache
            await _cacheService.RemoveAsync(CacheKeys.Categories);
            await _cacheService.RemoveAsync(CacheKeys.GetCategoryByIdKey(updateCategoryDto.Id));
            await _cacheService.RemoveAsync(CacheKeys.CategoryStatistics);
            
            return result;
        }

        public async Task<bool> DeleteCategoryAsync(int id)
        {
            var result = await _categoryService.DeleteCategoryAsync(id);
            
            if (result)
            {
                // Invalidate cache
                await _cacheService.RemoveAsync(CacheKeys.Categories);
                await _cacheService.RemoveAsync(CacheKeys.GetCategoryByIdKey(id));
                await _cacheService.RemoveAsync(CacheKeys.CategoryStatistics);
                await _cacheService.RemoveByPatternAsync("products_category_");
            }
            
            return result;
        }

        public async Task<bool> CategoryExistsAsync(int id)
        {
            return await _categoryService.CategoryExistsAsync(id);
        }

        public async Task<bool> CategoryNameExistsAsync(string name, int? excludeId = null)
        {
            return await _categoryService.CategoryNameExistsAsync(name, excludeId);
        }

        public async Task<IEnumerable<CategoryDto>> GetCategoriesByNameAsync(string name)
        {
            return await _categoryService.GetCategoriesByNameAsync(name);
        }

        public async Task<(IEnumerable<CategoryDto> Categories, int TotalCount)> GetPagedCategoriesAsync(int pageNumber, int pageSize)
        {
            return await _categoryService.GetPagedCategoriesAsync(pageNumber, pageSize);
        }

        public async Task<IEnumerable<CategoryDto>> SearchCategoriesAsync(string searchTerm)
        {
            return await _categoryService.SearchCategoriesAsync(searchTerm);
        }

        public async Task<object> GetCategoryStatisticsAsync()
        {
            return await _cacheService.GetOrSetAsync(
                CacheKeys.CategoryStatistics,
                () => _categoryService.GetCategoryStatisticsAsync(),
                TimeSpan.FromMinutes(60)
            );
        }

        public async Task<IEnumerable<object>> GetCategoriesWithProductCountAsync()
        {
            return await _categoryService.GetCategoriesWithProductCountAsync();
        }

        public async Task<bool> CanDeleteCategoryAsync(int id)
        {
            return await _categoryService.CanDeleteCategoryAsync(id);
        }
    }
    */
}
