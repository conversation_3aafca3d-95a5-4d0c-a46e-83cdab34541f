﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ElectronicsStore.API", "ElectronicsStore.API\ElectronicsStore.API.csproj", "{4DBFFF7C-231F-4A0A-8E38-CA36880324F8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ElectronicsStore.Domain", "ElectronicsStore.Domain\ElectronicsStore.Domain.csproj", "{C72EAE43-C9CD-4078-97C5-35D59BF98AC2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ElectronicsStore.Application", "ElectronicsStore.Application\ElectronicsStore.Application.csproj", "{7CBAC243-5AD4-485A-8F11-1DC8C1A25A7A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ElectronicsStore.Infrastructure", "ElectronicsStore.Infrastructure\ElectronicsStore.Infrastructure.csproj", "{652C3C78-AD0F-4195-9F0B-2A0C8D285192}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ElectronicsStore.Persistence", "ElectronicsStore.Persistence\ElectronicsStore.Persistence.csproj", "{49947CBF-384C-4560-BD99-5D84FB1C9A06}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ElectronicsStore.Tests", "ElectronicsStore.Tests\ElectronicsStore.Tests.csproj", "{D9CB6C56-8259-4E16-8EE6-BC19175AFB44}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{4DBFFF7C-231F-4A0A-8E38-CA36880324F8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4DBFFF7C-231F-4A0A-8E38-CA36880324F8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4DBFFF7C-231F-4A0A-8E38-CA36880324F8}.Debug|x64.ActiveCfg = Debug|Any CPU
		{4DBFFF7C-231F-4A0A-8E38-CA36880324F8}.Debug|x64.Build.0 = Debug|Any CPU
		{4DBFFF7C-231F-4A0A-8E38-CA36880324F8}.Debug|x86.ActiveCfg = Debug|Any CPU
		{4DBFFF7C-231F-4A0A-8E38-CA36880324F8}.Debug|x86.Build.0 = Debug|Any CPU
		{4DBFFF7C-231F-4A0A-8E38-CA36880324F8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4DBFFF7C-231F-4A0A-8E38-CA36880324F8}.Release|Any CPU.Build.0 = Release|Any CPU
		{4DBFFF7C-231F-4A0A-8E38-CA36880324F8}.Release|x64.ActiveCfg = Release|Any CPU
		{4DBFFF7C-231F-4A0A-8E38-CA36880324F8}.Release|x64.Build.0 = Release|Any CPU
		{4DBFFF7C-231F-4A0A-8E38-CA36880324F8}.Release|x86.ActiveCfg = Release|Any CPU
		{4DBFFF7C-231F-4A0A-8E38-CA36880324F8}.Release|x86.Build.0 = Release|Any CPU
		{C72EAE43-C9CD-4078-97C5-35D59BF98AC2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C72EAE43-C9CD-4078-97C5-35D59BF98AC2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C72EAE43-C9CD-4078-97C5-35D59BF98AC2}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C72EAE43-C9CD-4078-97C5-35D59BF98AC2}.Debug|x64.Build.0 = Debug|Any CPU
		{C72EAE43-C9CD-4078-97C5-35D59BF98AC2}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C72EAE43-C9CD-4078-97C5-35D59BF98AC2}.Debug|x86.Build.0 = Debug|Any CPU
		{C72EAE43-C9CD-4078-97C5-35D59BF98AC2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C72EAE43-C9CD-4078-97C5-35D59BF98AC2}.Release|Any CPU.Build.0 = Release|Any CPU
		{C72EAE43-C9CD-4078-97C5-35D59BF98AC2}.Release|x64.ActiveCfg = Release|Any CPU
		{C72EAE43-C9CD-4078-97C5-35D59BF98AC2}.Release|x64.Build.0 = Release|Any CPU
		{C72EAE43-C9CD-4078-97C5-35D59BF98AC2}.Release|x86.ActiveCfg = Release|Any CPU
		{C72EAE43-C9CD-4078-97C5-35D59BF98AC2}.Release|x86.Build.0 = Release|Any CPU
		{7CBAC243-5AD4-485A-8F11-1DC8C1A25A7A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7CBAC243-5AD4-485A-8F11-1DC8C1A25A7A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7CBAC243-5AD4-485A-8F11-1DC8C1A25A7A}.Debug|x64.ActiveCfg = Debug|Any CPU
		{7CBAC243-5AD4-485A-8F11-1DC8C1A25A7A}.Debug|x64.Build.0 = Debug|Any CPU
		{7CBAC243-5AD4-485A-8F11-1DC8C1A25A7A}.Debug|x86.ActiveCfg = Debug|Any CPU
		{7CBAC243-5AD4-485A-8F11-1DC8C1A25A7A}.Debug|x86.Build.0 = Debug|Any CPU
		{7CBAC243-5AD4-485A-8F11-1DC8C1A25A7A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7CBAC243-5AD4-485A-8F11-1DC8C1A25A7A}.Release|Any CPU.Build.0 = Release|Any CPU
		{7CBAC243-5AD4-485A-8F11-1DC8C1A25A7A}.Release|x64.ActiveCfg = Release|Any CPU
		{7CBAC243-5AD4-485A-8F11-1DC8C1A25A7A}.Release|x64.Build.0 = Release|Any CPU
		{7CBAC243-5AD4-485A-8F11-1DC8C1A25A7A}.Release|x86.ActiveCfg = Release|Any CPU
		{7CBAC243-5AD4-485A-8F11-1DC8C1A25A7A}.Release|x86.Build.0 = Release|Any CPU
		{652C3C78-AD0F-4195-9F0B-2A0C8D285192}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{652C3C78-AD0F-4195-9F0B-2A0C8D285192}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{652C3C78-AD0F-4195-9F0B-2A0C8D285192}.Debug|x64.ActiveCfg = Debug|Any CPU
		{652C3C78-AD0F-4195-9F0B-2A0C8D285192}.Debug|x64.Build.0 = Debug|Any CPU
		{652C3C78-AD0F-4195-9F0B-2A0C8D285192}.Debug|x86.ActiveCfg = Debug|Any CPU
		{652C3C78-AD0F-4195-9F0B-2A0C8D285192}.Debug|x86.Build.0 = Debug|Any CPU
		{652C3C78-AD0F-4195-9F0B-2A0C8D285192}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{652C3C78-AD0F-4195-9F0B-2A0C8D285192}.Release|Any CPU.Build.0 = Release|Any CPU
		{652C3C78-AD0F-4195-9F0B-2A0C8D285192}.Release|x64.ActiveCfg = Release|Any CPU
		{652C3C78-AD0F-4195-9F0B-2A0C8D285192}.Release|x64.Build.0 = Release|Any CPU
		{652C3C78-AD0F-4195-9F0B-2A0C8D285192}.Release|x86.ActiveCfg = Release|Any CPU
		{652C3C78-AD0F-4195-9F0B-2A0C8D285192}.Release|x86.Build.0 = Release|Any CPU
		{49947CBF-384C-4560-BD99-5D84FB1C9A06}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{49947CBF-384C-4560-BD99-5D84FB1C9A06}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{49947CBF-384C-4560-BD99-5D84FB1C9A06}.Debug|x64.ActiveCfg = Debug|Any CPU
		{49947CBF-384C-4560-BD99-5D84FB1C9A06}.Debug|x64.Build.0 = Debug|Any CPU
		{49947CBF-384C-4560-BD99-5D84FB1C9A06}.Debug|x86.ActiveCfg = Debug|Any CPU
		{49947CBF-384C-4560-BD99-5D84FB1C9A06}.Debug|x86.Build.0 = Debug|Any CPU
		{49947CBF-384C-4560-BD99-5D84FB1C9A06}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{49947CBF-384C-4560-BD99-5D84FB1C9A06}.Release|Any CPU.Build.0 = Release|Any CPU
		{49947CBF-384C-4560-BD99-5D84FB1C9A06}.Release|x64.ActiveCfg = Release|Any CPU
		{49947CBF-384C-4560-BD99-5D84FB1C9A06}.Release|x64.Build.0 = Release|Any CPU
		{49947CBF-384C-4560-BD99-5D84FB1C9A06}.Release|x86.ActiveCfg = Release|Any CPU
		{49947CBF-384C-4560-BD99-5D84FB1C9A06}.Release|x86.Build.0 = Release|Any CPU
		{D9CB6C56-8259-4E16-8EE6-BC19175AFB44}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D9CB6C56-8259-4E16-8EE6-BC19175AFB44}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D9CB6C56-8259-4E16-8EE6-BC19175AFB44}.Debug|x64.ActiveCfg = Debug|Any CPU
		{D9CB6C56-8259-4E16-8EE6-BC19175AFB44}.Debug|x64.Build.0 = Debug|Any CPU
		{D9CB6C56-8259-4E16-8EE6-BC19175AFB44}.Debug|x86.ActiveCfg = Debug|Any CPU
		{D9CB6C56-8259-4E16-8EE6-BC19175AFB44}.Debug|x86.Build.0 = Debug|Any CPU
		{D9CB6C56-8259-4E16-8EE6-BC19175AFB44}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D9CB6C56-8259-4E16-8EE6-BC19175AFB44}.Release|Any CPU.Build.0 = Release|Any CPU
		{D9CB6C56-8259-4E16-8EE6-BC19175AFB44}.Release|x64.ActiveCfg = Release|Any CPU
		{D9CB6C56-8259-4E16-8EE6-BC19175AFB44}.Release|x64.Build.0 = Release|Any CPU
		{D9CB6C56-8259-4E16-8EE6-BC19175AFB44}.Release|x86.ActiveCfg = Release|Any CPU
		{D9CB6C56-8259-4E16-8EE6-BC19175AFB44}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
