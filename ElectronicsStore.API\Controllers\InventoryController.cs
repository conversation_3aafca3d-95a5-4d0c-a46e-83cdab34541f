using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ElectronicsStore.Application.DTOs;
using ElectronicsStore.Application.Services;

namespace ElectronicsStore.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class InventoryController : ControllerBase
    {
        private readonly IInventoryService _inventoryService;

        public InventoryController(IInventoryService inventoryService)
        {
            _inventoryService = inventoryService;
        }

        /// <summary>
        /// Get all inventory movements
        /// </summary>
        /// <returns>List of inventory movements</returns>
        [HttpGet("movements")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<InventoryLogDto>>> GetAllMovements()
        {
            var movements = await _inventoryService.GetAllInventoryLogsAsync();
            return Ok(movements);
        }

        /// <summary>
        /// Get inventory movement by ID
        /// </summary>
        /// <param name="id">Movement ID</param>
        /// <returns>Movement details</returns>
        [HttpGet("movements/{id}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<InventoryLogDto>> GetMovement(int id)
        {
            var movement = await _inventoryService.GetInventoryLogByIdAsync(id);
            if (movement == null)
                return NotFound($"Inventory movement with ID {id} not found.");

            return Ok(movement);
        }

        /// <summary>
        /// Create a new inventory movement
        /// </summary>
        /// <param name="createInventoryLogDto">Movement creation data</param>
        /// <returns>Created movement</returns>
        [HttpPost("movements")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<InventoryLogDto>> CreateMovement(CreateInventoryLogDto createInventoryLogDto)
        {
            try
            {
                var movement = await _inventoryService.CreateInventoryLogAsync(createInventoryLogDto);
                return CreatedAtAction(nameof(GetMovement), new { id = movement.Id }, movement);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Get movements by product
        /// </summary>
        /// <param name="productId">Product ID</param>
        /// <returns>List of product movements</returns>
        [HttpGet("movements/product/{productId}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<InventoryLogDto>>> GetMovementsByProduct(int productId)
        {
            var movements = await _inventoryService.GetMovementsByProductAsync(productId);
            return Ok(movements);
        }

        /// <summary>
        /// Get movements by type
        /// </summary>
        /// <param name="movementType">Movement type</param>
        /// <returns>List of movements by type</returns>
        [HttpGet("movements/type/{movementType}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<InventoryLogDto>>> GetMovementsByType(string movementType)
        {
            var movements = await _inventoryService.GetMovementsByTypeAsync(movementType);
            return Ok(movements);
        }

        /// <summary>
        /// Get recent movements
        /// </summary>
        /// <param name="count">Number of movements to return</param>
        /// <returns>List of recent movements</returns>
        [HttpGet("movements/recent")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<InventoryLogDto>>> GetRecentMovements([FromQuery] int count = 50)
        {
            var movements = await _inventoryService.GetRecentMovementsAsync(count);
            return Ok(movements);
        }

        /// <summary>
        /// Get current stock for a product
        /// </summary>
        /// <param name="productId">Product ID</param>
        /// <returns>Current stock quantity</returns>
        [HttpGet("stock/{productId}")]
        [Authorize(Roles = "admin,manager,cashier")]
        public async Task<ActionResult<object>> GetCurrentStock(int productId)
        {
            var currentStock = await _inventoryService.GetCurrentStockAsync(productId);
            return Ok(new { ProductId = productId, CurrentStock = currentStock });
        }

        /// <summary>
        /// Get stock summary for all products
        /// </summary>
        /// <returns>Stock summary</returns>
        [HttpGet("stock/summary")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<ProductStockDto>>> GetStockSummary()
        {
            var stockSummary = await _inventoryService.GetStockSummaryAsync();
            return Ok(stockSummary);
        }

        /// <summary>
        /// Adjust stock for a product
        /// </summary>
        /// <param name="adjustmentDto">Stock adjustment data</param>
        /// <returns>Success status</returns>
        [HttpPost("stock/adjust")]
        [Authorize(Roles = "admin,manager")]
        public async Task<IActionResult> AdjustStock(StockAdjustmentDto adjustmentDto)
        {
            try
            {
                var result = await _inventoryService.AdjustStockAsync(adjustmentDto);
                if (!result)
                    return NotFound($"Product with ID {adjustmentDto.ProductId} not found.");

                return Ok(new { Message = "Stock adjusted successfully." });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Get low stock alerts
        /// </summary>
        /// <param name="threshold">Low stock threshold</param>
        /// <returns>List of low stock products</returns>
        [HttpGet("alerts/low-stock")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<LowStockAlertDto>>> GetLowStockAlerts([FromQuery] int threshold = 10)
        {
            var alerts = await _inventoryService.GetLowStockAlertsAsync(threshold);
            return Ok(alerts);
        }

        /// <summary>
        /// Get out of stock products
        /// </summary>
        /// <returns>List of out of stock products</returns>
        [HttpGet("alerts/out-of-stock")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<LowStockAlertDto>>> GetOutOfStockProducts()
        {
            var alerts = await _inventoryService.GetOutOfStockProductsAsync();
            return Ok(alerts);
        }

        /// <summary>
        /// Get critical stock alerts
        /// </summary>
        /// <param name="criticalThreshold">Critical stock threshold</param>
        /// <returns>List of critical stock products</returns>
        [HttpGet("alerts/critical")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<LowStockAlertDto>>> GetCriticalStockAlerts([FromQuery] int criticalThreshold = 5)
        {
            var alerts = await _inventoryService.GetCriticalStockAlertsAsync(criticalThreshold);
            return Ok(alerts);
        }

        /// <summary>
        /// Get inventory valuation report
        /// </summary>
        /// <returns>Inventory valuation report</returns>
        [HttpGet("valuation")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<InventoryValuationDto>>> GetInventoryValuationReport()
        {
            var report = await _inventoryService.GetInventoryValuationReportAsync();
            return Ok(report);
        }

        /// <summary>
        /// Get total inventory value
        /// </summary>
        /// <returns>Total inventory value</returns>
        [HttpGet("value/total")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<object>> GetTotalInventoryValue()
        {
            var totalValue = await _inventoryService.GetTotalInventoryValueAsync();
            return Ok(new { TotalInventoryValue = totalValue });
        }

        /// <summary>
        /// Get movements by type report
        /// </summary>
        /// <param name="fromDate">Start date (optional)</param>
        /// <param name="toDate">End date (optional)</param>
        /// <returns>Movements by type report</returns>
        [HttpGet("reports/movements-by-type")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<InventoryMovementDto>>> GetMovementsByTypeReport(
            [FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
        {
            var report = await _inventoryService.GetMovementsByTypeReportAsync(fromDate, toDate);
            return Ok(report);
        }

        /// <summary>
        /// Get daily movements report
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <returns>Daily movements report</returns>
        [HttpGet("reports/daily-movements")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<DailyMovementDto>>> GetDailyMovementsReport(
            [FromQuery] DateTime fromDate, [FromQuery] DateTime toDate)
        {
            var report = await _inventoryService.GetDailyMovementsReportAsync(fromDate, toDate);
            return Ok(report);
        }

        /// <summary>
        /// Transfer stock between products
        /// </summary>
        /// <param name="transferDto">Stock transfer data</param>
        /// <returns>Success status</returns>
        [HttpPost("stock/transfer")]
        [Authorize(Roles = "admin,manager")]
        public async Task<IActionResult> TransferStock(StockTransferDto transferDto)
        {
            try
            {
                var result = await _inventoryService.TransferStockAsync(transferDto);
                return Ok(new { Message = "Stock transferred successfully." });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Search inventory movements
        /// </summary>
        /// <param name="searchDto">Search criteria</param>
        /// <returns>List of matching movements</returns>
        [HttpPost("movements/search")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<InventoryLogDto>>> SearchMovements(InventorySearchDto searchDto)
        {
            var movements = await _inventoryService.SearchMovementsAsync(searchDto);
            return Ok(movements);
        }

        /// <summary>
        /// Get inventory statistics
        /// </summary>
        /// <param name="fromDate">Start date (optional)</param>
        /// <param name="toDate">End date (optional)</param>
        /// <returns>Inventory statistics</returns>
        [HttpGet("statistics")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<object>> GetInventoryStatistics([FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
        {
            var statistics = await _inventoryService.GetInventoryStatisticsAsync(fromDate, toDate);
            return Ok(statistics);
        }

        /// <summary>
        /// Get cost analysis for a product
        /// </summary>
        /// <param name="productId">Product ID</param>
        /// <returns>Cost analysis</returns>
        [HttpGet("cost-analysis/{productId}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<object>>> GetCostAnalysis(int productId)
        {
            var analysis = await _inventoryService.GetCostAnalysisAsync(productId);
            return Ok(analysis);
        }

        /// <summary>
        /// Get audit trail for a product
        /// </summary>
        /// <param name="productId">Product ID</param>
        /// <param name="fromDate">Start date (optional)</param>
        /// <param name="toDate">End date (optional)</param>
        /// <returns>Product audit trail</returns>
        [HttpGet("audit-trail/{productId}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<InventoryLogDto>>> GetAuditTrail(
            int productId, [FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
        {
            var auditTrail = await _inventoryService.GetAuditTrailAsync(productId, fromDate, toDate);
            return Ok(auditTrail);
        }

        /// <summary>
        /// Get paged inventory movements
        /// </summary>
        /// <param name="pageNumber">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="searchDto">Search criteria (optional)</param>
        /// <returns>Paged list of movements</returns>
        [HttpGet("movements/paged")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<object>> GetPagedMovements(
            [FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10, [FromQuery] InventorySearchDto? searchDto = null)
        {
            var (movements, totalCount) = await _inventoryService.GetPagedMovementsAsync(pageNumber, pageSize, searchDto);
            
            return Ok(new
            {
                Movements = movements,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            });
        }
    }
}
