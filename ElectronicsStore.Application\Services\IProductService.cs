using ElectronicsStore.Application.DTOs;

namespace ElectronicsStore.Application.Services
{
    public interface IProductService
    {
        // Basic CRUD operations
        Task<ProductDto?> GetProductByIdAsync(int id);
        Task<ProductDto?> GetProductByBarcodeAsync(string barcode);
        Task<IEnumerable<ProductDto>> GetAllProductsAsync();
        Task<ProductDto> CreateProductAsync(CreateProductDto createProductDto, int userId);
        Task<ProductDto> UpdateProductAsync(UpdateProductDto updateProductDto);
        Task<bool> DeleteProductAsync(int id);

        // Search and filtering
        Task<IEnumerable<ProductDto>> SearchProductsAsync(ProductSearchDto searchDto);
        Task<IEnumerable<ProductDto>> GetProductsByCategoryAsync(int categoryId);
        Task<IEnumerable<ProductDto>> GetProductsBySupplierAsync(int supplierId);
        Task<IEnumerable<ProductDto>> GetProductsInPriceRangeAsync(decimal minPrice, decimal maxPrice);

        // Stock management
        Task<IEnumerable<ProductStockDto>> GetStockSummaryAsync();
        Task<IEnumerable<ProductStockDto>> GetLowStockProductsAsync(int threshold = 10);
        Task<IEnumerable<ProductStockDto>> GetOutOfStockProductsAsync();
        Task<ProductStockDto?> GetProductStockAsync(int productId);
        Task<bool> AdjustStockAsync(int productId, int quantity, string reason, int userId);

        // Analytics
        Task<IEnumerable<ProductDto>> GetTopSellingProductsAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null);
        Task<decimal> GetTotalInventoryValueAsync();
        Task<decimal> GetAverageProductPriceAsync();
        Task<object> GetProductStatisticsAsync();

        // Validation
        Task<bool> ProductExistsAsync(int id);
        Task<bool> BarcodeExistsAsync(string barcode, int? excludeId = null);
        Task<bool> CanDeleteProductAsync(int id);

        // Pagination
        Task<(IEnumerable<ProductDto> Products, int TotalCount)> GetPagedProductsAsync(
            int pageNumber, int pageSize, ProductSearchDto? searchDto = null);
    }
}
