using ElectronicsStore.Application.DTOs;
using ElectronicsStore.Application.Interfaces;
using ElectronicsStore.Domain.Entities;

namespace ElectronicsStore.Application.Services
{
    public class SupplierService : ISupplierService
    {
        private readonly IUnitOfWork _unitOfWork;

        public SupplierService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<SupplierDto?> GetSupplierByIdAsync(int id)
        {
            var supplier = await _unitOfWork.Suppliers.GetByIdAsync(id);
            if (supplier == null)
                return null;

            var productCount = await _unitOfWork.Products.CountAsync(p => p.SupplierId == id);

            return MapToSupplierDto(supplier, productCount);
        }

        public async Task<IEnumerable<SupplierDto>> GetAllSuppliersAsync()
        {
            var suppliers = await _unitOfWork.Suppliers.GetAllAsync();
            var supplierDtos = new List<SupplierDto>();

            foreach (var supplier in suppliers)
            {
                var productCount = await _unitOfWork.Products.CountAsync(p => p.SupplierId == supplier.Id);
                supplierDtos.Add(MapToSupplierDto(supplier, productCount));
            }

            return supplierDtos;
        }

        public async Task<SupplierDto> CreateSupplierAsync(CreateSupplierDto createSupplierDto)
        {
            // Check if supplier name already exists
            var existingSupplier = await _unitOfWork.Suppliers.FirstOrDefaultAsync(s => s.Name == createSupplierDto.Name);
            if (existingSupplier != null)
                throw new InvalidOperationException($"Supplier with name '{createSupplierDto.Name}' already exists.");

            var supplier = new Supplier
            {
                Name = createSupplierDto.Name,
                Phone = createSupplierDto.Phone,
                Email = createSupplierDto.Email,
                Address = createSupplierDto.Address
            };

            await _unitOfWork.Suppliers.AddAsync(supplier);
            await _unitOfWork.SaveChangesAsync();

            return MapToSupplierDto(supplier, 0);
        }

        public async Task<SupplierDto> UpdateSupplierAsync(UpdateSupplierDto updateSupplierDto)
        {
            var supplier = await _unitOfWork.Suppliers.GetByIdAsync(updateSupplierDto.Id);
            if (supplier == null)
                throw new InvalidOperationException($"Supplier with ID {updateSupplierDto.Id} not found.");

            // Check if new name conflicts with existing supplier
            var existingSupplier = await _unitOfWork.Suppliers.FirstOrDefaultAsync(
                s => s.Name == updateSupplierDto.Name && s.Id != updateSupplierDto.Id);
            if (existingSupplier != null)
                throw new InvalidOperationException($"Supplier with name '{updateSupplierDto.Name}' already exists.");

            supplier.Name = updateSupplierDto.Name;
            supplier.Phone = updateSupplierDto.Phone;
            supplier.Email = updateSupplierDto.Email;
            supplier.Address = updateSupplierDto.Address;

            _unitOfWork.Suppliers.Update(supplier);
            await _unitOfWork.SaveChangesAsync();

            var productCount = await _unitOfWork.Products.CountAsync(p => p.SupplierId == supplier.Id);
            return MapToSupplierDto(supplier, productCount);
        }

        public async Task<bool> DeleteSupplierAsync(int id)
        {
            var supplier = await _unitOfWork.Suppliers.GetByIdAsync(id);
            if (supplier == null)
                return false;

            // Check if supplier has associated products
            var hasProducts = await _unitOfWork.Products.AnyAsync(p => p.SupplierId == id);
            if (hasProducts)
                throw new InvalidOperationException("Cannot delete supplier that has associated products.");

            _unitOfWork.Suppliers.Remove(supplier);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<SupplierDto>> SearchSuppliersAsync(SupplierSearchDto searchDto)
        {
            var suppliers = await _unitOfWork.Suppliers.GetAllAsync();

            if (!string.IsNullOrEmpty(searchDto.SearchTerm))
            {
                suppliers = suppliers.Where(s => 
                    s.Name.Contains(searchDto.SearchTerm, StringComparison.OrdinalIgnoreCase) ||
                    (s.Phone != null && s.Phone.Contains(searchDto.SearchTerm)) ||
                    (s.Email != null && s.Email.Contains(searchDto.SearchTerm, StringComparison.OrdinalIgnoreCase)) ||
                    (s.Address != null && s.Address.Contains(searchDto.SearchTerm, StringComparison.OrdinalIgnoreCase)));
            }

            if (!string.IsNullOrEmpty(searchDto.Phone))
            {
                suppliers = suppliers.Where(s => s.Phone != null && s.Phone.Contains(searchDto.Phone));
            }

            if (!string.IsNullOrEmpty(searchDto.Email))
            {
                suppliers = suppliers.Where(s => s.Email != null && s.Email.Contains(searchDto.Email, StringComparison.OrdinalIgnoreCase));
            }

            var supplierDtos = new List<SupplierDto>();
            foreach (var supplier in suppliers)
            {
                var productCount = await _unitOfWork.Products.CountAsync(p => p.SupplierId == supplier.Id);
                supplierDtos.Add(MapToSupplierDto(supplier, productCount));
            }

            return supplierDtos;
        }

        public async Task<IEnumerable<SupplierDto>> GetSuppliersByNameAsync(string name)
        {
            var suppliers = await _unitOfWork.Suppliers.FindAsync(s => s.Name.Contains(name));
            var supplierDtos = new List<SupplierDto>();

            foreach (var supplier in suppliers)
            {
                var productCount = await _unitOfWork.Products.CountAsync(p => p.SupplierId == supplier.Id);
                supplierDtos.Add(MapToSupplierDto(supplier, productCount));
            }

            return supplierDtos;
        }

        public async Task<bool> SupplierExistsAsync(int id)
        {
            return await _unitOfWork.Suppliers.AnyAsync(s => s.Id == id);
        }

        public async Task<bool> SupplierNameExistsAsync(string name, int? excludeId = null)
        {
            if (excludeId.HasValue)
                return await _unitOfWork.Suppliers.AnyAsync(s => s.Name == name && s.Id != excludeId.Value);

            return await _unitOfWork.Suppliers.AnyAsync(s => s.Name == name);
        }

        public async Task<bool> CanDeleteSupplierAsync(int id)
        {
            var hasProducts = await _unitOfWork.Products.AnyAsync(p => p.SupplierId == id);
            return !hasProducts;
        }

        public async Task<IEnumerable<SupplierDto>> GetTopSuppliersAsync(int count = 10)
        {
            var suppliers = await _unitOfWork.Suppliers.GetAllAsync();
            var supplierDtos = new List<SupplierDto>();

            foreach (var supplier in suppliers)
            {
                var productCount = await _unitOfWork.Products.CountAsync(p => p.SupplierId == supplier.Id);
                supplierDtos.Add(MapToSupplierDto(supplier, productCount));
            }

            return supplierDtos.OrderByDescending(s => s.ProductCount).Take(count);
        }

        public async Task<object> GetSupplierStatisticsAsync()
        {
            var totalSuppliers = await _unitOfWork.Suppliers.CountAsync();
            var suppliersWithProducts = 0;
            var suppliersWithoutProducts = 0;
            var totalProducts = 0;

            var suppliers = await _unitOfWork.Suppliers.GetAllAsync();
            foreach (var supplier in suppliers)
            {
                var productCount = await _unitOfWork.Products.CountAsync(p => p.SupplierId == supplier.Id);
                totalProducts += productCount;
                
                if (productCount > 0)
                    suppliersWithProducts++;
                else
                    suppliersWithoutProducts++;
            }

            return new
            {
                TotalSuppliers = totalSuppliers,
                SuppliersWithProducts = suppliersWithProducts,
                SuppliersWithoutProducts = suppliersWithoutProducts,
                TotalProducts = totalProducts,
                AverageProductsPerSupplier = totalSuppliers > 0 ? (double)totalProducts / totalSuppliers : 0
            };
        }

        public async Task<IEnumerable<object>> GetSupplierProductsAsync(int supplierId)
        {
            var products = await _unitOfWork.Products.FindAsync(p => p.SupplierId == supplierId);
            return products.Select(p => new
            {
                p.Id,
                p.Name,
                p.Barcode,
                p.DefaultCostPrice,
                p.DefaultSellingPrice,
                p.CreatedAt
            });
        }

        public async Task<(IEnumerable<SupplierDto> Suppliers, int TotalCount)> GetPagedSuppliersAsync(
            int pageNumber, int pageSize, SupplierSearchDto? searchDto = null)
        {
            var suppliers = await _unitOfWork.Suppliers.GetAllAsync();

            // Apply search filters
            if (searchDto != null)
            {
                if (!string.IsNullOrEmpty(searchDto.SearchTerm))
                {
                    suppliers = suppliers.Where(s => 
                        s.Name.Contains(searchDto.SearchTerm, StringComparison.OrdinalIgnoreCase) ||
                        (s.Phone != null && s.Phone.Contains(searchDto.SearchTerm)) ||
                        (s.Email != null && s.Email.Contains(searchDto.SearchTerm, StringComparison.OrdinalIgnoreCase)));
                }

                if (!string.IsNullOrEmpty(searchDto.Phone))
                {
                    suppliers = suppliers.Where(s => s.Phone != null && s.Phone.Contains(searchDto.Phone));
                }

                if (!string.IsNullOrEmpty(searchDto.Email))
                {
                    suppliers = suppliers.Where(s => s.Email != null && s.Email.Contains(searchDto.Email, StringComparison.OrdinalIgnoreCase));
                }
            }

            var totalCount = suppliers.Count();
            var pagedSuppliers = suppliers.Skip((pageNumber - 1) * pageSize).Take(pageSize);

            var supplierDtos = new List<SupplierDto>();
            foreach (var supplier in pagedSuppliers)
            {
                var productCount = await _unitOfWork.Products.CountAsync(p => p.SupplierId == supplier.Id);
                supplierDtos.Add(MapToSupplierDto(supplier, productCount));
            }

            return (supplierDtos, totalCount);
        }

        private static SupplierDto MapToSupplierDto(Supplier supplier, int productCount)
        {
            return new SupplierDto
            {
                Id = supplier.Id,
                Name = supplier.Name,
                Phone = supplier.Phone,
                Email = supplier.Email,
                Address = supplier.Address,
                ProductCount = productCount
            };
        }
    }
}
