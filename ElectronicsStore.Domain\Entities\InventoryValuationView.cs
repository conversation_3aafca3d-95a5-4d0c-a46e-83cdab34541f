using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ElectronicsStore.Domain.Entities
{
    [Table("inventory_valuation_view")]
    public class InventoryValuationView
    {
        [Key]
        [Column("product_id")]
        public int ProductId { get; set; }

        [Column("product_name")]
        public string ProductName { get; set; } = string.Empty;

        [Column("total_value", TypeName = "decimal(18,2)")]
        public decimal TotalValue { get; set; }
    }
}
