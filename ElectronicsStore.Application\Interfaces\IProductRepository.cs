using ElectronicsStore.Domain.Entities;
using System.Linq.Expressions;

namespace ElectronicsStore.Application.Interfaces
{
    public interface IProductRepository : IGenericRepository<Product>
    {
        // Product-specific methods
        Task<Product?> GetByBarcodeAsync(string barcode);
        Task<IEnumerable<Product>> GetByCategoryAsync(int categoryId);
        Task<IEnumerable<Product>> GetBySupplierAsync(int supplierId);
        Task<IEnumerable<Product>> SearchByNameAsync(string searchTerm);
        Task<bool> BarcodeExistsAsync(string barcode, int? excludeId = null);
        
        // Inventory-related methods
        Task<IEnumerable<Product>> GetLowStockProductsAsync(int threshold = 10);
        Task<IEnumerable<Product>> GetOutOfStockProductsAsync();
        Task<Product?> GetWithCurrentStockAsync(int productId);
        Task<IEnumerable<Product>> GetWithCurrentStockAsync();
        
        // Price-related methods
        Task<IEnumerable<Product>> GetProductsInPriceRangeAsync(decimal minPrice, decimal maxPrice);
        Task<decimal> GetAveragePriceAsync();
        Task<decimal> GetTotalInventoryValueAsync();
        
        // Advanced queries
        Task<IEnumerable<Product>> GetTopSellingProductsAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<Product>> GetProductsWithDetailsAsync(Expression<Func<Product, bool>>? filter = null);
        Task<(IEnumerable<Product> Products, int TotalCount)> GetPagedWithStockAsync(
            int pageNumber, int pageSize, string? searchTerm = null, int? categoryId = null);
    }
}
