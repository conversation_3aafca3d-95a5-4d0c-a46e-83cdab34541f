namespace ElectronicsStore.Application.DTOs
{
    public class ProductDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Barcode { get; set; }
        public int CategoryId { get; set; }
        public string CategoryName { get; set; } = string.Empty;
        public int? SupplierId { get; set; }
        public string? SupplierName { get; set; }
        public decimal DefaultCostPrice { get; set; }
        public decimal DefaultSellingPrice { get; set; }
        public decimal MinSellingPrice { get; set; }
        public string? Description { get; set; }
        public DateTime CreatedAt { get; set; }
        public int CurrentQuantity { get; set; }
        public decimal InventoryValue { get; set; }
        public DateTime? LastMovementDate { get; set; }
    }

    public class CreateProductDto
    {
        public string Name { get; set; } = string.Empty;
        public string? Barcode { get; set; }
        public int CategoryId { get; set; }
        public int? SupplierId { get; set; }
        public decimal DefaultCostPrice { get; set; }
        public decimal DefaultSellingPrice { get; set; }
        public decimal MinSellingPrice { get; set; }
        public string? Description { get; set; }
        public int InitialQuantity { get; set; } = 0;
    }

    public class UpdateProductDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Barcode { get; set; }
        public int CategoryId { get; set; }
        public int? SupplierId { get; set; }
        public decimal DefaultCostPrice { get; set; }
        public decimal DefaultSellingPrice { get; set; }
        public decimal MinSellingPrice { get; set; }
        public string? Description { get; set; }
    }

    public class ProductSearchDto
    {
        public string? SearchTerm { get; set; }
        public int? CategoryId { get; set; }
        public int? SupplierId { get; set; }
        public decimal? MinPrice { get; set; }
        public decimal? MaxPrice { get; set; }
        public bool? InStock { get; set; }
        public bool? LowStock { get; set; }
        public int LowStockThreshold { get; set; } = 10;
    }

    public class ProductStockDto
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public int CurrentQuantity { get; set; }
        public decimal AverageCost { get; set; }
        public decimal TotalValue { get; set; }
        public DateTime? LastMovementDate { get; set; }
        public string Status { get; set; } = string.Empty; // InStock, LowStock, OutOfStock
    }
}
