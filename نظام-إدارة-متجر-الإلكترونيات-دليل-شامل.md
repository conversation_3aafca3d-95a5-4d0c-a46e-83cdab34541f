# 📋 **دليل شامل لجميع وظائف نظام إدارة متجر الإلكترونيات**

## 🏗️ **نظرة عامة على النظام**

النظام مصمم لإدارة متجر إلكترونيات بشكل كامل ومتكامل، ويغطي جميع العمليات من المشتريات إلى المبيعات والتقارير.

---

## 👥 **1. نظام إدارة المستخدمين والصلاحيات**

### **إدارة المستخدمين**
- ✅ إنشاء حسابات مستخدمين جديدة
- ✅ تعديل بيانات المستخدمين
- ✅ حذف المستخدمين
- ✅ تفعيل/إلغاء تفعيل الحسابات
- ✅ تغيير كلمات المرور
- ✅ عرض سجل نشاط المستخدمين

### **نظام الأدوار والصلاحيات**
- ✅ **Admin (مدير النظام)**:
  - إدارة كاملة للنظام
  - إدارة المستخدمين والأدوار
  - الوصول لجميع التقارير
  - إعدادات النظام

- ✅ **Manager (مدير المتجر)**:
  - إدارة المنتجات والأصناف
  - إدارة الموردين
  - إدارة المشتريات والمبيعات
  - عرض جميع التقارير
  - تفويض الأسعار (Override Prices)
  - إدارة المرتجعات

- ✅ **Cashier (كاشير)**:
  - إنشاء فواتير البيع
  - عرض المنتجات والأسعار
  - البحث في المنتجات
  - عرض المخزون الحالي
  - طباعة الفواتير

### **نظام المصادقة والأمان**
- ✅ تسجيل الدخول بـ JWT Token
- ✅ تحديث Token تلقائياً
- ✅ انتهاء صلاحية الجلسات
- ✅ تشفير كلمات المرور
- ✅ حماية APIs بالصلاحيات

---

## 📦 **2. إدارة المنتجات والمخزون**

### **إدارة المنتجات**
- ✅ إضافة منتجات جديدة
- ✅ تعديل بيانات المنتجات
- ✅ حذف المنتجات
- ✅ البحث في المنتجات (بالاسم، الباركود، الصنف)
- ✅ تصنيف المنتجات حسب الأصناف
- ✅ ربط المنتجات بالموردين
- ✅ إدارة الباركود
- ✅ تحديد أسعار متعددة:
  - سعر التكلفة الافتراضي
  - سعر البيع الافتراضي  
  - الحد الأدنى لسعر البيع
- ✅ إضافة وصف للمنتجات
- ✅ تتبع تاريخ إضافة المنتجات

### **إدارة الأصناف**
- ✅ إنشاء أصناف جديدة
- ✅ تعديل أسماء الأصناف
- ✅ حذف الأصناف (مع التحقق من عدم وجود منتجات)
- ✅ عرض المنتجات حسب الصنف
- ✅ إحصائيات الأصناف

### **إدارة الموردين**
- ✅ إضافة موردين جدد
- ✅ تعديل بيانات الموردين:
  - الاسم
  - رقم الهاتف
  - البريد الإلكتروني
  - العنوان
- ✅ حذف الموردين
- ✅ ربط المنتجات بالموردين
- ✅ عرض تاريخ التعامل مع الموردين

### **إدارة المخزون**
- ✅ **تتبع المخزون الحالي**:
  - عرض الكمية المتاحة لكل منتج
  - تحديث تلقائي عند البيع/الشراء
  - تتبع حركات المخزون

- ✅ **أنواع حركات المخزون**:
  - شراء (Purchase)
  - بيع (Sale)
  - مرتجع مبيعات (Return Sale)
  - مرتجع مشتريات (Return Purchase)
  - تعديل يدوي (Adjust)

- ✅ **تنبيهات المخزون**:
  - تنبيه المخزون المنخفض
  - تنبيه المخزون المنتهي
  - تحديد حد أدنى لكل منتج

- ✅ **تقييم المخزون**:
  - حساب قيمة المخزون الإجمالية
  - تقييم حسب سعر التكلفة
  - تقارير تقييم المخزون

---

## 🛒 **3. إدارة المشتريات**

### **فواتير الشراء**
- ✅ إنشاء فواتير شراء جديدة
- ✅ إضافة تفاصيل الفاتورة:
  - اختيار المورد
  - إضافة منتجات متعددة
  - تحديد الكمية وسعر الوحدة
  - حساب الإجمالي تلقائياً
- ✅ حفظ وطباعة الفواتير
- ✅ تعديل فواتير الشراء
- ✅ عرض تاريخ فواتير الشراء
- ✅ البحث في فواتير الشراء

### **تأثير المشتريات على المخزون**
- ✅ تحديث المخزون تلقائياً عند الشراء
- ✅ تسجيل حركة مخزون لكل عملية شراء
- ✅ تتبع تكلفة المنتجات
- ✅ حساب متوسط التكلفة

### **مرتجعات المشتريات**
- ✅ تسجيل مرتجعات للموردين
- ✅ تحديد سبب الإرجاع
- ✅ تحديث المخزون عند الإرجاع
- ✅ ربط المرتجع بفاتورة الشراء الأصلية

---

## 💰 **4. إدارة المبيعات**

### **فواتير البيع**
- ✅ إنشاء فواتير بيع جديدة
- ✅ إضافة بيانات العميل (اختياري)
- ✅ إضافة منتجات متعددة للفاتورة:
  - اختيار المنتج (بالاسم أو الباركود)
  - تحديد الكمية
  - تحديد سعر البيع
  - إضافة خصم على المنتج
- ✅ **أنواع الدفع**:
  - نقدي (Cash)
  - بطاقة ائتمان (Card)
  - آجل (Deferred)
- ✅ حساب الإجمالي تلقائياً
- ✅ تطبيق خصومات إجمالية
- ✅ طباعة الفواتير
- ✅ حفظ الفواتير في النظام

### **نظام تفويض الأسعار**
- ✅ منع البيع بأقل من الحد الأدنى
- ✅ تفويض المدير للبيع بسعر أقل
- ✅ تسجيل من قام بالتفويض ومتى
- ✅ تقارير التفويضات

### **إدارة المبيعات**
- ✅ عرض جميع فواتير البيع
- ✅ البحث في الفواتير (برقم الفاتورة، اسم العميل، التاريخ)
- ✅ تعديل فواتير البيع (قبل الطباعة)
- ✅ إلغاء فواتير البيع
- ✅ عرض تفاصيل الفاتورة

### **مرتجعات المبيعات**
- ✅ تسجيل مرتجعات من العملاء
- ✅ تحديد سبب الإرجاع
- ✅ إرجاع المبلغ أو استبدال المنتج
- ✅ تحديث المخزون عند الإرجاع
- ✅ ربط المرتجع بفاتورة البيع الأصلية

---

## 📊 **5. التقارير والإحصائيات**

### **تقارير المبيعات**
- ✅ **تقارير يومية**:
  - مبيعات اليوم
  - عدد الفواتير
  - متوسط قيمة الفاتورة
  - أفضل المنتجات مبيعاً اليوم

- ✅ **تقارير شهرية**:
  - إجمالي المبيعات الشهرية
  - مقارنة مع الأشهر السابقة
  - نمو المبيعات

- ✅ **تقارير مخصصة**:
  - مبيعات فترة محددة
  - مبيعات حسب طريقة الدفع
  - مبيعات حسب المنتج
  - مبيعات حسب الصنف

- ✅ **تحليل العملاء**:
  - أفضل العملاء (الأكثر شراءً)
  - تكرار الشراء
  - متوسط قيمة الشراء

### **تقارير المخزون**
- ✅ **المخزون الحالي**:
  - كمية كل منتج
  - قيمة المخزون
  - المنتجات منخفضة المخزون
  - المنتجات منتهية المخزون

- ✅ **حركات المخزون**:
  - جميع حركات المخزون
  - حركات منتج محدد
  - حركات فترة محددة
  - تحليل أسباب الحركات

- ✅ **تقييم المخزون**:
  - قيمة المخزون الإجمالية
  - تقييم حسب التكلفة
  - تقييم حسب سعر البيع
  - تقارير الجرد

### **تقارير المشتريات**
- ✅ إجمالي المشتريات (يومي/شهري/سنوي)
- ✅ مشتريات حسب المورد
- ✅ مشتريات حسب المنتج
- ✅ تحليل تكاليف المشتريات
- ✅ مقارنة أسعار الموردين

### **التقارير المالية**
- ✅ **تقرير الأرباح والخسائر**:
  - إجمالي المبيعات
  - تكلفة البضاعة المباعة (COGS)
  - إجمالي الربح
  - صافي الربح

- ✅ **تحليل الربحية**:
  - ربحية كل منتج
  - ربحية كل صنف
  - هامش الربح
  - نسبة الربح

- ✅ **تقارير المصروفات**:
  - تسجيل المصروفات
  - تصنيف المصروفات
  - تقارير المصروفات الشهرية

### **تقارير المرتجعات**
- ✅ مرتجعات المبيعات
- ✅ مرتجعات المشتريات  
- ✅ أسباب المرتجعات
- ✅ تأثير المرتجعات على الأرباح

---

## 🔍 **6. البحث والتصفية**

### **البحث في المنتجات**
- ✅ البحث بالاسم
- ✅ البحث بالباركود
- ✅ البحث بالصنف
- ✅ البحث بالمورد
- ✅ البحث المتقدم (متعدد المعايير)

### **البحث في الفواتير**
- ✅ البحث برقم الفاتورة
- ✅ البحث باسم العميل
- ✅ البحث بالتاريخ
- ✅ البحث بطريقة الدفع
- ✅ البحث بالمبلغ

### **التصفية والترتيب**
- ✅ ترتيب النتائج (تصاعدي/تنازلي)
- ✅ تصفية حسب التاريخ
- ✅ تصفية حسب النوع
- ✅ تصفية حسب الحالة
