using Microsoft.AspNetCore.Authorization;

namespace ElectronicsStore.API.Attributes
{
    /// <summary>
    /// Custom authorization attribute for common role combinations
    /// </summary>
    public static class AuthorizeRoles
    {
        /// <summary>
        /// Admin only access
        /// </summary>
        public class AdminOnlyAttribute : AuthorizeAttribute
        {
            public AdminOnlyAttribute()
            {
                Roles = "admin";
            }
        }

        /// <summary>
        /// Admin and Manager access
        /// </summary>
        public class AdminManagerAttribute : AuthorizeAttribute
        {
            public AdminManagerAttribute()
            {
                Roles = "admin,manager";
            }
        }

        /// <summary>
        /// All roles access (Admin, Manager, Cashier)
        /// </summary>
        public class AllRolesAttribute : AuthorizeAttribute
        {
            public AllRolesAttribute()
            {
                Roles = "admin,manager,cashier";
            }
        }

        /// <summary>
        /// Manager and Cashier access (for sales operations)
        /// </summary>
        public class SalesRolesAttribute : AuthorizeAttribute
        {
            public SalesRolesAttribute()
            {
                Roles = "admin,manager,cashier";
            }
        }

        /// <summary>
        /// Admin and Manager access (for management operations)
        /// </summary>
        public class ManagementRolesAttribute : AuthorizeAttribute
        {
            public ManagementRolesAttribute()
            {
                Roles = "admin,manager";
            }
        }

        /// <summary>
        /// Read-only access for all authenticated users
        /// </summary>
        public class ReadOnlyAttribute : AuthorizeAttribute
        {
            public ReadOnlyAttribute()
            {
                Roles = "admin,manager,cashier";
            }
        }
    }

    /// <summary>
    /// Attribute for operations that require specific permissions
    /// </summary>
    public class RequirePermissionAttribute : AuthorizeAttribute
    {
        public RequirePermissionAttribute(string permission)
        {
            Policy = $"Permission.{permission}";
        }
    }

    /// <summary>
    /// Attribute for CRUD operations with standard role requirements
    /// </summary>
    public static class CrudAuthorize
    {
        /// <summary>
        /// Read operations - all roles can read
        /// </summary>
        public class ReadAttribute : AuthorizeAttribute
        {
            public ReadAttribute()
            {
                Roles = "admin,manager,cashier";
            }
        }

        /// <summary>
        /// Create operations - admin and manager only
        /// </summary>
        public class CreateAttribute : AuthorizeAttribute
        {
            public CreateAttribute()
            {
                Roles = "admin,manager";
            }
        }

        /// <summary>
        /// Update operations - admin and manager only
        /// </summary>
        public class UpdateAttribute : AuthorizeAttribute
        {
            public UpdateAttribute()
            {
                Roles = "admin,manager";
            }
        }

        /// <summary>
        /// Delete operations - admin only
        /// </summary>
        public class DeleteAttribute : AuthorizeAttribute
        {
            public DeleteAttribute()
            {
                Roles = "admin";
            }
        }
    }

    /// <summary>
    /// Attributes for specific business operations
    /// </summary>
    public static class BusinessAuthorize
    {
        /// <summary>
        /// Inventory management operations
        /// </summary>
        public class InventoryAttribute : AuthorizeAttribute
        {
            public InventoryAttribute()
            {
                Roles = "admin,manager";
            }
        }

        /// <summary>
        /// Financial operations and reports
        /// </summary>
        public class FinancialAttribute : AuthorizeAttribute
        {
            public FinancialAttribute()
            {
                Roles = "admin,manager";
            }
        }

        /// <summary>
        /// Sales operations
        /// </summary>
        public class SalesAttribute : AuthorizeAttribute
        {
            public SalesAttribute()
            {
                Roles = "admin,manager,cashier";
            }
        }

        /// <summary>
        /// Purchase operations
        /// </summary>
        public class PurchaseAttribute : AuthorizeAttribute
        {
            public PurchaseAttribute()
            {
                Roles = "admin,manager";
            }
        }

        /// <summary>
        /// Return operations
        /// </summary>
        public class ReturnAttribute : AuthorizeAttribute
        {
            public ReturnAttribute()
            {
                Roles = "admin,manager,cashier";
            }
        }

        /// <summary>
        /// User management operations
        /// </summary>
        public class UserManagementAttribute : AuthorizeAttribute
        {
            public UserManagementAttribute()
            {
                Roles = "admin";
            }
        }

        /// <summary>
        /// System administration operations
        /// </summary>
        public class SystemAdminAttribute : AuthorizeAttribute
        {
            public SystemAdminAttribute()
            {
                Roles = "admin";
            }
        }

        /// <summary>
        /// Statistics and analytics operations
        /// </summary>
        public class AnalyticsAttribute : AuthorizeAttribute
        {
            public AnalyticsAttribute()
            {
                Roles = "admin,manager";
            }
        }
    }
}
