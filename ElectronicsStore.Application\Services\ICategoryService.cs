using ElectronicsStore.Application.DTOs;

namespace ElectronicsStore.Application.Services
{
    public interface ICategoryService
    {
        // Basic CRUD operations
        Task<IEnumerable<CategoryDto>> GetAllCategoriesAsync();
        Task<CategoryDto?> GetCategoryByIdAsync(int id);
        Task<CategoryDto> CreateCategoryAsync(CreateCategoryDto createCategoryDto);
        Task<CategoryDto> UpdateCategoryAsync(UpdateCategoryDto updateCategoryDto);
        Task<bool> DeleteCategoryAsync(int id);

        // Validation methods
        Task<bool> CategoryExistsAsync(int id);
        Task<bool> CategoryNameExistsAsync(string name, int? excludeId = null);
        Task<bool> CanDeleteCategoryAsync(int id);

        // Search and filtering
        Task<IEnumerable<CategoryDto>> GetCategoriesByNameAsync(string name);
        Task<IEnumerable<CategoryDto>> SearchCategoriesAsync(string searchTerm);

        // Pagination
        Task<(IEnumerable<CategoryDto> Categories, int TotalCount)> GetPagedCategoriesAsync(int pageNumber, int pageSize);

        // Statistics and analytics
        Task<object> GetCategoryStatisticsAsync();
        Task<IEnumerable<object>> GetCategoriesWithProductCountAsync();
    }
}
