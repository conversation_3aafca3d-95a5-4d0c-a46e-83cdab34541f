using ElectronicsStore.Domain.Entities;

namespace ElectronicsStore.Application.Interfaces
{
    public interface IUnitOfWork : IDisposable
    {
        // Generic repositories
        IGenericRepository<Category> Categories { get; }
        IGenericRepository<Supplier> Suppliers { get; }
        IGenericRepository<Role> Roles { get; }
        IGenericRepository<Permission> Permissions { get; }
        IGenericRepository<RolePermission> RolePermissions { get; }
        IGenericRepository<User> Users { get; }
        IGenericRepository<PurchaseInvoice> PurchaseInvoices { get; }
        IGenericRepository<PurchaseInvoiceDetail> PurchaseInvoiceDetails { get; }
        IGenericRepository<SalesInvoice> SalesInvoices { get; }
        IGenericRepository<SalesInvoiceDetail> SalesInvoiceDetails { get; }
        IGenericRepository<SalesReturn> SalesReturns { get; }
        IGenericRepository<PurchaseReturn> PurchaseReturns { get; }
        IGenericRepository<Expense> Expenses { get; }

        // Specific repositories
        IProductRepository Products { get; }
        ISalesRepository Sales { get; }
        IInventoryRepository Inventory { get; }

        // Views
        IGenericRepository<InventoryView> InventoryViews { get; }
        IGenericRepository<InventoryValuationView> InventoryValuationViews { get; }
        IGenericRepository<CogsView> CogsViews { get; }

        // Transaction management
        Task<int> SaveChangesAsync();
        Task BeginTransactionAsync();
        Task CommitTransactionAsync();
        Task RollbackTransactionAsync();
        bool HasActiveTransaction { get; }

        // Bulk operations
        Task<int> ExecuteSqlRawAsync(string sql, params object[] parameters);
        Task<IEnumerable<T>> SqlQueryAsync<T>(string sql, params object[] parameters) where T : class;
    }
}
