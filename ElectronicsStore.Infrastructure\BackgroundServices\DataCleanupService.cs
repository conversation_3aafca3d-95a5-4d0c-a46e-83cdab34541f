using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using ElectronicsStore.Application.Interfaces;

namespace ElectronicsStore.Infrastructure.BackgroundServices
{
    public class DataCleanupService : BackgroundService
    {
        private readonly ILogger<DataCleanupService> _logger;
        private readonly IServiceProvider _serviceProvider;

        public DataCleanupService(ILogger<DataCleanupService> logger, IServiceProvider serviceProvider)
        {
            _logger = logger;
            _serviceProvider = serviceProvider;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    _logger.LogInformation("Data cleanup service started at: {Time}", DateTimeOffset.Now);

                    using (var scope = _serviceProvider.CreateScope())
                    {
                        var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();

                        // Clean up old inventory movements (older than 1 year)
                        await CleanupOldInventoryMovements(unitOfWork);

                        // Clean up old performance logs (older than 30 days)
                        await CleanupOldPerformanceLogs(unitOfWork);

                        // Clean up expired cache entries
                        await CleanupExpiredCacheEntries(scope.ServiceProvider);

                        // Update inventory statistics
                        await UpdateInventoryStatistics(unitOfWork);
                    }

                    _logger.LogInformation("Data cleanup service completed at: {Time}", DateTimeOffset.Now);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred during data cleanup");
                }

                // Run every 24 hours
                await Task.Delay(TimeSpan.FromHours(24), stoppingToken);
            }
        }

        private async Task CleanupOldInventoryMovements(IUnitOfWork unitOfWork)
        {
            try
            {
                var cutoffDate = DateTime.UtcNow.AddYears(-1);
                var oldMovements = await unitOfWork.InventoryMovements
                    .FindAsync(im => im.MovementDate < cutoffDate);

                var count = oldMovements.Count();
                if (count > 0)
                {
                    foreach (var movement in oldMovements)
                    {
                        unitOfWork.InventoryMovements.Remove(movement);
                    }

                    await unitOfWork.SaveChangesAsync();
                    _logger.LogInformation("Cleaned up {Count} old inventory movements", count);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cleaning up old inventory movements");
            }
        }

        private async Task CleanupOldPerformanceLogs(IUnitOfWork unitOfWork)
        {
            try
            {
                // This would clean up performance logs if we had a table for them
                // For now, we'll just log that this step was executed
                _logger.LogInformation("Performance logs cleanup completed");
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cleaning up old performance logs");
            }
        }

        private async Task CleanupExpiredCacheEntries(IServiceProvider serviceProvider)
        {
            try
            {
                // Cache cleanup is handled automatically by MemoryCache
                // This is a placeholder for custom cache cleanup logic
                _logger.LogInformation("Cache cleanup completed");
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cleaning up expired cache entries");
            }
        }

        private async Task UpdateInventoryStatistics(IUnitOfWork unitOfWork)
        {
            try
            {
                // Update product statistics
                var products = await unitOfWork.Products.GetAllAsync();
                var lowStockCount = products.Count(p => p.Quantity < 10);
                var outOfStockCount = products.Count(p => p.Quantity == 0);

                _logger.LogInformation("Inventory statistics updated: {LowStock} low stock, {OutOfStock} out of stock", 
                    lowStockCount, outOfStockCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating inventory statistics");
            }
        }
    }
}
