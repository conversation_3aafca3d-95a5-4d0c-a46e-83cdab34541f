using Microsoft.Extensions.Logging;

namespace ElectronicsStore.Infrastructure.Services
{
    public interface ILoggingService
    {
        void LogInformation(string message, params object[] args);
        void LogWarning(string message, params object[] args);
        void LogError(string message, params object[] args);
        void LogError(Exception exception, string message, params object[] args);
        void LogDebug(string message, params object[] args);
        void LogCritical(string message, params object[] args);
        void LogCritical(Exception exception, string message, params object[] args);
    }

    public class LoggingService : ILoggingService
    {
        private readonly ILogger<LoggingService> _logger;

        public LoggingService(ILogger<LoggingService> logger)
        {
            _logger = logger;
        }

        public void LogInformation(string message, params object[] args)
        {
            _logger.LogInformation(message, args);
        }

        public void LogWarning(string message, params object[] args)
        {
            _logger.LogWarning(message, args);
        }

        public void LogError(string message, params object[] args)
        {
            _logger.LogError(message, args);
        }

        public void LogError(Exception exception, string message, params object[] args)
        {
            _logger.LogError(exception, message, args);
        }

        public void LogDebug(string message, params object[] args)
        {
            _logger.LogDebug(message, args);
        }

        public void LogCritical(string message, params object[] args)
        {
            _logger.LogCritical(message, args);
        }

        public void LogCritical(Exception exception, string message, params object[] args)
        {
            _logger.LogCritical(exception, message, args);
        }
    }

    // Extension methods for easier logging
    public static class LoggingExtensions
    {
        public static void LogUserAction(this ILoggingService logger, int userId, string action, string details = "")
        {
            logger.LogInformation("User {UserId} performed action: {Action}. Details: {Details}", 
                userId, action, details);
        }

        public static void LogBusinessOperation(this ILoggingService logger, string operation, object data)
        {
            logger.LogInformation("Business operation: {Operation}. Data: {@Data}", operation, data);
        }

        public static void LogSecurityEvent(this ILoggingService logger, string eventType, string details, int? userId = null)
        {
            if (userId.HasValue)
            {
                logger.LogWarning("Security event: {EventType} for User {UserId}. Details: {Details}", 
                    eventType, userId, details);
            }
            else
            {
                logger.LogWarning("Security event: {EventType}. Details: {Details}", eventType, details);
            }
        }

        public static void LogPerformanceMetric(this ILoggingService logger, string operation, long elapsedMilliseconds)
        {
            if (elapsedMilliseconds > 1000) // Log slow operations (> 1 second)
            {
                logger.LogWarning("Slow operation detected: {Operation} took {ElapsedMs}ms", 
                    operation, elapsedMilliseconds);
            }
            else
            {
                logger.LogDebug("Operation {Operation} completed in {ElapsedMs}ms", 
                    operation, elapsedMilliseconds);
            }
        }

        public static void LogDatabaseOperation(this ILoggingService logger, string operation, string table, int? recordId = null)
        {
            if (recordId.HasValue)
            {
                logger.LogDebug("Database operation: {Operation} on table {Table} for record {RecordId}", 
                    operation, table, recordId);
            }
            else
            {
                logger.LogDebug("Database operation: {Operation} on table {Table}", operation, table);
            }
        }

        public static void LogApiRequest(this ILoggingService logger, string method, string path, int? userId = null)
        {
            if (userId.HasValue)
            {
                logger.LogInformation("API Request: {Method} {Path} by User {UserId}", method, path, userId);
            }
            else
            {
                logger.LogInformation("API Request: {Method} {Path}", method, path);
            }
        }

        public static void LogApiResponse(this ILoggingService logger, string method, string path, int statusCode, long elapsedMs)
        {
            if (statusCode >= 400)
            {
                logger.LogWarning("API Response: {Method} {Path} returned {StatusCode} in {ElapsedMs}ms", 
                    method, path, statusCode, elapsedMs);
            }
            else
            {
                logger.LogInformation("API Response: {Method} {Path} returned {StatusCode} in {ElapsedMs}ms", 
                    method, path, statusCode, elapsedMs);
            }
        }
    }
}
