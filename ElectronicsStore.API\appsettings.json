{"ConnectionStrings": {"DefaultConnection": "Server=.;Database=ElectronicsStoreDB;Trusted_Connection=true;TrustServerCertificate=true;"}, "JwtSettings": {"SecretKey": "ElectronicsStore_SuperSecretKey_2024_ForJWT_Authentication_MinimumLength32Characters", "Issuer": "ElectronicsStoreAPI", "Audience": "ElectronicsStoreUsers", "ExpirationInMinutes": 60, "RefreshTokenExpirationInDays": 7}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Information"}}, "AllowedHosts": "*", "IpRateLimiting": {"EnableEndpointRateLimiting": true, "StackBlockedRequests": false, "RealIpHeader": "X-Real-IP", "ClientIdHeader": "X-ClientId", "HttpStatusCode": 429, "GeneralRules": [{"Endpoint": "*", "Period": "1m", "Limit": 100}, {"Endpoint": "*", "Period": "1h", "Limit": 1000}]}, "IpRateLimitPolicies": {"EndpointRules": [{"Endpoint": "POST:/api/auth/login", "Period": "1m", "Limit": 5}, {"Endpoint": "POST:/api/auth/register", "Period": "1m", "Limit": 3}, {"Endpoint": "GET:/api/products", "Period": "1m", "Limit": 50}]}}