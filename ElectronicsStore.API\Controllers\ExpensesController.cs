using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using ElectronicsStore.Application.Services;
using ElectronicsStore.Application.DTOs;
using System.Security.Claims;

namespace ElectronicsStore.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ExpensesController : BaseController
    {
        private readonly IExpenseService _expenseService;

        public ExpensesController(IExpenseService expenseService)
        {
            _expenseService = expenseService;
        }

        /// <summary>
        /// Get all expenses with pagination
        /// </summary>
        [HttpGet]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<ExpenseDto>>> GetExpenses(
            [FromQuery] int pageNumber = 1, 
            [FromQuery] int pageSize = 10)
        {
            try
            {
                var expenses = await _expenseService.GetAllExpensesAsync(pageNumber, pageSize);
                return Ok(expenses);
            }
            catch (Exception ex)
            {
                return HandleException(ex, "Error retrieving expenses");
            }
        }

        /// <summary>
        /// Get expense by ID
        /// </summary>
        [HttpGet("{id}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<ExpenseDto>> GetExpense(int id)
        {
            try
            {
                var expense = await _expenseService.GetExpenseByIdAsync(id);
                if (expense == null)
                {
                    return NotFound($"Expense with ID {id} not found.");
                }
                return Ok(expense);
            }
            catch (Exception ex)
            {
                return HandleException(ex, "Error retrieving expense");
            }
        }

        /// <summary>
        /// Create new expense
        /// </summary>
        [HttpPost]
        [Authorize(Roles = "admin,manager,cashier")]
        public async Task<ActionResult<ExpenseDto>> CreateExpense([FromBody] CreateExpenseDto createExpenseDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Set user ID from token if not provided
                if (createExpenseDto.UserId == 0)
                {
                    var userIdClaim = User.FindFirst("UserId")?.Value;
                    if (int.TryParse(userIdClaim, out int userId))
                    {
                        createExpenseDto.UserId = userId;
                    }
                    else
                    {
                        return BadRequest("User ID is required.");
                    }
                }

                var expense = await _expenseService.CreateExpenseAsync(createExpenseDto);
                return CreatedAtAction(nameof(GetExpense), new { id = expense.Id }, expense);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                return HandleException(ex, "Error creating expense");
            }
        }

        /// <summary>
        /// Update expense
        /// </summary>
        [HttpPut("{id}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<ExpenseDto>> UpdateExpense(int id, [FromBody] UpdateExpenseDto updateExpenseDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var expense = await _expenseService.UpdateExpenseAsync(id, updateExpenseDto);
                return Ok(expense);
            }
            catch (ArgumentException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                return HandleException(ex, "Error updating expense");
            }
        }

        /// <summary>
        /// Delete expense
        /// </summary>
        [HttpDelete("{id}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult> DeleteExpense(int id)
        {
            try
            {
                var result = await _expenseService.DeleteExpenseAsync(id);
                if (!result)
                {
                    return NotFound($"Expense with ID {id} not found.");
                }
                return NoContent();
            }
            catch (Exception ex)
            {
                return HandleException(ex, "Error deleting expense");
            }
        }

        /// <summary>
        /// Search expenses
        /// </summary>
        [HttpPost("search")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<ExpenseDto>>> SearchExpenses([FromBody] ExpenseSearchDto searchDto)
        {
            try
            {
                var expenses = await _expenseService.SearchExpensesAsync(searchDto);
                return Ok(expenses);
            }
            catch (Exception ex)
            {
                return HandleException(ex, "Error searching expenses");
            }
        }

        /// <summary>
        /// Get expenses by type
        /// </summary>
        [HttpGet("type/{expenseType}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<ExpenseDto>>> GetExpensesByType(
            string expenseType,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                var expenses = await _expenseService.GetExpensesByTypeAsync(expenseType, pageNumber, pageSize);
                return Ok(expenses);
            }
            catch (Exception ex)
            {
                return HandleException(ex, "Error retrieving expenses by type");
            }
        }

        /// <summary>
        /// Get expenses by user
        /// </summary>
        [HttpGet("user/{userId}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<ExpenseDto>>> GetExpensesByUser(
            int userId,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                var expenses = await _expenseService.GetExpensesByUserAsync(userId, pageNumber, pageSize);
                return Ok(expenses);
            }
            catch (Exception ex)
            {
                return HandleException(ex, "Error retrieving expenses by user");
            }
        }

        /// <summary>
        /// Get today's expenses
        /// </summary>
        [HttpGet("today")]
        [Authorize(Roles = "admin,manager,cashier")]
        public async Task<ActionResult<IEnumerable<ExpenseDto>>> GetTodayExpenses()
        {
            try
            {
                var expenses = await _expenseService.GetTodayExpensesAsync();
                return Ok(expenses);
            }
            catch (Exception ex)
            {
                return HandleException(ex, "Error retrieving today's expenses");
            }
        }

        /// <summary>
        /// Get today's total expenses
        /// </summary>
        [HttpGet("today/total")]
        [Authorize(Roles = "admin,manager,cashier")]
        public async Task<ActionResult<decimal>> GetTodayTotalExpenses()
        {
            try
            {
                var total = await _expenseService.GetTodayTotalExpensesAsync();
                return Ok(total);
            }
            catch (Exception ex)
            {
                return HandleException(ex, "Error retrieving today's total expenses");
            }
        }

        /// <summary>
        /// Get month expenses
        /// </summary>
        [HttpGet("month/{year}/{month}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<ExpenseDto>>> GetMonthExpenses(int year, int month)
        {
            try
            {
                if (month < 1 || month > 12)
                {
                    return BadRequest("Month must be between 1 and 12.");
                }

                var expenses = await _expenseService.GetMonthExpensesAsync(year, month);
                return Ok(expenses);
            }
            catch (Exception ex)
            {
                return HandleException(ex, "Error retrieving month expenses");
            }
        }

        /// <summary>
        /// Get month total expenses
        /// </summary>
        [HttpGet("month/{year}/{month}/total")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<decimal>> GetMonthTotalExpenses(int year, int month)
        {
            try
            {
                if (month < 1 || month > 12)
                {
                    return BadRequest("Month must be between 1 and 12.");
                }

                var total = await _expenseService.GetMonthTotalExpensesAsync(year, month);
                return Ok(total);
            }
            catch (Exception ex)
            {
                return HandleException(ex, "Error retrieving month total expenses");
            }
        }

        /// <summary>
        /// Get expense statistics
        /// </summary>
        [HttpGet("statistics")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<ExpenseStatisticsDto>> GetExpenseStatistics(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                var statistics = await _expenseService.GetExpenseStatisticsAsync(fromDate, toDate);
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                return HandleException(ex, "Error retrieving expense statistics");
            }
        }

        /// <summary>
        /// Get expense report
        /// </summary>
        [HttpGet("reports")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<ExpenseReportDto>> GetExpenseReport(
            [FromQuery] DateTime fromDate,
            [FromQuery] DateTime toDate)
        {
            try
            {
                if (fromDate == default || toDate == default)
                {
                    return BadRequest("From date and to date are required.");
                }

                if (fromDate > toDate)
                {
                    return BadRequest("From date cannot be greater than to date.");
                }

                var report = await _expenseService.GetExpenseReportAsync(fromDate, toDate);
                return Ok(report);
            }
            catch (Exception ex)
            {
                return HandleException(ex, "Error generating expense report");
            }
        }

        /// <summary>
        /// Get expense type breakdown
        /// </summary>
        [HttpGet("reports/type-breakdown")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<ExpenseTypeStatDto>>> GetExpenseTypeBreakdown(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                var breakdown = await _expenseService.GetExpenseTypeBreakdownAsync(fromDate, toDate);
                return Ok(breakdown);
            }
            catch (Exception ex)
            {
                return HandleException(ex, "Error retrieving expense type breakdown");
            }
        }

        /// <summary>
        /// Get monthly trend
        /// </summary>
        [HttpGet("reports/monthly-trend")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<MonthlyExpenseDto>>> GetMonthlyTrend(
            [FromQuery] int months = 12)
        {
            try
            {
                if (months < 1 || months > 24)
                {
                    return BadRequest("Months must be between 1 and 24.");
                }

                var trend = await _expenseService.GetMonthlyTrendAsync(months);
                return Ok(trend);
            }
            catch (Exception ex)
            {
                return HandleException(ex, "Error retrieving monthly trend");
            }
        }

        /// <summary>
        /// Get top expense types
        /// </summary>
        [HttpGet("reports/top-types")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<ExpenseTypeStatDto>>> GetTopExpenseTypes(
            [FromQuery] int count = 10,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                if (count < 1 || count > 50)
                {
                    return BadRequest("Count must be between 1 and 50.");
                }

                var topTypes = await _expenseService.GetTopExpenseTypesAsync(count, fromDate, toDate);
                return Ok(topTypes);
            }
            catch (Exception ex)
            {
                return HandleException(ex, "Error retrieving top expense types");
            }
        }

        /// <summary>
        /// Get top spending users
        /// </summary>
        [HttpGet("reports/top-users")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<UserExpenseDto>>> GetTopSpendingUsers(
            [FromQuery] int count = 10,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                if (count < 1 || count > 50)
                {
                    return BadRequest("Count must be between 1 and 50.");
                }

                var topUsers = await _expenseService.GetTopSpendingUsersAsync(count, fromDate, toDate);
                return Ok(topUsers);
            }
            catch (Exception ex)
            {
                return HandleException(ex, "Error retrieving top spending users");
            }
        }

        /// <summary>
        /// Get all expense types
        /// </summary>
        [HttpGet("types")]
        [Authorize(Roles = "admin,manager,cashier")]
        public async Task<ActionResult<IEnumerable<string>>> GetExpenseTypes()
        {
            try
            {
                var types = await _expenseService.GetExpenseTypesAsync();
                return Ok(types);
            }
            catch (Exception ex)
            {
                return HandleException(ex, "Error retrieving expense types");
            }
        }

        /// <summary>
        /// Get total expenses by type
        /// </summary>
        [HttpGet("types/{expenseType}/total")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<decimal>> GetTotalExpensesByType(
            string expenseType,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                var total = await _expenseService.GetTotalExpensesByTypeAsync(expenseType, fromDate, toDate);
                return Ok(total);
            }
            catch (Exception ex)
            {
                return HandleException(ex, "Error retrieving total expenses by type");
            }
        }
    }
}
