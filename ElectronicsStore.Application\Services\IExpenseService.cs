using ElectronicsStore.Application.DTOs;

namespace ElectronicsStore.Application.Services
{
    public interface IExpenseService
    {
        // Basic CRUD operations
        Task<IEnumerable<ExpenseDto>> GetAllExpensesAsync(int pageNumber = 1, int pageSize = 10);
        Task<ExpenseDto?> GetExpenseByIdAsync(int id);
        Task<ExpenseDto> CreateExpenseAsync(CreateExpenseDto createExpenseDto);
        Task<ExpenseDto> UpdateExpenseAsync(int id, UpdateExpenseDto updateExpenseDto);
        Task<bool> DeleteExpenseAsync(int id);

        // Search and filtering
        Task<IEnumerable<ExpenseDto>> SearchExpensesAsync(ExpenseSearchDto searchDto);
        Task<IEnumerable<ExpenseDto>> GetExpensesByTypeAsync(string expenseType, int pageNumber = 1, int pageSize = 10);
        Task<IEnumerable<ExpenseDto>> GetExpensesByUserAsync(int userId, int pageNumber = 1, int pageSize = 10);
        Task<IEnumerable<ExpenseDto>> GetExpensesByDateRangeAsync(DateTime fromDate, DateTime toDate, int pageNumber = 1, int pageSize = 10);

        // Today's expenses
        Task<IEnumerable<ExpenseDto>> GetTodayExpensesAsync();
        Task<decimal> GetTodayTotalExpensesAsync();

        // Monthly expenses
        Task<IEnumerable<ExpenseDto>> GetMonthExpensesAsync(int year, int month);
        Task<decimal> GetMonthTotalExpensesAsync(int year, int month);

        // Statistics and reports
        Task<ExpenseStatisticsDto> GetExpenseStatisticsAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<ExpenseReportDto> GetExpenseReportAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<ExpenseTypeStatDto>> GetExpenseTypeBreakdownAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<MonthlyExpenseDto>> GetMonthlyTrendAsync(int months = 12);

        // Top expense types and users
        Task<IEnumerable<ExpenseTypeStatDto>> GetTopExpenseTypesAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<UserExpenseDto>> GetTopSpendingUsersAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null);

        // Validation helpers
        Task<bool> ExpenseExistsAsync(int id);
        Task<IEnumerable<string>> GetExpenseTypesAsync();
        Task<decimal> GetTotalExpensesByTypeAsync(string expenseType, DateTime? fromDate = null, DateTime? toDate = null);
    }
}
