using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using FluentAssertions;
using System.Net.Http.Json;
using ElectronicsStore.Application.DTOs;
using ElectronicsStore.Persistence;
using ElectronicsStore.Domain.Entities;
using System.Net;

namespace ElectronicsStore.Tests.Integration
{
    public class CategoryIntegrationTests : IClassFixture<WebApplicationFactory<Program>>
    {
        private readonly WebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;

        public CategoryIntegrationTests(WebApplicationFactory<Program> factory)
        {
            _factory = factory.WithWebHostBuilder(builder =>
            {
                builder.ConfigureServices(services =>
                {
                    // Remove the existing DbContext registration
                    var descriptor = services.SingleOrDefault(d => d.ServiceType == typeof(DbContextOptions<ElectronicsDbContext>));
                    if (descriptor != null)
                        services.Remove(descriptor);

                    // Add InMemory database for testing
                    services.AddDbContext<ElectronicsDbContext>(options =>
                    {
                        options.UseInMemoryDatabase("TestDb");
                    });
                });
            });

            _client = _factory.CreateClient();
        }

        [Fact]
        public async Task GetCategories_ShouldReturnOkResult()
        {
            // Arrange
            await SeedTestData();
            await AuthenticateAsync();

            // Act
            var response = await _client.GetAsync("/api/categories");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            var categories = await response.Content.ReadFromJsonAsync<List<CategoryDto>>();
            categories.Should().NotBeNull();
            categories!.Should().HaveCountGreaterThan(0);
        }

        [Fact]
        public async Task GetCategoryById_WithValidId_ShouldReturnCategory()
        {
            // Arrange
            await SeedTestData();
            await AuthenticateAsync();

            // Act
            var response = await _client.GetAsync("/api/categories/1");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            var category = await response.Content.ReadFromJsonAsync<CategoryDto>();
            category.Should().NotBeNull();
            category!.Id.Should().Be(1);
        }

        [Fact]
        public async Task CreateCategory_WithValidData_ShouldCreateCategory()
        {
            // Arrange
            await AuthenticateAsAdmin();
            var createDto = new CreateCategoryDto { Name = "New Test Category" };

            // Act
            var response = await _client.PostAsJsonAsync("/api/categories", createDto);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Created);
            var category = await response.Content.ReadFromJsonAsync<CategoryDto>();
            category.Should().NotBeNull();
            category!.Name.Should().Be("New Test Category");
        }

        [Fact]
        public async Task CreateCategory_WithDuplicateName_ShouldReturnBadRequest()
        {
            // Arrange
            await SeedTestData();
            await AuthenticateAsAdmin();
            var createDto = new CreateCategoryDto { Name = "Electronics" }; // Duplicate name

            // Act
            var response = await _client.PostAsJsonAsync("/api/categories", createDto);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task UpdateCategory_WithValidData_ShouldUpdateCategory()
        {
            // Arrange
            await SeedTestData();
            await AuthenticateAsAdmin();
            var updateDto = new UpdateCategoryDto { Id = 1, Name = "Updated Electronics" };

            // Act
            var response = await _client.PutAsJsonAsync("/api/categories/1", updateDto);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            var category = await response.Content.ReadFromJsonAsync<CategoryDto>();
            category.Should().NotBeNull();
            category!.Name.Should().Be("Updated Electronics");
        }

        [Fact]
        public async Task DeleteCategory_WithValidId_ShouldDeleteCategory()
        {
            // Arrange
            await SeedTestData();
            await AuthenticateAsAdmin();

            // Act
            var response = await _client.DeleteAsync("/api/categories/2"); // Category without products

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.NoContent);
        }

        [Fact]
        public async Task DeleteCategory_WithProducts_ShouldReturnBadRequest()
        {
            // Arrange
            await SeedTestData();
            await AuthenticateAsAdmin();

            // Act
            var response = await _client.DeleteAsync("/api/categories/1"); // Category with products

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task GetCategories_WithoutAuthentication_ShouldReturnUnauthorized()
        {
            // Act
            var response = await _client.GetAsync("/api/categories");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
        }

        [Fact]
        public async Task CreateCategory_AsCashier_ShouldReturnForbidden()
        {
            // Arrange
            await AuthenticateAsCashier();
            var createDto = new CreateCategoryDto { Name = "Forbidden Category" };

            // Act
            var response = await _client.PostAsJsonAsync("/api/categories", createDto);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Forbidden);
        }

        private async Task SeedTestData()
        {
            using var scope = _factory.Services.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ElectronicsDbContext>();
            
            await context.Database.EnsureDeletedAsync();
            await context.Database.EnsureCreatedAsync();

            // Seed categories
            var categories = new List<Category>
            {
                new Category { Id = 1, Name = "Electronics" },
                new Category { Id = 2, Name = "Clothing" }
            };
            context.Categories.AddRange(categories);

            // Seed products (to test deletion constraints)
            var products = new List<Product>
            {
                new Product { Id = 1, Name = "Laptop", CategoryId = 1, Price = 1000, Quantity = 10, Barcode = "123456789" }
            };
            context.Products.AddRange(products);

            // Seed users and roles for authentication
            var roles = new List<Role>
            {
                new Role { Id = 1, Name = "admin" },
                new Role { Id = 2, Name = "manager" },
                new Role { Id = 3, Name = "cashier" }
            };
            context.Roles.AddRange(roles);

            var users = new List<User>
            {
                new User { Id = 1, Username = "admin", Email = "<EMAIL>", PasswordHash = BCrypt.Net.BCrypt.HashPassword("password"), RoleId = 1 },
                new User { Id = 2, Username = "cashier", Email = "<EMAIL>", PasswordHash = BCrypt.Net.BCrypt.HashPassword("password"), RoleId = 3 }
            };
            context.Users.AddRange(users);

            await context.SaveChangesAsync();
        }

        private async Task AuthenticateAsync()
        {
            await AuthenticateAsAdmin();
        }

        private async Task AuthenticateAsAdmin()
        {
            var loginDto = new { Username = "admin", Password = "password" };
            var response = await _client.PostAsJsonAsync("/api/auth/login", loginDto);
            
            if (response.IsSuccessStatusCode)
            {
                var loginResponse = await response.Content.ReadFromJsonAsync<dynamic>();
                var token = loginResponse?.GetProperty("token").GetString();
                _client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            }
        }

        private async Task AuthenticateAsCashier()
        {
            var loginDto = new { Username = "cashier", Password = "password" };
            var response = await _client.PostAsJsonAsync("/api/auth/login", loginDto);
            
            if (response.IsSuccessStatusCode)
            {
                var loginResponse = await response.Content.ReadFromJsonAsync<dynamic>();
                var token = loginResponse?.GetProperty("token").GetString();
                _client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            }
        }
    }
}
