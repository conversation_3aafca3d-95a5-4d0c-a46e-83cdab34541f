{"ConnectionStrings": {"DefaultConnection": "Server=.;Database=ElectronicsStoreDB;Trusted_Connection=true;TrustServerCertificate=true;"}, "JwtSettings": {"SecretKey": "ElectronicsStore_SuperSecretKey_2024_ForJWT_Authentication_MinimumLength32Characters", "Issuer": "ElectronicsStoreAPI", "Audience": "ElectronicsStoreUsers", "ExpirationInMinutes": 60, "RefreshTokenExpirationInDays": 7}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Information"}}, "AllowedHosts": "*"}