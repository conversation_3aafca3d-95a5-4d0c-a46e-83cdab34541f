using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ElectronicsStore.Domain.Entities
{
    [Table("purchase_invoices")]
    public class PurchaseInvoice
    {
        [Key]
        [Column("id")]
        public int Id { get; set; }

        [Required]
        [MaxLength(50)]
        [Column("invoice_number")]
        public string InvoiceNumber { get; set; } = string.Empty;

        [Column("supplier_id")]
        public int SupplierId { get; set; }

        [Column("invoice_date")]
        public DateTime InvoiceDate { get; set; } = DateTime.Now;

        [Column("user_id")]
        public int UserId { get; set; }

        [Column("total_amount", TypeName = "decimal(14,2)")]
        public decimal TotalAmount { get; set; }

        // Navigation properties
        [ForeignKey("SupplierId")]
        public virtual Supplier Supplier { get; set; } = null!;

        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        public virtual ICollection<PurchaseInvoiceDetail> PurchaseInvoiceDetails { get; set; } = new List<PurchaseInvoiceDetail>();
        public virtual ICollection<PurchaseReturn> PurchaseReturns { get; set; } = new List<PurchaseReturn>();
    }
}
