namespace ElectronicsStore.Application.DTOs
{
    public class SalesInvoiceDto
    {
        public int Id { get; set; }
        public string InvoiceNumber { get; set; } = string.Empty;
        public string? CustomerName { get; set; }
        public DateTime InvoiceDate { get; set; }
        public decimal DiscountTotal { get; set; }
        public decimal TotalAmount { get; set; }
        public string PaymentMethod { get; set; } = string.Empty;
        public int? OverrideByUserId { get; set; }
        public string? OverrideByUserName { get; set; }
        public DateTime? OverrideDate { get; set; }
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public List<SalesInvoiceDetailDto> Details { get; set; } = new();
    }

    public class SalesInvoiceDetailDto
    {
        public int Id { get; set; }
        public int SalesInvoiceId { get; set; }
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public int Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal LineTotal { get; set; }
    }

    public class CreateSalesInvoiceDto
    {
        public string InvoiceNumber { get; set; } = string.Empty;
        public string? CustomerName { get; set; }
        public decimal DiscountTotal { get; set; } = 0;
        public string PaymentMethod { get; set; } = string.Empty;
        public int UserId { get; set; }
        public List<CreateSalesInvoiceDetailDto> Details { get; set; } = new();
    }

    public class CreateSalesInvoiceDetailDto
    {
        public int ProductId { get; set; }
        public int Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal DiscountAmount { get; set; } = 0;
    }

    public class SalesReportDto
    {
        public DateTime Date { get; set; }
        public int TotalSales { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal TotalDiscount { get; set; }
        public decimal NetAmount { get; set; }
    }

    public class TopCustomerDto
    {
        public string CustomerName { get; set; } = string.Empty;
        public int TotalPurchases { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal AverageAmount { get; set; }
        public DateTime LastPurchase { get; set; }
    }

    public class TopProductDto
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public int TotalQuantity { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal AveragePrice { get; set; }
    }

    public class SalesSearchDto
    {
        public string? SearchTerm { get; set; }
        public string? CustomerName { get; set; }
        public string? PaymentMethod { get; set; }
        public int? UserId { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public decimal? MinAmount { get; set; }
        public decimal? MaxAmount { get; set; }
    }
}
