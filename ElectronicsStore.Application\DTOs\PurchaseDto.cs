namespace ElectronicsStore.Application.DTOs
{
    public class PurchaseInvoiceDto
    {
        public int Id { get; set; }
        public string InvoiceNumber { get; set; } = string.Empty;
        public int SupplierId { get; set; }
        public string SupplierName { get; set; } = string.Empty;
        public DateTime InvoiceDate { get; set; }
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public decimal TotalAmount { get; set; }
        public List<PurchaseInvoiceDetailDto> Details { get; set; } = new();
    }

    public class CreatePurchaseInvoiceDto
    {
        public string InvoiceNumber { get; set; } = string.Empty;
        public int SupplierId { get; set; }
        public DateTime InvoiceDate { get; set; } = DateTime.Now;
        public List<CreatePurchaseInvoiceDetailDto> Details { get; set; } = new();
    }

    public class UpdatePurchaseInvoiceDto
    {
        public int Id { get; set; }
        public string InvoiceNumber { get; set; } = string.Empty;
        public int SupplierId { get; set; }
        public DateTime InvoiceDate { get; set; }
        public List<UpdatePurchaseInvoiceDetailDto> Details { get; set; } = new();
    }

    public class PurchaseInvoiceDetailDto
    {
        public int Id { get; set; }
        public int PurchaseInvoiceId { get; set; }
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public string? ProductBarcode { get; set; }
        public int Quantity { get; set; }
        public decimal UnitCost { get; set; }
        public decimal LineTotal { get; set; }
    }

    public class CreatePurchaseInvoiceDetailDto
    {
        public int ProductId { get; set; }
        public int Quantity { get; set; }
        public decimal UnitCost { get; set; }
    }

    public class UpdatePurchaseInvoiceDetailDto
    {
        public int Id { get; set; }
        public int ProductId { get; set; }
        public int Quantity { get; set; }
        public decimal UnitCost { get; set; }
    }

    public class PurchaseSearchDto
    {
        public string? InvoiceNumber { get; set; }
        public int? SupplierId { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public decimal? MinAmount { get; set; }
        public decimal? MaxAmount { get; set; }
        public int? UserId { get; set; }
    }

    public class PurchaseReportDto
    {
        public DateTime Date { get; set; }
        public int InvoiceCount { get; set; }
        public decimal TotalAmount { get; set; }
        public int TotalItems { get; set; }
        public decimal AverageInvoiceAmount { get; set; }
    }

    public class PurchaseStatisticsDto
    {
        public int TotalInvoices { get; set; }
        public decimal TotalAmount { get; set; }
        public int TotalItems { get; set; }
        public decimal AverageInvoiceAmount { get; set; }
        public int TotalSuppliers { get; set; }
        public decimal TodayAmount { get; set; }
        public decimal MonthAmount { get; set; }
        public decimal YearAmount { get; set; }
    }
}
