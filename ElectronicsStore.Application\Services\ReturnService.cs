using ElectronicsStore.Application.DTOs;
using ElectronicsStore.Application.Interfaces;
using ElectronicsStore.Domain.Entities;

namespace ElectronicsStore.Application.Services
{
    public class ReturnService : IReturnService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IInventoryService _inventoryService;

        public ReturnService(IUnitOfWork unitOfWork, IInventoryService inventoryService)
        {
            _unitOfWork = unitOfWork;
            _inventoryService = inventoryService;
        }

        #region Sales Returns

        public async Task<SalesReturnDto> CreateSalesReturnAsync(CreateSalesReturnDto createDto, int userId)
        {
            // Validate sales invoice exists
            var salesInvoice = await _unitOfWork.SalesInvoices.GetByIdAsync(createDto.SalesInvoiceId);
            if (salesInvoice == null)
                throw new InvalidOperationException($"Sales invoice with ID {createDto.SalesInvoiceId} not found.");

            // Get the sales invoice detail for this product
            var salesDetail = await _unitOfWork.SalesInvoiceDetails.FirstOrDefaultAsync(
                d => d.SalesInvoiceId == createDto.SalesInvoiceId && d.ProductId == createDto.ProductId);
            
            if (salesDetail == null)
                throw new InvalidOperationException($"Product with ID {createDto.ProductId} not found in sales invoice {createDto.SalesInvoiceId}.");

            // Check if return quantity is valid
            var existingReturns = await _unitOfWork.SalesReturns.FindAsync(
                r => r.SalesInvoiceId == createDto.SalesInvoiceId && r.ProductId == createDto.ProductId);
            var totalReturned = existingReturns.Sum(r => r.Quantity);
            var maxReturnable = salesDetail.Quantity - totalReturned;

            if (createDto.Quantity > maxReturnable)
                throw new InvalidOperationException($"Cannot return {createDto.Quantity} items. Maximum returnable quantity is {maxReturnable}.");

            if (createDto.Quantity <= 0)
                throw new InvalidOperationException("Return quantity must be greater than zero.");

            // Validate product exists
            var product = await _unitOfWork.Products.GetByIdAsync(createDto.ProductId);
            if (product == null)
                throw new InvalidOperationException($"Product with ID {createDto.ProductId} not found.");

            // Create sales return
            var salesReturn = new SalesReturn
            {
                SalesInvoiceId = createDto.SalesInvoiceId,
                ProductId = createDto.ProductId,
                Quantity = createDto.Quantity,
                UnitPrice = salesDetail.UnitPrice,
                Reason = createDto.Reason,
                UserId = userId,
                CreatedAt = DateTime.Now
            };

            await _unitOfWork.SalesReturns.AddAsync(salesReturn);

            // Update inventory - add back to stock
            await _inventoryService.CreateInventoryLogAsync(new CreateInventoryLogDto
            {
                ProductId = createDto.ProductId,
                MovementType = "return_sale",
                Quantity = createDto.Quantity,
                UnitCost = salesDetail.UnitPrice, // Use original sale price as reference
                ReferenceTable = "sales_returns",
                ReferenceId = salesReturn.Id,
                Note = $"Sales return from invoice {salesInvoice.InvoiceNumber}. Reason: {createDto.Reason}",
                UserId = userId
            });

            await _unitOfWork.SaveChangesAsync();

            // Return DTO
            var user = await _unitOfWork.Users.GetByIdAsync(userId);
            return new SalesReturnDto
            {
                Id = salesReturn.Id,
                SalesInvoiceId = salesReturn.SalesInvoiceId,
                SalesInvoiceNumber = salesInvoice.InvoiceNumber,
                ProductId = salesReturn.ProductId,
                ProductName = product.Name,
                ProductBarcode = product.Barcode,
                Quantity = salesReturn.Quantity,
                UnitPrice = salesReturn.UnitPrice,
                TotalAmount = salesReturn.Quantity * salesReturn.UnitPrice,
                Reason = salesReturn.Reason,
                UserId = salesReturn.UserId,
                UserName = user?.Username ?? "Unknown",
                CreatedAt = salesReturn.CreatedAt
            };
        }

        public async Task<SalesReturnDto?> GetSalesReturnByIdAsync(int id)
        {
            var salesReturn = await _unitOfWork.SalesReturns.GetByIdAsync(id);
            if (salesReturn == null)
                return null;

            return await MapToSalesReturnDto(salesReturn);
        }

        public async Task<IEnumerable<SalesReturnDto>> GetAllSalesReturnsAsync()
        {
            var salesReturns = await _unitOfWork.SalesReturns.GetAllAsync();
            var returnDtos = new List<SalesReturnDto>();

            foreach (var salesReturn in salesReturns.OrderByDescending(r => r.CreatedAt))
            {
                returnDtos.Add(await MapToSalesReturnDto(salesReturn));
            }

            return returnDtos;
        }

        public async Task<IEnumerable<SalesReturnDto>> GetSalesReturnsByInvoiceAsync(int salesInvoiceId)
        {
            var salesReturns = await _unitOfWork.SalesReturns.FindAsync(r => r.SalesInvoiceId == salesInvoiceId);
            var returnDtos = new List<SalesReturnDto>();

            foreach (var salesReturn in salesReturns.OrderByDescending(r => r.CreatedAt))
            {
                returnDtos.Add(await MapToSalesReturnDto(salesReturn));
            }

            return returnDtos;
        }

        public async Task<IEnumerable<SalesReturnDto>> GetSalesReturnsByProductAsync(int productId)
        {
            var salesReturns = await _unitOfWork.SalesReturns.FindAsync(r => r.ProductId == productId);
            var returnDtos = new List<SalesReturnDto>();

            foreach (var salesReturn in salesReturns.OrderByDescending(r => r.CreatedAt))
            {
                returnDtos.Add(await MapToSalesReturnDto(salesReturn));
            }

            return returnDtos;
        }

        public async Task<IEnumerable<SalesReturnDto>> GetSalesReturnsByUserAsync(int userId)
        {
            var salesReturns = await _unitOfWork.SalesReturns.FindAsync(r => r.UserId == userId);
            var returnDtos = new List<SalesReturnDto>();

            foreach (var salesReturn in salesReturns.OrderByDescending(r => r.CreatedAt))
            {
                returnDtos.Add(await MapToSalesReturnDto(salesReturn));
            }

            return returnDtos;
        }

        public async Task<bool> DeleteSalesReturnAsync(int id)
        {
            var salesReturn = await _unitOfWork.SalesReturns.GetByIdAsync(id);
            if (salesReturn == null)
                return false;

            // Reverse inventory movement
            await _inventoryService.CreateInventoryLogAsync(new CreateInventoryLogDto
            {
                ProductId = salesReturn.ProductId,
                MovementType = "adjust",
                Quantity = -salesReturn.Quantity, // Negative to reverse
                UnitCost = salesReturn.UnitPrice,
                ReferenceTable = "sales_returns",
                ReferenceId = salesReturn.Id,
                Note = $"Reversed sales return (deleted)",
                UserId = salesReturn.UserId
            });

            _unitOfWork.SalesReturns.Remove(salesReturn);
            await _unitOfWork.SaveChangesAsync();

            return true;
        }

        #endregion

        #region Purchase Returns

        public async Task<PurchaseReturnDto> CreatePurchaseReturnAsync(CreatePurchaseReturnDto createDto, int userId)
        {
            // Validate purchase invoice exists
            var purchaseInvoice = await _unitOfWork.PurchaseInvoices.GetByIdAsync(createDto.PurchaseInvoiceId);
            if (purchaseInvoice == null)
                throw new InvalidOperationException($"Purchase invoice with ID {createDto.PurchaseInvoiceId} not found.");

            // Get the purchase invoice detail for this product
            var purchaseDetail = await _unitOfWork.PurchaseInvoiceDetails.FirstOrDefaultAsync(
                d => d.PurchaseInvoiceId == createDto.PurchaseInvoiceId && d.ProductId == createDto.ProductId);
            
            if (purchaseDetail == null)
                throw new InvalidOperationException($"Product with ID {createDto.ProductId} not found in purchase invoice {createDto.PurchaseInvoiceId}.");

            // Check if return quantity is valid
            var existingReturns = await _unitOfWork.PurchaseReturns.FindAsync(
                r => r.PurchaseInvoiceId == createDto.PurchaseInvoiceId && r.ProductId == createDto.ProductId);
            var totalReturned = existingReturns.Sum(r => r.Quantity);
            var maxReturnable = purchaseDetail.Quantity - totalReturned;

            if (createDto.Quantity > maxReturnable)
                throw new InvalidOperationException($"Cannot return {createDto.Quantity} items. Maximum returnable quantity is {maxReturnable}.");

            if (createDto.Quantity <= 0)
                throw new InvalidOperationException("Return quantity must be greater than zero.");

            // Validate product exists
            var product = await _unitOfWork.Products.GetByIdAsync(createDto.ProductId);
            if (product == null)
                throw new InvalidOperationException($"Product with ID {createDto.ProductId} not found.");

            // Create purchase return
            var purchaseReturn = new PurchaseReturn
            {
                PurchaseInvoiceId = createDto.PurchaseInvoiceId,
                ProductId = createDto.ProductId,
                Quantity = createDto.Quantity,
                UnitCost = purchaseDetail.UnitCost,
                Reason = createDto.Reason,
                UserId = userId,
                CreatedAt = DateTime.Now
            };

            await _unitOfWork.PurchaseReturns.AddAsync(purchaseReturn);

            // Update inventory - remove from stock
            await _inventoryService.CreateInventoryLogAsync(new CreateInventoryLogDto
            {
                ProductId = createDto.ProductId,
                MovementType = "return_purchase",
                Quantity = -createDto.Quantity, // Negative because we're returning to supplier
                UnitCost = purchaseDetail.UnitCost,
                ReferenceTable = "purchase_returns",
                ReferenceId = purchaseReturn.Id,
                Note = $"Purchase return to supplier from invoice {purchaseInvoice.InvoiceNumber}. Reason: {createDto.Reason}",
                UserId = userId
            });

            await _unitOfWork.SaveChangesAsync();

            // Return DTO
            var user = await _unitOfWork.Users.GetByIdAsync(userId);
            return new PurchaseReturnDto
            {
                Id = purchaseReturn.Id,
                PurchaseInvoiceId = purchaseReturn.PurchaseInvoiceId,
                PurchaseInvoiceNumber = purchaseInvoice.InvoiceNumber,
                ProductId = purchaseReturn.ProductId,
                ProductName = product.Name,
                ProductBarcode = product.Barcode,
                Quantity = purchaseReturn.Quantity,
                UnitCost = purchaseReturn.UnitCost,
                TotalAmount = purchaseReturn.Quantity * purchaseReturn.UnitCost,
                Reason = purchaseReturn.Reason,
                UserId = purchaseReturn.UserId,
                UserName = user?.Username ?? "Unknown",
                CreatedAt = purchaseReturn.CreatedAt
            };
        }

        public async Task<PurchaseReturnDto?> GetPurchaseReturnByIdAsync(int id)
        {
            var purchaseReturn = await _unitOfWork.PurchaseReturns.GetByIdAsync(id);
            if (purchaseReturn == null)
                return null;

            return await MapToPurchaseReturnDto(purchaseReturn);
        }

        public async Task<IEnumerable<PurchaseReturnDto>> GetAllPurchaseReturnsAsync()
        {
            var purchaseReturns = await _unitOfWork.PurchaseReturns.GetAllAsync();
            var returnDtos = new List<PurchaseReturnDto>();

            foreach (var purchaseReturn in purchaseReturns.OrderByDescending(r => r.CreatedAt))
            {
                returnDtos.Add(await MapToPurchaseReturnDto(purchaseReturn));
            }

            return returnDtos;
        }

        public async Task<IEnumerable<PurchaseReturnDto>> GetPurchaseReturnsByInvoiceAsync(int purchaseInvoiceId)
        {
            var purchaseReturns = await _unitOfWork.PurchaseReturns.FindAsync(r => r.PurchaseInvoiceId == purchaseInvoiceId);
            var returnDtos = new List<PurchaseReturnDto>();

            foreach (var purchaseReturn in purchaseReturns.OrderByDescending(r => r.CreatedAt))
            {
                returnDtos.Add(await MapToPurchaseReturnDto(purchaseReturn));
            }

            return returnDtos;
        }

        public async Task<IEnumerable<PurchaseReturnDto>> GetPurchaseReturnsByProductAsync(int productId)
        {
            var purchaseReturns = await _unitOfWork.PurchaseReturns.FindAsync(r => r.ProductId == productId);
            var returnDtos = new List<PurchaseReturnDto>();

            foreach (var purchaseReturn in purchaseReturns.OrderByDescending(r => r.CreatedAt))
            {
                returnDtos.Add(await MapToPurchaseReturnDto(purchaseReturn));
            }

            return returnDtos;
        }

        public async Task<IEnumerable<PurchaseReturnDto>> GetPurchaseReturnsByUserAsync(int userId)
        {
            var purchaseReturns = await _unitOfWork.PurchaseReturns.FindAsync(r => r.UserId == userId);
            var returnDtos = new List<PurchaseReturnDto>();

            foreach (var purchaseReturn in purchaseReturns.OrderByDescending(r => r.CreatedAt))
            {
                returnDtos.Add(await MapToPurchaseReturnDto(purchaseReturn));
            }

            return returnDtos;
        }

        public async Task<bool> DeletePurchaseReturnAsync(int id)
        {
            var purchaseReturn = await _unitOfWork.PurchaseReturns.GetByIdAsync(id);
            if (purchaseReturn == null)
                return false;

            // Reverse inventory movement
            await _inventoryService.CreateInventoryLogAsync(new CreateInventoryLogDto
            {
                ProductId = purchaseReturn.ProductId,
                MovementType = "adjust",
                Quantity = purchaseReturn.Quantity, // Positive to reverse the negative return
                UnitCost = purchaseReturn.UnitCost,
                ReferenceTable = "purchase_returns",
                ReferenceId = purchaseReturn.Id,
                Note = $"Reversed purchase return (deleted)",
                UserId = purchaseReturn.UserId
            });

            _unitOfWork.PurchaseReturns.Remove(purchaseReturn);
            await _unitOfWork.SaveChangesAsync();

            return true;
        }

        #endregion

        #region Combined Operations

        public async Task<IEnumerable<object>> GetAllReturnsAsync()
        {
            var salesReturns = await GetAllSalesReturnsAsync();
            var purchaseReturns = await GetAllPurchaseReturnsAsync();

            var combinedReturns = new List<object>();

            foreach (var salesReturn in salesReturns)
            {
                combinedReturns.Add(new
                {
                    Type = "Sales",
                    Id = salesReturn.Id,
                    InvoiceId = salesReturn.SalesInvoiceId,
                    InvoiceNumber = salesReturn.SalesInvoiceNumber,
                    ProductId = salesReturn.ProductId,
                    ProductName = salesReturn.ProductName,
                    ProductBarcode = salesReturn.ProductBarcode,
                    Quantity = salesReturn.Quantity,
                    UnitPrice = salesReturn.UnitPrice,
                    TotalAmount = salesReturn.TotalAmount,
                    Reason = salesReturn.Reason,
                    UserId = salesReturn.UserId,
                    UserName = salesReturn.UserName,
                    CreatedAt = salesReturn.CreatedAt
                });
            }

            foreach (var purchaseReturn in purchaseReturns)
            {
                combinedReturns.Add(new
                {
                    Type = "Purchase",
                    Id = purchaseReturn.Id,
                    InvoiceId = purchaseReturn.PurchaseInvoiceId,
                    InvoiceNumber = purchaseReturn.PurchaseInvoiceNumber,
                    ProductId = purchaseReturn.ProductId,
                    ProductName = purchaseReturn.ProductName,
                    ProductBarcode = purchaseReturn.ProductBarcode,
                    Quantity = purchaseReturn.Quantity,
                    UnitPrice = purchaseReturn.UnitCost,
                    TotalAmount = purchaseReturn.TotalAmount,
                    Reason = purchaseReturn.Reason,
                    UserId = purchaseReturn.UserId,
                    UserName = purchaseReturn.UserName,
                    CreatedAt = purchaseReturn.CreatedAt
                });
            }

            return combinedReturns.OrderByDescending(r => ((dynamic)r).CreatedAt);
        }

        public async Task<IEnumerable<object>> SearchReturnsAsync(ReturnSearchDto searchDto)
        {
            var allReturns = await GetAllReturnsAsync();
            var filteredReturns = allReturns.AsEnumerable();

            if (!string.IsNullOrEmpty(searchDto.ReturnType))
            {
                filteredReturns = filteredReturns.Where(r => ((dynamic)r).Type.ToString().Equals(searchDto.ReturnType, StringComparison.OrdinalIgnoreCase));
            }

            if (searchDto.InvoiceId.HasValue)
            {
                filteredReturns = filteredReturns.Where(r => ((dynamic)r).InvoiceId == searchDto.InvoiceId.Value);
            }

            if (searchDto.ProductId.HasValue)
            {
                filteredReturns = filteredReturns.Where(r => ((dynamic)r).ProductId == searchDto.ProductId.Value);
            }

            if (searchDto.FromDate.HasValue)
            {
                filteredReturns = filteredReturns.Where(r => ((DateTime)((dynamic)r).CreatedAt).Date >= searchDto.FromDate.Value.Date);
            }

            if (searchDto.ToDate.HasValue)
            {
                filteredReturns = filteredReturns.Where(r => ((DateTime)((dynamic)r).CreatedAt).Date <= searchDto.ToDate.Value.Date);
            }

            if (!string.IsNullOrEmpty(searchDto.Reason))
            {
                filteredReturns = filteredReturns.Where(r =>
                    !string.IsNullOrEmpty(((dynamic)r).Reason?.ToString()) &&
                    ((dynamic)r).Reason.ToString().Contains(searchDto.Reason, StringComparison.OrdinalIgnoreCase));
            }

            if (searchDto.UserId.HasValue)
            {
                filteredReturns = filteredReturns.Where(r => ((dynamic)r).UserId == searchDto.UserId.Value);
            }

            return filteredReturns.OrderByDescending(r => ((dynamic)r).CreatedAt);
        }

        public async Task<IEnumerable<object>> GetReturnsByDateRangeAsync(DateTime fromDate, DateTime toDate)
        {
            var searchDto = new ReturnSearchDto
            {
                FromDate = fromDate,
                ToDate = toDate
            };

            return await SearchReturnsAsync(searchDto);
        }

        public async Task<IEnumerable<object>> GetTodayReturnsAsync()
        {
            var today = DateTime.Today;
            return await GetReturnsByDateRangeAsync(today, today);
        }

        public async Task<ReturnStatisticsDto> GetReturnStatisticsAsync()
        {
            var salesReturns = await _unitOfWork.SalesReturns.GetAllAsync();
            var purchaseReturns = await _unitOfWork.PurchaseReturns.GetAllAsync();

            var today = DateTime.Today;
            var startOfMonth = new DateTime(today.Year, today.Month, 1);

            var todaySalesReturns = salesReturns.Where(r => r.CreatedAt.Date == today);
            var todayPurchaseReturns = purchaseReturns.Where(r => r.CreatedAt.Date == today);
            var monthSalesReturns = salesReturns.Where(r => r.CreatedAt.Date >= startOfMonth);
            var monthPurchaseReturns = purchaseReturns.Where(r => r.CreatedAt.Date >= startOfMonth);

            // Calculate return rate (simplified - could be more sophisticated)
            var totalSales = await _unitOfWork.SalesInvoices.GetAllAsync();
            var totalPurchases = await _unitOfWork.PurchaseInvoices.GetAllAsync();
            var returnRate = totalSales.Any() ?
                (decimal)(salesReturns.Count() + purchaseReturns.Count()) / (totalSales.Count() + totalPurchases.Count()) * 100 : 0;

            return new ReturnStatisticsDto
            {
                TotalSalesReturns = salesReturns.Count(),
                TotalPurchaseReturns = purchaseReturns.Count(),
                TotalSalesReturnAmount = salesReturns.Sum(r => r.Quantity * r.UnitPrice),
                TotalPurchaseReturnAmount = purchaseReturns.Sum(r => r.Quantity * r.UnitCost),
                TotalReturnedItems = salesReturns.Sum(r => r.Quantity) + purchaseReturns.Sum(r => r.Quantity),
                TodaySalesReturns = todaySalesReturns.Sum(r => r.Quantity * r.UnitPrice),
                TodayPurchaseReturns = todayPurchaseReturns.Sum(r => r.Quantity * r.UnitCost),
                MonthSalesReturns = monthSalesReturns.Sum(r => r.Quantity * r.UnitPrice),
                MonthPurchaseReturns = monthPurchaseReturns.Sum(r => r.Quantity * r.UnitCost),
                ReturnRate = returnRate
            };
        }

        public async Task<IEnumerable<ReturnReportDto>> GetDailyReturnReportAsync(DateTime date)
        {
            var salesReturns = await _unitOfWork.SalesReturns.FindAsync(r => r.CreatedAt.Date == date.Date);
            var purchaseReturns = await _unitOfWork.PurchaseReturns.FindAsync(r => r.CreatedAt.Date == date.Date);

            var report = new List<ReturnReportDto>();
            if (salesReturns.Any() || purchaseReturns.Any())
            {
                report.Add(new ReturnReportDto
                {
                    Date = date.Date,
                    SalesReturnsCount = salesReturns.Count(),
                    PurchaseReturnsCount = purchaseReturns.Count(),
                    SalesReturnAmount = salesReturns.Sum(r => r.Quantity * r.UnitPrice),
                    PurchaseReturnAmount = purchaseReturns.Sum(r => r.Quantity * r.UnitCost),
                    TotalReturnedItems = salesReturns.Sum(r => r.Quantity) + purchaseReturns.Sum(r => r.Quantity)
                });
            }

            return report;
        }

        public async Task<IEnumerable<ReturnReportDto>> GetMonthlyReturnReportAsync(int year, int month)
        {
            var startDate = new DateTime(year, month, 1);
            var endDate = startDate.AddMonths(1).AddDays(-1);

            var salesReturns = await _unitOfWork.SalesReturns.FindAsync(r =>
                r.CreatedAt.Date >= startDate && r.CreatedAt.Date <= endDate);
            var purchaseReturns = await _unitOfWork.PurchaseReturns.FindAsync(r =>
                r.CreatedAt.Date >= startDate && r.CreatedAt.Date <= endDate);

            var dailyReports = new List<ReturnReportDto>();
            for (var date = startDate; date <= endDate; date = date.AddDays(1))
            {
                var daySalesReturns = salesReturns.Where(r => r.CreatedAt.Date == date);
                var dayPurchaseReturns = purchaseReturns.Where(r => r.CreatedAt.Date == date);

                if (daySalesReturns.Any() || dayPurchaseReturns.Any())
                {
                    dailyReports.Add(new ReturnReportDto
                    {
                        Date = date,
                        SalesReturnsCount = daySalesReturns.Count(),
                        PurchaseReturnsCount = dayPurchaseReturns.Count(),
                        SalesReturnAmount = daySalesReturns.Sum(r => r.Quantity * r.UnitPrice),
                        PurchaseReturnAmount = dayPurchaseReturns.Sum(r => r.Quantity * r.UnitCost),
                        TotalReturnedItems = daySalesReturns.Sum(r => r.Quantity) + dayPurchaseReturns.Sum(r => r.Quantity)
                    });
                }
            }

            return dailyReports.OrderBy(r => r.Date);
        }

        public async Task<IEnumerable<ReturnReportDto>> GetYearlyReturnReportAsync(int year)
        {
            var startDate = new DateTime(year, 1, 1);
            var endDate = new DateTime(year, 12, 31);

            var salesReturns = await _unitOfWork.SalesReturns.FindAsync(r =>
                r.CreatedAt.Date >= startDate && r.CreatedAt.Date <= endDate);
            var purchaseReturns = await _unitOfWork.PurchaseReturns.FindAsync(r =>
                r.CreatedAt.Date >= startDate && r.CreatedAt.Date <= endDate);

            var monthlyReports = new List<ReturnReportDto>();
            for (int month = 1; month <= 12; month++)
            {
                var monthStart = new DateTime(year, month, 1);
                var monthEnd = monthStart.AddMonths(1).AddDays(-1);

                var monthSalesReturns = salesReturns.Where(r => r.CreatedAt.Month == month);
                var monthPurchaseReturns = purchaseReturns.Where(r => r.CreatedAt.Month == month);

                if (monthSalesReturns.Any() || monthPurchaseReturns.Any())
                {
                    monthlyReports.Add(new ReturnReportDto
                    {
                        Date = monthStart,
                        SalesReturnsCount = monthSalesReturns.Count(),
                        PurchaseReturnsCount = monthPurchaseReturns.Count(),
                        SalesReturnAmount = monthSalesReturns.Sum(r => r.Quantity * r.UnitPrice),
                        PurchaseReturnAmount = monthPurchaseReturns.Sum(r => r.Quantity * r.UnitCost),
                        TotalReturnedItems = monthSalesReturns.Sum(r => r.Quantity) + monthPurchaseReturns.Sum(r => r.Quantity)
                    });
                }
            }

            return monthlyReports.OrderBy(r => r.Date);
        }

        #endregion

        #region Analytics and Validation

        public async Task<IEnumerable<TopReturnedProductDto>> GetTopReturnedProductsAsync(int count = 10)
        {
            var salesReturns = await _unitOfWork.SalesReturns.GetAllAsync();
            var purchaseReturns = await _unitOfWork.PurchaseReturns.GetAllAsync();

            var productStats = new Dictionary<int, TopReturnedProductDto>();

            // Process sales returns
            foreach (var salesReturn in salesReturns)
            {
                if (!productStats.ContainsKey(salesReturn.ProductId))
                {
                    var product = await _unitOfWork.Products.GetByIdAsync(salesReturn.ProductId);
                    productStats[salesReturn.ProductId] = new TopReturnedProductDto
                    {
                        ProductId = salesReturn.ProductId,
                        ProductName = product?.Name ?? "Unknown",
                        ProductBarcode = product?.Barcode
                    };
                }

                productStats[salesReturn.ProductId].SalesReturnsCount++;
                productStats[salesReturn.ProductId].TotalReturnAmount += salesReturn.Quantity * salesReturn.UnitPrice;
            }

            // Process purchase returns
            foreach (var purchaseReturn in purchaseReturns)
            {
                if (!productStats.ContainsKey(purchaseReturn.ProductId))
                {
                    var product = await _unitOfWork.Products.GetByIdAsync(purchaseReturn.ProductId);
                    productStats[purchaseReturn.ProductId] = new TopReturnedProductDto
                    {
                        ProductId = purchaseReturn.ProductId,
                        ProductName = product?.Name ?? "Unknown",
                        ProductBarcode = product?.Barcode
                    };
                }

                productStats[purchaseReturn.ProductId].PurchaseReturnsCount++;
                productStats[purchaseReturn.ProductId].TotalReturnAmount += purchaseReturn.Quantity * purchaseReturn.UnitCost;
            }

            // Calculate totals and find top reason
            foreach (var stat in productStats.Values)
            {
                stat.TotalReturnsCount = stat.SalesReturnsCount + stat.PurchaseReturnsCount;

                // Find most common return reason for this product
                var productSalesReturns = salesReturns.Where(r => r.ProductId == stat.ProductId);
                var productPurchaseReturns = purchaseReturns.Where(r => r.ProductId == stat.ProductId);

                var allReasons = productSalesReturns.Select(r => r.Reason)
                    .Concat(productPurchaseReturns.Select(r => r.Reason))
                    .Where(r => !string.IsNullOrEmpty(r))
                    .GroupBy(r => r)
                    .OrderByDescending(g => g.Count())
                    .FirstOrDefault();

                stat.TopReturnReason = allReasons?.Key ?? "No reason specified";
            }

            return productStats.Values
                .OrderByDescending(p => p.TotalReturnAmount)
                .Take(count);
        }

        public async Task<IEnumerable<ReturnReasonAnalysisDto>> GetReturnReasonAnalysisAsync()
        {
            var salesReturns = await _unitOfWork.SalesReturns.GetAllAsync();
            var purchaseReturns = await _unitOfWork.PurchaseReturns.GetAllAsync();

            var reasonStats = new Dictionary<string, ReturnReasonAnalysisDto>();

            // Process sales returns
            foreach (var salesReturn in salesReturns)
            {
                var reason = string.IsNullOrEmpty(salesReturn.Reason) ? "No reason specified" : salesReturn.Reason;

                if (!reasonStats.ContainsKey(reason))
                {
                    reasonStats[reason] = new ReturnReasonAnalysisDto { Reason = reason };
                }

                reasonStats[reason].SalesReturnsCount++;
            }

            // Process purchase returns
            foreach (var purchaseReturn in purchaseReturns)
            {
                var reason = string.IsNullOrEmpty(purchaseReturn.Reason) ? "No reason specified" : purchaseReturn.Reason;

                if (!reasonStats.ContainsKey(reason))
                {
                    reasonStats[reason] = new ReturnReasonAnalysisDto { Reason = reason };
                }

                reasonStats[reason].PurchaseReturnsCount++;
            }

            var totalReturns = salesReturns.Count() + purchaseReturns.Count();

            // Calculate totals and percentages
            foreach (var stat in reasonStats.Values)
            {
                stat.TotalCount = stat.SalesReturnsCount + stat.PurchaseReturnsCount;
                stat.Percentage = totalReturns > 0 ? (decimal)stat.TotalCount / totalReturns * 100 : 0;
            }

            return reasonStats.Values.OrderByDescending(r => r.TotalCount);
        }

        public async Task<decimal> GetReturnRateAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var salesReturns = await _unitOfWork.SalesReturns.GetAllAsync();
            var purchaseReturns = await _unitOfWork.PurchaseReturns.GetAllAsync();
            var salesInvoices = await _unitOfWork.SalesInvoices.GetAllAsync();
            var purchaseInvoices = await _unitOfWork.PurchaseInvoices.GetAllAsync();

            if (fromDate.HasValue)
            {
                salesReturns = salesReturns.Where(r => r.CreatedAt.Date >= fromDate.Value.Date);
                purchaseReturns = purchaseReturns.Where(r => r.CreatedAt.Date >= fromDate.Value.Date);
                salesInvoices = salesInvoices.Where(i => i.InvoiceDate.Date >= fromDate.Value.Date);
                purchaseInvoices = purchaseInvoices.Where(i => i.InvoiceDate.Date >= fromDate.Value.Date);
            }

            if (toDate.HasValue)
            {
                salesReturns = salesReturns.Where(r => r.CreatedAt.Date <= toDate.Value.Date);
                purchaseReturns = purchaseReturns.Where(r => r.CreatedAt.Date <= toDate.Value.Date);
                salesInvoices = salesInvoices.Where(i => i.InvoiceDate.Date <= toDate.Value.Date);
                purchaseInvoices = purchaseInvoices.Where(i => i.InvoiceDate.Date <= toDate.Value.Date);
            }

            var totalInvoices = salesInvoices.Count() + purchaseInvoices.Count();
            var totalReturns = salesReturns.Count() + purchaseReturns.Count();

            return totalInvoices > 0 ? (decimal)totalReturns / totalInvoices * 100 : 0;
        }

        public async Task<object> GetReturnTrendsAsync(int months = 12)
        {
            var endDate = DateTime.Today;
            var startDate = endDate.AddMonths(-months);

            var salesReturns = await _unitOfWork.SalesReturns.FindAsync(r => r.CreatedAt.Date >= startDate);
            var purchaseReturns = await _unitOfWork.PurchaseReturns.FindAsync(r => r.CreatedAt.Date >= startDate);

            var trends = new List<object>();

            for (int i = 0; i < months; i++)
            {
                var monthStart = startDate.AddMonths(i);
                var monthEnd = monthStart.AddMonths(1).AddDays(-1);

                var monthSalesReturns = salesReturns.Where(r => r.CreatedAt.Date >= monthStart && r.CreatedAt.Date <= monthEnd);
                var monthPurchaseReturns = purchaseReturns.Where(r => r.CreatedAt.Date >= monthStart && r.CreatedAt.Date <= monthEnd);

                trends.Add(new
                {
                    Month = monthStart.ToString("yyyy-MM"),
                    SalesReturnsCount = monthSalesReturns.Count(),
                    PurchaseReturnsCount = monthPurchaseReturns.Count(),
                    SalesReturnAmount = monthSalesReturns.Sum(r => r.Quantity * r.UnitPrice),
                    PurchaseReturnAmount = monthPurchaseReturns.Sum(r => r.Quantity * r.UnitCost),
                    TotalReturns = monthSalesReturns.Count() + monthPurchaseReturns.Count()
                });
            }

            return trends;
        }

        #endregion

        #region Validation and Invoice Operations

        public async Task<bool> CanReturnSalesItemAsync(int salesInvoiceId, int productId, int quantity)
        {
            var salesDetail = await _unitOfWork.SalesInvoiceDetails.FirstOrDefaultAsync(
                d => d.SalesInvoiceId == salesInvoiceId && d.ProductId == productId);

            if (salesDetail == null)
                return false;

            var existingReturns = await _unitOfWork.SalesReturns.FindAsync(
                r => r.SalesInvoiceId == salesInvoiceId && r.ProductId == productId);
            var totalReturned = existingReturns.Sum(r => r.Quantity);
            var maxReturnable = salesDetail.Quantity - totalReturned;

            return quantity <= maxReturnable && quantity > 0;
        }

        public async Task<bool> CanReturnPurchaseItemAsync(int purchaseInvoiceId, int productId, int quantity)
        {
            var purchaseDetail = await _unitOfWork.PurchaseInvoiceDetails.FirstOrDefaultAsync(
                d => d.PurchaseInvoiceId == purchaseInvoiceId && d.ProductId == productId);

            if (purchaseDetail == null)
                return false;

            var existingReturns = await _unitOfWork.PurchaseReturns.FindAsync(
                r => r.PurchaseInvoiceId == purchaseInvoiceId && r.ProductId == productId);
            var totalReturned = existingReturns.Sum(r => r.Quantity);
            var maxReturnable = purchaseDetail.Quantity - totalReturned;

            return quantity <= maxReturnable && quantity > 0;
        }

        public async Task<int> GetMaxReturnableQuantityAsync(int invoiceId, int productId, string returnType)
        {
            if (returnType.Equals("sales", StringComparison.OrdinalIgnoreCase))
            {
                var salesDetail = await _unitOfWork.SalesInvoiceDetails.FirstOrDefaultAsync(
                    d => d.SalesInvoiceId == invoiceId && d.ProductId == productId);

                if (salesDetail == null)
                    return 0;

                var existingReturns = await _unitOfWork.SalesReturns.FindAsync(
                    r => r.SalesInvoiceId == invoiceId && r.ProductId == productId);
                var totalReturned = existingReturns.Sum(r => r.Quantity);

                return salesDetail.Quantity - totalReturned;
            }
            else if (returnType.Equals("purchase", StringComparison.OrdinalIgnoreCase))
            {
                var purchaseDetail = await _unitOfWork.PurchaseInvoiceDetails.FirstOrDefaultAsync(
                    d => d.PurchaseInvoiceId == invoiceId && d.ProductId == productId);

                if (purchaseDetail == null)
                    return 0;

                var existingReturns = await _unitOfWork.PurchaseReturns.FindAsync(
                    r => r.PurchaseInvoiceId == invoiceId && r.ProductId == productId);
                var totalReturned = existingReturns.Sum(r => r.Quantity);

                return purchaseDetail.Quantity - totalReturned;
            }

            return 0;
        }

        public async Task<(IEnumerable<SalesReturnDto> Returns, int TotalCount)> GetPagedSalesReturnsAsync(
            int pageNumber, int pageSize, ReturnSearchDto? searchDto = null)
        {
            var salesReturns = await _unitOfWork.SalesReturns.GetAllAsync();

            // Apply search filters if provided
            if (searchDto != null)
            {
                if (searchDto.InvoiceId.HasValue)
                {
                    salesReturns = salesReturns.Where(r => r.SalesInvoiceId == searchDto.InvoiceId.Value);
                }

                if (searchDto.ProductId.HasValue)
                {
                    salesReturns = salesReturns.Where(r => r.ProductId == searchDto.ProductId.Value);
                }

                if (searchDto.FromDate.HasValue)
                {
                    salesReturns = salesReturns.Where(r => r.CreatedAt.Date >= searchDto.FromDate.Value.Date);
                }

                if (searchDto.ToDate.HasValue)
                {
                    salesReturns = salesReturns.Where(r => r.CreatedAt.Date <= searchDto.ToDate.Value.Date);
                }

                if (!string.IsNullOrEmpty(searchDto.Reason))
                {
                    salesReturns = salesReturns.Where(r =>
                        !string.IsNullOrEmpty(r.Reason) &&
                        r.Reason.Contains(searchDto.Reason, StringComparison.OrdinalIgnoreCase));
                }

                if (searchDto.UserId.HasValue)
                {
                    salesReturns = salesReturns.Where(r => r.UserId == searchDto.UserId.Value);
                }
            }

            var totalCount = salesReturns.Count();
            var pagedReturns = salesReturns
                .OrderByDescending(r => r.CreatedAt)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize);

            var returnDtos = new List<SalesReturnDto>();
            foreach (var salesReturn in pagedReturns)
            {
                returnDtos.Add(await MapToSalesReturnDto(salesReturn));
            }

            return (returnDtos, totalCount);
        }

        public async Task<(IEnumerable<PurchaseReturnDto> Returns, int TotalCount)> GetPagedPurchaseReturnsAsync(
            int pageNumber, int pageSize, ReturnSearchDto? searchDto = null)
        {
            var purchaseReturns = await _unitOfWork.PurchaseReturns.GetAllAsync();

            // Apply search filters if provided
            if (searchDto != null)
            {
                if (searchDto.InvoiceId.HasValue)
                {
                    purchaseReturns = purchaseReturns.Where(r => r.PurchaseInvoiceId == searchDto.InvoiceId.Value);
                }

                if (searchDto.ProductId.HasValue)
                {
                    purchaseReturns = purchaseReturns.Where(r => r.ProductId == searchDto.ProductId.Value);
                }

                if (searchDto.FromDate.HasValue)
                {
                    purchaseReturns = purchaseReturns.Where(r => r.CreatedAt.Date >= searchDto.FromDate.Value.Date);
                }

                if (searchDto.ToDate.HasValue)
                {
                    purchaseReturns = purchaseReturns.Where(r => r.CreatedAt.Date <= searchDto.ToDate.Value.Date);
                }

                if (!string.IsNullOrEmpty(searchDto.Reason))
                {
                    purchaseReturns = purchaseReturns.Where(r =>
                        !string.IsNullOrEmpty(r.Reason) &&
                        r.Reason.Contains(searchDto.Reason, StringComparison.OrdinalIgnoreCase));
                }

                if (searchDto.UserId.HasValue)
                {
                    purchaseReturns = purchaseReturns.Where(r => r.UserId == searchDto.UserId.Value);
                }
            }

            var totalCount = purchaseReturns.Count();
            var pagedReturns = purchaseReturns
                .OrderByDescending(r => r.CreatedAt)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize);

            var returnDtos = new List<PurchaseReturnDto>();
            foreach (var purchaseReturn in pagedReturns)
            {
                returnDtos.Add(await MapToPurchaseReturnDto(purchaseReturn));
            }

            return (returnDtos, totalCount);
        }

        #endregion

        #region Invoice Operations

        public async Task<object> GetInvoiceReturnSummaryAsync(int invoiceId, string returnType)
        {
            if (returnType.Equals("sales", StringComparison.OrdinalIgnoreCase))
            {
                var salesReturns = await _unitOfWork.SalesReturns.FindAsync(r => r.SalesInvoiceId == invoiceId);
                return new
                {
                    InvoiceId = invoiceId,
                    ReturnType = "Sales",
                    TotalReturns = salesReturns.Count(),
                    TotalReturnedItems = salesReturns.Sum(r => r.Quantity),
                    TotalReturnAmount = salesReturns.Sum(r => r.Quantity * r.UnitPrice),
                    Returns = await GetSalesReturnsByInvoiceAsync(invoiceId)
                };
            }
            else if (returnType.Equals("purchase", StringComparison.OrdinalIgnoreCase))
            {
                var purchaseReturns = await _unitOfWork.PurchaseReturns.FindAsync(r => r.PurchaseInvoiceId == invoiceId);
                return new
                {
                    InvoiceId = invoiceId,
                    ReturnType = "Purchase",
                    TotalReturns = purchaseReturns.Count(),
                    TotalReturnedItems = purchaseReturns.Sum(r => r.Quantity),
                    TotalReturnAmount = purchaseReturns.Sum(r => r.Quantity * r.UnitCost),
                    Returns = await GetPurchaseReturnsByInvoiceAsync(invoiceId)
                };
            }

            return new { InvoiceId = invoiceId, ReturnType = returnType, Error = "Invalid return type" };
        }

        public async Task<bool> HasReturnsAsync(int invoiceId, string returnType)
        {
            if (returnType.Equals("sales", StringComparison.OrdinalIgnoreCase))
            {
                return await _unitOfWork.SalesReturns.AnyAsync(r => r.SalesInvoiceId == invoiceId);
            }
            else if (returnType.Equals("purchase", StringComparison.OrdinalIgnoreCase))
            {
                return await _unitOfWork.PurchaseReturns.AnyAsync(r => r.PurchaseInvoiceId == invoiceId);
            }

            return false;
        }

        public async Task<decimal> GetInvoiceReturnAmountAsync(int invoiceId, string returnType)
        {
            if (returnType.Equals("sales", StringComparison.OrdinalIgnoreCase))
            {
                var salesReturns = await _unitOfWork.SalesReturns.FindAsync(r => r.SalesInvoiceId == invoiceId);
                return salesReturns.Sum(r => r.Quantity * r.UnitPrice);
            }
            else if (returnType.Equals("purchase", StringComparison.OrdinalIgnoreCase))
            {
                var purchaseReturns = await _unitOfWork.PurchaseReturns.FindAsync(r => r.PurchaseInvoiceId == invoiceId);
                return purchaseReturns.Sum(r => r.Quantity * r.UnitCost);
            }

            return 0;
        }

        #endregion

        #region Helper Methods

        private async Task<SalesReturnDto> MapToSalesReturnDto(SalesReturn salesReturn)
        {
            var salesInvoice = await _unitOfWork.SalesInvoices.GetByIdAsync(salesReturn.SalesInvoiceId);
            var product = await _unitOfWork.Products.GetByIdAsync(salesReturn.ProductId);
            var user = await _unitOfWork.Users.GetByIdAsync(salesReturn.UserId);

            return new SalesReturnDto
            {
                Id = salesReturn.Id,
                SalesInvoiceId = salesReturn.SalesInvoiceId,
                SalesInvoiceNumber = salesInvoice?.InvoiceNumber ?? "Unknown",
                ProductId = salesReturn.ProductId,
                ProductName = product?.Name ?? "Unknown",
                ProductBarcode = product?.Barcode,
                Quantity = salesReturn.Quantity,
                UnitPrice = salesReturn.UnitPrice,
                TotalAmount = salesReturn.Quantity * salesReturn.UnitPrice,
                Reason = salesReturn.Reason,
                UserId = salesReturn.UserId,
                UserName = user?.Username ?? "Unknown",
                CreatedAt = salesReturn.CreatedAt
            };
        }

        private async Task<PurchaseReturnDto> MapToPurchaseReturnDto(PurchaseReturn purchaseReturn)
        {
            var purchaseInvoice = await _unitOfWork.PurchaseInvoices.GetByIdAsync(purchaseReturn.PurchaseInvoiceId);
            var product = await _unitOfWork.Products.GetByIdAsync(purchaseReturn.ProductId);
            var user = await _unitOfWork.Users.GetByIdAsync(purchaseReturn.UserId);

            return new PurchaseReturnDto
            {
                Id = purchaseReturn.Id,
                PurchaseInvoiceId = purchaseReturn.PurchaseInvoiceId,
                PurchaseInvoiceNumber = purchaseInvoice?.InvoiceNumber ?? "Unknown",
                ProductId = purchaseReturn.ProductId,
                ProductName = product?.Name ?? "Unknown",
                ProductBarcode = product?.Barcode,
                Quantity = purchaseReturn.Quantity,
                UnitCost = purchaseReturn.UnitCost,
                TotalAmount = purchaseReturn.Quantity * purchaseReturn.UnitCost,
                Reason = purchaseReturn.Reason,
                UserId = purchaseReturn.UserId,
                UserName = user?.Username ?? "Unknown",
                CreatedAt = purchaseReturn.CreatedAt
            };
        }

        #endregion
    }
}
