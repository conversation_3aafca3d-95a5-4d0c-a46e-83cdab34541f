using ElectronicsStore.Application.Interfaces;
using ElectronicsStore.Application.Common;
using Microsoft.EntityFrameworkCore;

namespace ElectronicsStore.Application.Services
{
    /// <summary>
    /// Base service class containing common patterns used across all services
    /// </summary>
    public abstract class BaseService
    {
        protected readonly IUnitOfWork _unitOfWork;

        protected BaseService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        #region Common Validation Patterns

        /// <summary>
        /// Validates that an entity exists by ID
        /// </summary>
        protected async Task ValidateEntityExistsAsync<T>(int id, string entityName) where T : class
        {
            var repository = GetRepository<T>();
            var entity = await repository.GetByIdAsync(id);
            if (entity == null)
                throw new InvalidOperationException($"{entityName} with ID {id} not found.");
        }

        /// <summary>
        /// Validates that a name is unique (for entities with Name property)
        /// </summary>
        protected async Task ValidateNameUniquenessAsync<T>(string name, string entityName, int? excludeId = null) where T : class
        {
            var repository = GetRepository<T>();
            var existingEntity = await repository.FirstOrDefaultAsync(e => 
                EF.Property<string>(e, "Name") == name && 
                (excludeId == null || EF.Property<int>(e, "Id") != excludeId));

            if (existingEntity != null)
                throw new InvalidOperationException($"{entityName} with name '{name}' already exists.");
        }

        /// <summary>
        /// Validates that an entity can be deleted (no related entities)
        /// </summary>
        protected async Task<bool> CanDeleteEntityAsync<T>(int id, Func<int, Task<bool>> hasRelatedEntitiesCheck) where T : class
        {
            var repository = GetRepository<T>();
            var entity = await repository.GetByIdAsync(id);
            if (entity == null)
                return false;

            var hasRelatedEntities = await hasRelatedEntitiesCheck(id);
            return !hasRelatedEntities;
        }

        #endregion

        #region Common CRUD Patterns

        /// <summary>
        /// Generic get by ID pattern
        /// </summary>
        protected async Task<T?> GetEntityByIdAsync<T>(int id) where T : class
        {
            var repository = GetRepository<T>();
            return await repository.GetByIdAsync(id);
        }

        /// <summary>
        /// Generic get all pattern
        /// </summary>
        protected async Task<IEnumerable<T>> GetAllEntitiesAsync<T>() where T : class
        {
            var repository = GetRepository<T>();
            return await repository.GetAllAsync();
        }

        /// <summary>
        /// Generic create pattern
        /// </summary>
        protected async Task<T> CreateEntityAsync<T>(T entity) where T : class
        {
            var repository = GetRepository<T>();
            await repository.AddAsync(entity);
            await _unitOfWork.SaveChangesAsync();
            return entity;
        }

        /// <summary>
        /// Generic update pattern
        /// </summary>
        protected async Task<T> UpdateEntityAsync<T>(T entity) where T : class
        {
            var repository = GetRepository<T>();
            repository.Update(entity);
            await _unitOfWork.SaveChangesAsync();
            return entity;
        }

        /// <summary>
        /// Generic delete pattern
        /// </summary>
        protected async Task<bool> DeleteEntityAsync<T>(int id) where T : class
        {
            var repository = GetRepository<T>();
            var entity = await repository.GetByIdAsync(id);
            if (entity == null)
                return false;

            repository.Remove(entity);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        #endregion

        #region Common Search Patterns

        /// <summary>
        /// Generic search by name pattern
        /// </summary>
        protected async Task<IEnumerable<T>> SearchEntitiesByNameAsync<T>(string searchTerm) where T : class
        {
            var repository = GetRepository<T>();
            return await repository.FindAsync(e => EF.Property<string>(e, "Name").Contains(searchTerm));
        }

        /// <summary>
        /// Generic pagination pattern
        /// </summary>
        protected async Task<(IEnumerable<T> Items, int TotalCount)> GetPagedEntitiesAsync<T>(
            int pageNumber, int pageSize, Func<IEnumerable<T>, IEnumerable<T>>? filter = null) where T : class
        {
            var repository = GetRepository<T>();
            var allEntities = await repository.GetAllAsync();
            
            if (filter != null)
                allEntities = filter(allEntities);

            var totalCount = allEntities.Count();
            var pagedEntities = allEntities
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize);

            return (pagedEntities, totalCount);
        }

        #endregion

        #region Common Business Logic Patterns

        /// <summary>
        /// Execute operation with transaction
        /// </summary>
        protected async Task<T> ExecuteWithTransactionAsync<T>(Func<Task<T>> operation)
        {
            if (_unitOfWork.HasActiveTransaction)
            {
                return await operation();
            }

            await _unitOfWork.BeginTransactionAsync();
            try
            {
                var result = await operation();
                await _unitOfWork.CommitTransactionAsync();
                return result;
            }
            catch
            {
                await _unitOfWork.RollbackTransactionAsync();
                throw;
            }
        }

        /// <summary>
        /// Execute operation with transaction (void return)
        /// </summary>
        protected async Task ExecuteWithTransactionAsync(Func<Task> operation)
        {
            if (_unitOfWork.HasActiveTransaction)
            {
                await operation();
                return;
            }

            await _unitOfWork.BeginTransactionAsync();
            try
            {
                await operation();
                await _unitOfWork.CommitTransactionAsync();
            }
            catch
            {
                await _unitOfWork.RollbackTransactionAsync();
                throw;
            }
        }

        /// <summary>
        /// Check if entity exists
        /// </summary>
        protected async Task<bool> EntityExistsAsync<T>(int id) where T : class
        {
            var repository = GetRepository<T>();
            var entity = await repository.GetByIdAsync(id);
            return entity != null;
        }

        /// <summary>
        /// Get entity count
        /// </summary>
        protected async Task<int> GetEntityCountAsync<T>() where T : class
        {
            var repository = GetRepository<T>();
            return await repository.CountAsync();
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Get repository for specific entity type
        /// </summary>
        private IGenericRepository<T> GetRepository<T>() where T : class
        {
            var propertyName = typeof(T).Name + "s"; // Categories, Suppliers, etc.
            var property = typeof(IUnitOfWork).GetProperty(propertyName);
            
            if (property == null)
                throw new InvalidOperationException($"Repository for {typeof(T).Name} not found in UnitOfWork.");

            return (IGenericRepository<T>)property.GetValue(_unitOfWork)!;
        }

        /// <summary>
        /// Validate input parameters
        /// </summary>
        protected void ValidateInput(object input, string parameterName)
        {
            if (input == null)
                throw new ArgumentNullException(parameterName);
        }

        /// <summary>
        /// Validate ID parameter
        /// </summary>
        protected void ValidateId(int id, string parameterName = "id")
        {
            if (id <= 0)
                throw new ArgumentException("ID must be greater than zero.", parameterName);
        }

        /// <summary>
        /// Validate pagination parameters
        /// </summary>
        protected void ValidatePaginationParameters(int pageNumber, int pageSize)
        {
            if (pageNumber <= 0)
                throw new ArgumentException("Page number must be greater than zero.", nameof(pageNumber));
            
            if (pageSize <= 0 || pageSize > 100)
                throw new ArgumentException("Page size must be between 1 and 100.", nameof(pageSize));
        }

        #endregion
    }
}
