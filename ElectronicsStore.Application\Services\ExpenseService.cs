using ElectronicsStore.Application.DTOs;
using ElectronicsStore.Application.Interfaces;
using ElectronicsStore.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using System.Globalization;
using System.Linq.Expressions;

namespace ElectronicsStore.Application.Services
{
    public class ExpenseService : IExpenseService
    {
        private readonly IUnitOfWork _unitOfWork;

        public ExpenseService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<ExpenseDto>> GetAllExpensesAsync(int pageNumber = 1, int pageSize = 10)
        {
            var expenses = await _unitOfWork.Expenses.GetPagedAsync(pageNumber, pageSize);
            var expenseList = new List<ExpenseDto>();

            foreach (var expense in expenses)
            {
                var user = await _unitOfWork.Users.GetByIdAsync(expense.UserId);
                expenseList.Add(new ExpenseDto
                {
                    Id = expense.Id,
                    ExpenseType = expense.ExpenseType,
                    Amount = expense.Amount,
                    Note = expense.Note,
                    UserId = expense.UserId,
                    UserName = user?.Username ?? "",
                    CreatedAt = expense.CreatedAt
                });
            }

            return expenseList.OrderByDescending(x => x.CreatedAt);
        }

        public async Task<ExpenseDto?> GetExpenseByIdAsync(int id)
        {
            var expense = await _unitOfWork.Expenses.GetByIdAsync(id);
            if (expense == null) return null;

            var user = await _unitOfWork.Users.GetByIdAsync(expense.UserId);
            return new ExpenseDto
            {
                Id = expense.Id,
                ExpenseType = expense.ExpenseType,
                Amount = expense.Amount,
                Note = expense.Note,
                UserId = expense.UserId,
                UserName = user?.Username ?? "",
                CreatedAt = expense.CreatedAt
            };
        }

        public async Task<ExpenseDto> CreateExpenseAsync(CreateExpenseDto createExpenseDto)
        {
            // Validate user exists
            var user = await _unitOfWork.Users.GetByIdAsync(createExpenseDto.UserId);
            if (user == null)
            {
                throw new ArgumentException($"User with ID {createExpenseDto.UserId} not found.");
            }

            var expense = new Expense
            {
                ExpenseType = createExpenseDto.ExpenseType.Trim(),
                Amount = createExpenseDto.Amount,
                Note = createExpenseDto.Note?.Trim(),
                UserId = createExpenseDto.UserId,
                CreatedAt = DateTime.Now
            };

            await _unitOfWork.Expenses.AddAsync(expense);
            await _unitOfWork.SaveChangesAsync();

            return await GetExpenseByIdAsync(expense.Id) ?? throw new InvalidOperationException("Failed to retrieve created expense.");
        }

        public async Task<ExpenseDto> UpdateExpenseAsync(int id, UpdateExpenseDto updateExpenseDto)
        {
            var expense = await _unitOfWork.Expenses.GetByIdAsync(id);
            if (expense == null)
            {
                throw new ArgumentException($"Expense with ID {id} not found.");
            }

            expense.ExpenseType = updateExpenseDto.ExpenseType.Trim();
            expense.Amount = updateExpenseDto.Amount;
            expense.Note = updateExpenseDto.Note?.Trim();

            _unitOfWork.Expenses.Update(expense);
            await _unitOfWork.SaveChangesAsync();

            return await GetExpenseByIdAsync(id) ?? throw new InvalidOperationException("Failed to retrieve updated expense.");
        }

        public async Task<bool> DeleteExpenseAsync(int id)
        {
            var expense = await _unitOfWork.Expenses.GetByIdAsync(id);
            if (expense == null)
            {
                return false;
            }

            _unitOfWork.Expenses.Remove(expense);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<ExpenseDto>> SearchExpensesAsync(ExpenseSearchDto searchDto)
        {
            // Build filter expression
            Expression<Func<Expense, bool>>? filter = null;

            if (!string.IsNullOrWhiteSpace(searchDto.ExpenseType))
            {
                filter = e => e.ExpenseType.Contains(searchDto.ExpenseType);
            }

            if (searchDto.MinAmount.HasValue)
            {
                var minAmount = searchDto.MinAmount.Value;
                if (filter == null)
                    filter = e => e.Amount >= minAmount;
                else
                    filter = e => filter.Compile()(e) && e.Amount >= minAmount;
            }

            if (searchDto.MaxAmount.HasValue)
            {
                var maxAmount = searchDto.MaxAmount.Value;
                if (filter == null)
                    filter = e => e.Amount <= maxAmount;
                else
                    filter = e => filter.Compile()(e) && e.Amount <= maxAmount;
            }

            if (searchDto.FromDate.HasValue)
            {
                var fromDate = searchDto.FromDate.Value;
                if (filter == null)
                    filter = e => e.CreatedAt >= fromDate;
                else
                    filter = e => filter.Compile()(e) && e.CreatedAt >= fromDate;
            }

            if (searchDto.ToDate.HasValue)
            {
                var toDate = searchDto.ToDate.Value.Date.AddDays(1);
                if (filter == null)
                    filter = e => e.CreatedAt < toDate;
                else
                    filter = e => filter.Compile()(e) && e.CreatedAt < toDate;
            }

            if (searchDto.UserId.HasValue)
            {
                var userId = searchDto.UserId.Value;
                if (filter == null)
                    filter = e => e.UserId == userId;
                else
                    filter = e => filter.Compile()(e) && e.UserId == userId;
            }

            if (!string.IsNullOrWhiteSpace(searchDto.Note))
            {
                var note = searchDto.Note;
                if (filter == null)
                    filter = e => e.Note != null && e.Note.Contains(note);
                else
                    filter = e => filter.Compile()(e) && e.Note != null && e.Note.Contains(note);
            }

            var expenses = filter != null
                ? await _unitOfWork.Expenses.FindAsync(filter)
                : await _unitOfWork.Expenses.GetAllAsync();

            var expenseList = new List<ExpenseDto>();
            foreach (var expense in expenses.OrderByDescending(e => e.CreatedAt)
                .Skip((searchDto.PageNumber - 1) * searchDto.PageSize)
                .Take(searchDto.PageSize))
            {
                var user = await _unitOfWork.Users.GetByIdAsync(expense.UserId);
                expenseList.Add(new ExpenseDto
                {
                    Id = expense.Id,
                    ExpenseType = expense.ExpenseType,
                    Amount = expense.Amount,
                    Note = expense.Note,
                    UserId = expense.UserId,
                    UserName = user?.Username ?? "",
                    CreatedAt = expense.CreatedAt
                });
            }

            return expenseList;
        }

        public async Task<IEnumerable<ExpenseDto>> GetExpensesByTypeAsync(string expenseType, int pageNumber = 1, int pageSize = 10)
        {
            var expenses = await _unitOfWork.Expenses.FindAsync(e => e.ExpenseType.Contains(expenseType));
            var expenseList = new List<ExpenseDto>();

            foreach (var expense in expenses.OrderByDescending(e => e.CreatedAt)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize))
            {
                var user = await _unitOfWork.Users.GetByIdAsync(expense.UserId);
                expenseList.Add(new ExpenseDto
                {
                    Id = expense.Id,
                    ExpenseType = expense.ExpenseType,
                    Amount = expense.Amount,
                    Note = expense.Note,
                    UserId = expense.UserId,
                    UserName = user?.Username ?? "",
                    CreatedAt = expense.CreatedAt
                });
            }

            return expenseList;
        }

        public async Task<IEnumerable<ExpenseDto>> GetExpensesByUserAsync(int userId, int pageNumber = 1, int pageSize = 10)
        {
            var expenses = await _unitOfWork.Expenses.FindAsync(e => e.UserId == userId);
            var expenseList = new List<ExpenseDto>();

            foreach (var expense in expenses.OrderByDescending(e => e.CreatedAt)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize))
            {
                var user = await _unitOfWork.Users.GetByIdAsync(expense.UserId);
                expenseList.Add(new ExpenseDto
                {
                    Id = expense.Id,
                    ExpenseType = expense.ExpenseType,
                    Amount = expense.Amount,
                    Note = expense.Note,
                    UserId = expense.UserId,
                    UserName = user?.Username ?? "",
                    CreatedAt = expense.CreatedAt
                });
            }

            return expenseList;
        }

        public async Task<IEnumerable<ExpenseDto>> GetExpensesByDateRangeAsync(DateTime fromDate, DateTime toDate, int pageNumber = 1, int pageSize = 10)
        {
            var toDateEnd = toDate.Date.AddDays(1);
            var expenses = await _unitOfWork.Expenses.FindAsync(e => e.CreatedAt >= fromDate && e.CreatedAt < toDateEnd);
            var expenseList = new List<ExpenseDto>();

            foreach (var expense in expenses.OrderByDescending(e => e.CreatedAt)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize))
            {
                var user = await _unitOfWork.Users.GetByIdAsync(expense.UserId);
                expenseList.Add(new ExpenseDto
                {
                    Id = expense.Id,
                    ExpenseType = expense.ExpenseType,
                    Amount = expense.Amount,
                    Note = expense.Note,
                    UserId = expense.UserId,
                    UserName = user?.Username ?? "",
                    CreatedAt = expense.CreatedAt
                });
            }

            return expenseList;
        }

        public async Task<IEnumerable<ExpenseDto>> GetTodayExpensesAsync()
        {
            var today = DateTime.Today;
            var tomorrow = today.AddDays(1);

            var expenses = await _unitOfWork.Expenses.FindAsync(e => e.CreatedAt >= today && e.CreatedAt < tomorrow);
            var expenseList = new List<ExpenseDto>();

            foreach (var expense in expenses.OrderByDescending(e => e.CreatedAt))
            {
                var user = await _unitOfWork.Users.GetByIdAsync(expense.UserId);
                expenseList.Add(new ExpenseDto
                {
                    Id = expense.Id,
                    ExpenseType = expense.ExpenseType,
                    Amount = expense.Amount,
                    Note = expense.Note,
                    UserId = expense.UserId,
                    UserName = user?.Username ?? "",
                    CreatedAt = expense.CreatedAt
                });
            }

            return expenseList;
        }

        public async Task<decimal> GetTodayTotalExpensesAsync()
        {
            var today = DateTime.Today;
            var tomorrow = today.AddDays(1);

            var expenses = await _unitOfWork.Expenses.FindAsync(e => e.CreatedAt >= today && e.CreatedAt < tomorrow);
            return expenses.Sum(e => e.Amount);
        }

        public async Task<IEnumerable<ExpenseDto>> GetMonthExpensesAsync(int year, int month)
        {
            var startDate = new DateTime(year, month, 1);
            var endDate = startDate.AddMonths(1);

            var expenses = await _unitOfWork.Expenses.FindAsync(e => e.CreatedAt >= startDate && e.CreatedAt < endDate);
            var expenseList = new List<ExpenseDto>();

            foreach (var expense in expenses.OrderByDescending(e => e.CreatedAt))
            {
                var user = await _unitOfWork.Users.GetByIdAsync(expense.UserId);
                expenseList.Add(new ExpenseDto
                {
                    Id = expense.Id,
                    ExpenseType = expense.ExpenseType,
                    Amount = expense.Amount,
                    Note = expense.Note,
                    UserId = expense.UserId,
                    UserName = user?.Username ?? "",
                    CreatedAt = expense.CreatedAt
                });
            }

            return expenseList;
        }

        public async Task<decimal> GetMonthTotalExpensesAsync(int year, int month)
        {
            var startDate = new DateTime(year, month, 1);
            var endDate = startDate.AddMonths(1);

            var expenses = await _unitOfWork.Expenses.FindAsync(e => e.CreatedAt >= startDate && e.CreatedAt < endDate);
            return expenses.Sum(e => e.Amount);
        }

        public async Task<bool> ExpenseExistsAsync(int id)
        {
            var expense = await _unitOfWork.Expenses.GetByIdAsync(id);
            return expense != null;
        }

        public async Task<IEnumerable<string>> GetExpenseTypesAsync()
        {
            var expenses = await _unitOfWork.Expenses.GetAllAsync();
            return expenses.Select(e => e.ExpenseType).Distinct().OrderBy(t => t);
        }

        public async Task<decimal> GetTotalExpensesByTypeAsync(string expenseType, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var expenses = await _unitOfWork.Expenses.FindAsync(e => e.ExpenseType == expenseType);

            if (fromDate.HasValue)
            {
                expenses = expenses.Where(e => e.CreatedAt >= fromDate.Value);
            }

            if (toDate.HasValue)
            {
                var toDateEnd = toDate.Value.Date.AddDays(1);
                expenses = expenses.Where(e => e.CreatedAt < toDateEnd);
            }

            return expenses.Sum(e => e.Amount);
        }

        public async Task<ExpenseStatisticsDto> GetExpenseStatisticsAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var allExpenses = await _unitOfWork.Expenses.GetAllAsync();
            var filteredExpenses = allExpenses.AsEnumerable();

            if (fromDate.HasValue)
            {
                filteredExpenses = filteredExpenses.Where(e => e.CreatedAt >= fromDate.Value);
            }

            if (toDate.HasValue)
            {
                var toDateEnd = toDate.Value.Date.AddDays(1);
                filteredExpenses = filteredExpenses.Where(e => e.CreatedAt < toDateEnd);
            }

            var totalExpenses = filteredExpenses.Count();
            var totalAmount = filteredExpenses.Sum(e => e.Amount);
            var averageAmount = totalExpenses > 0 ? totalAmount / totalExpenses : 0;

            // Today's statistics
            var today = DateTime.Today;
            var tomorrow = today.AddDays(1);
            var todayExpenses = allExpenses.Count(e => e.CreatedAt >= today && e.CreatedAt < tomorrow);
            var todayAmount = allExpenses.Where(e => e.CreatedAt >= today && e.CreatedAt < tomorrow).Sum(e => e.Amount);

            // This month's statistics
            var monthStart = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
            var monthEnd = monthStart.AddMonths(1);
            var monthExpenses = allExpenses.Count(e => e.CreatedAt >= monthStart && e.CreatedAt < monthEnd);
            var monthAmount = allExpenses.Where(e => e.CreatedAt >= monthStart && e.CreatedAt < monthEnd).Sum(e => e.Amount);

            // Expense type breakdown
            var typeBreakdown = await GetExpenseTypeBreakdownAsync(fromDate, toDate);
            var monthlyTrend = await GetMonthlyTrendAsync(12);

            return new ExpenseStatisticsDto
            {
                TotalExpenses = totalExpenses,
                TotalAmount = totalAmount,
                AverageAmount = averageAmount,
                TodayExpenses = todayExpenses,
                TodayAmount = todayAmount,
                MonthExpenses = monthExpenses,
                MonthAmount = monthAmount,
                ExpenseTypeBreakdown = typeBreakdown.ToList(),
                MonthlyTrend = monthlyTrend.ToList()
            };
        }

        public async Task<ExpenseReportDto> GetExpenseReportAsync(DateTime fromDate, DateTime toDate)
        {
            var toDateEnd = toDate.Date.AddDays(1);
            var expenses = await _unitOfWork.Expenses.FindAsync(e => e.CreatedAt >= fromDate && e.CreatedAt < toDateEnd);
            var expenseList = new List<ExpenseDto>();

            foreach (var expense in expenses.OrderByDescending(e => e.CreatedAt))
            {
                var user = await _unitOfWork.Users.GetByIdAsync(expense.UserId);
                expenseList.Add(new ExpenseDto
                {
                    Id = expense.Id,
                    ExpenseType = expense.ExpenseType,
                    Amount = expense.Amount,
                    Note = expense.Note,
                    UserId = expense.UserId,
                    UserName = user?.Username ?? "",
                    CreatedAt = expense.CreatedAt
                });
            }

            var typeBreakdown = await GetExpenseTypeBreakdownAsync(fromDate, toDate);
            var userBreakdown = await GetTopSpendingUsersAsync(100, fromDate, toDate);

            return new ExpenseReportDto
            {
                FromDate = fromDate,
                ToDate = toDate,
                TotalExpenses = expenseList.Count,
                TotalAmount = expenseList.Sum(e => e.Amount),
                Expenses = expenseList,
                TypeBreakdown = typeBreakdown.ToList(),
                UserBreakdown = userBreakdown.ToList()
            };
        }

        public async Task<IEnumerable<ExpenseTypeStatDto>> GetExpenseTypeBreakdownAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var allExpenses = await _unitOfWork.Expenses.GetAllAsync();
            var filteredExpenses = allExpenses.AsEnumerable();

            if (fromDate.HasValue)
            {
                filteredExpenses = filteredExpenses.Where(e => e.CreatedAt >= fromDate.Value);
            }

            if (toDate.HasValue)
            {
                var toDateEnd = toDate.Value.Date.AddDays(1);
                filteredExpenses = filteredExpenses.Where(e => e.CreatedAt < toDateEnd);
            }

            var totalAmount = filteredExpenses.Sum(e => e.Amount);

            var breakdown = filteredExpenses
                .GroupBy(e => e.ExpenseType)
                .Select(g => new ExpenseTypeStatDto
                {
                    ExpenseType = g.Key,
                    Count = g.Count(),
                    TotalAmount = g.Sum(e => e.Amount),
                    AverageAmount = g.Average(e => e.Amount),
                    Percentage = totalAmount > 0 ? (g.Sum(e => e.Amount) / totalAmount) * 100 : 0
                })
                .OrderByDescending(x => x.TotalAmount)
                .ToList();

            return breakdown;
        }

        public async Task<IEnumerable<MonthlyExpenseDto>> GetMonthlyTrendAsync(int months = 12)
        {
            var endDate = DateTime.Now;
            var startDate = endDate.AddMonths(-months);

            var expenses = await _unitOfWork.Expenses.FindAsync(e => e.CreatedAt >= startDate && e.CreatedAt <= endDate);

            var monthlyExpenses = expenses
                .GroupBy(e => new { e.CreatedAt.Year, e.CreatedAt.Month })
                .Select(g => new MonthlyExpenseDto
                {
                    Year = g.Key.Year,
                    Month = g.Key.Month,
                    MonthName = CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(g.Key.Month),
                    Count = g.Count(),
                    TotalAmount = g.Sum(e => e.Amount),
                    AverageAmount = g.Average(e => e.Amount)
                })
                .OrderBy(x => x.Year)
                .ThenBy(x => x.Month)
                .ToList();

            return monthlyExpenses;
        }

        public async Task<IEnumerable<ExpenseTypeStatDto>> GetTopExpenseTypesAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var breakdown = await GetExpenseTypeBreakdownAsync(fromDate, toDate);
            return breakdown.Take(count);
        }

        public async Task<IEnumerable<UserExpenseDto>> GetTopSpendingUsersAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var allExpenses = await _unitOfWork.Expenses.GetAllAsync();
            var filteredExpenses = allExpenses.AsEnumerable();

            if (fromDate.HasValue)
            {
                filteredExpenses = filteredExpenses.Where(e => e.CreatedAt >= fromDate.Value);
            }

            if (toDate.HasValue)
            {
                var toDateEnd = toDate.Value.Date.AddDays(1);
                filteredExpenses = filteredExpenses.Where(e => e.CreatedAt < toDateEnd);
            }

            var userExpensesList = new List<UserExpenseDto>();
            var userGroups = filteredExpenses.GroupBy(e => e.UserId);

            foreach (var group in userGroups)
            {
                var user = await _unitOfWork.Users.GetByIdAsync(group.Key);
                userExpensesList.Add(new UserExpenseDto
                {
                    UserId = group.Key,
                    UserName = user?.Username ?? "",
                    Count = group.Count(),
                    TotalAmount = group.Sum(e => e.Amount),
                    AverageAmount = group.Average(e => e.Amount)
                });
            }

            return userExpensesList
                .OrderByDescending(x => x.TotalAmount)
                .Take(count);
        }
    }
}