using ElectronicsStore.Application.DTOs;

namespace ElectronicsStore.Application.Services
{
    public interface ISalesService
    {
        // Basic CRUD operations
        Task<SalesInvoiceDto?> GetSalesInvoiceByIdAsync(int id);
        Task<SalesInvoiceDto?> GetSalesInvoiceByNumberAsync(string invoiceNumber);
        Task<IEnumerable<SalesInvoiceDto>> GetAllSalesInvoicesAsync();
        Task<SalesInvoiceDto> CreateSalesInvoiceAsync(CreateSalesInvoiceDto createSalesInvoiceDto);
        Task<bool> DeleteSalesInvoiceAsync(int id);

        // Search and filtering
        Task<IEnumerable<SalesInvoiceDto>> SearchSalesInvoicesAsync(SalesSearchDto searchDto);
        Task<IEnumerable<SalesInvoiceDto>> GetSalesInvoicesByCustomerAsync(string customerName);
        Task<IEnumerable<SalesInvoiceDto>> GetSalesInvoicesByUserAsync(int userId);
        Task<IEnumerable<SalesInvoiceDto>> GetSalesInvoicesByDateRangeAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<SalesInvoiceDto>> GetSalesInvoicesByPaymentMethodAsync(string paymentMethod);
        Task<IEnumerable<SalesInvoiceDto>> GetTodaysSalesAsync();

        // Sales analytics
        Task<decimal> GetTotalSalesAmountAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<int> GetTotalSalesCountAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<decimal> GetAverageSaleAmountAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<decimal> GetTotalDiscountsAsync(DateTime? fromDate = null, DateTime? toDate = null);

        // Reports
        Task<IEnumerable<SalesReportDto>> GetDailySalesReportAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<SalesReportDto>> GetMonthlySalesReportAsync(int year);
        Task<IEnumerable<object>> GetSalesByPaymentMethodAsync(DateTime? fromDate = null, DateTime? toDate = null);

        // Top customers and products
        Task<IEnumerable<TopCustomerDto>> GetTopCustomersAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<TopProductDto>> GetTopSellingProductsAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null);

        // Override management
        Task<IEnumerable<SalesInvoiceDto>> GetOverriddenSalesAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<bool> OverrideSaleAsync(int salesInvoiceId, int overrideByUserId, string reason);

        // Returns management
        Task<bool> ProcessReturnAsync(int salesInvoiceId, int productId, int quantity, string reason, int userId);
        Task<IEnumerable<object>> GetSalesReturnsAsync(DateTime? fromDate = null, DateTime? toDate = null);

        // Validation
        Task<bool> SalesInvoiceExistsAsync(int id);
        Task<bool> InvoiceNumberExistsAsync(string invoiceNumber);
        Task<bool> CanDeleteSalesInvoiceAsync(int id);

        // Pagination
        Task<(IEnumerable<SalesInvoiceDto> Sales, int TotalCount)> GetPagedSalesInvoicesAsync(
            int pageNumber, int pageSize, SalesSearchDto? searchDto = null);

        // Statistics
        Task<object> GetSalesStatisticsAsync(DateTime? fromDate = null, DateTime? toDate = null);
    }
}
