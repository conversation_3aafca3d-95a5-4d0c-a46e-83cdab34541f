using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace ElectronicsStore.Application.Common
{
    public static class ValidationHelper
    {
        // Email validation
        public static bool IsValidEmail(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;

            try
            {
                var emailAttribute = new EmailAddressAttribute();
                return emailAttribute.IsValid(email);
            }
            catch
            {
                return false;
            }
        }

        // Phone number validation (Saudi Arabia format)
        public static bool IsValidSaudiPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return false;

            // Remove spaces and special characters
            var cleanPhone = Regex.Replace(phoneNumber, @"[\s\-\(\)]", "");

            // Saudi phone number patterns
            var patterns = new[]
            {
                @"^(\+966|966|0)?5[0-9]{8}$",  // Mobile numbers
                @"^(\+966|966|0)?1[1-9][0-9]{7}$"  // Landline numbers
            };

            return patterns.Any(pattern => Regex.IsMatch(cleanPhone, pattern));
        }

        // Password strength validation
        public static ValidationResult ValidatePassword(string password)
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(password))
            {
                errors.Add("Password is required.");
                return new ValidationResult(false, errors);
            }

            if (password.Length < 8)
                errors.Add("Password must be at least 8 characters long.");

            if (password.Length > 100)
                errors.Add("Password must not exceed 100 characters.");

            if (!Regex.IsMatch(password, @"[A-Z]"))
                errors.Add("Password must contain at least one uppercase letter.");

            if (!Regex.IsMatch(password, @"[a-z]"))
                errors.Add("Password must contain at least one lowercase letter.");

            if (!Regex.IsMatch(password, @"[0-9]"))
                errors.Add("Password must contain at least one digit.");

            if (!Regex.IsMatch(password, @"[!@#$%^&*()_+\-=\[\]{};':""\\|,.<>\/?]"))
                errors.Add("Password must contain at least one special character.");

            return new ValidationResult(errors.Count == 0, errors);
        }

        // Barcode validation
        public static bool IsValidBarcode(string barcode)
        {
            if (string.IsNullOrWhiteSpace(barcode))
                return false;

            // Remove spaces
            barcode = barcode.Replace(" ", "");

            // Check common barcode formats
            return IsValidEAN13(barcode) || IsValidEAN8(barcode) || IsValidUPCA(barcode) || IsValidCode128(barcode);
        }

        private static bool IsValidEAN13(string barcode)
        {
            if (barcode.Length != 13 || !barcode.All(char.IsDigit))
                return false;

            // Calculate checksum
            int sum = 0;
            for (int i = 0; i < 12; i++)
            {
                int digit = int.Parse(barcode[i].ToString());
                sum += (i % 2 == 0) ? digit : digit * 3;
            }

            int checkDigit = (10 - (sum % 10)) % 10;
            return checkDigit == int.Parse(barcode[12].ToString());
        }

        private static bool IsValidEAN8(string barcode)
        {
            if (barcode.Length != 8 || !barcode.All(char.IsDigit))
                return false;

            int sum = 0;
            for (int i = 0; i < 7; i++)
            {
                int digit = int.Parse(barcode[i].ToString());
                sum += (i % 2 == 0) ? digit * 3 : digit;
            }

            int checkDigit = (10 - (sum % 10)) % 10;
            return checkDigit == int.Parse(barcode[7].ToString());
        }

        private static bool IsValidUPCA(string barcode)
        {
            if (barcode.Length != 12 || !barcode.All(char.IsDigit))
                return false;

            int sum = 0;
            for (int i = 0; i < 11; i++)
            {
                int digit = int.Parse(barcode[i].ToString());
                sum += (i % 2 == 0) ? digit * 3 : digit;
            }

            int checkDigit = (10 - (sum % 10)) % 10;
            return checkDigit == int.Parse(barcode[11].ToString());
        }

        private static bool IsValidCode128(string barcode)
        {
            // Basic validation for Code 128 (alphanumeric, length between 1-48)
            return !string.IsNullOrWhiteSpace(barcode) && 
                   barcode.Length >= 1 && 
                   barcode.Length <= 48 &&
                   barcode.All(c => char.IsLetterOrDigit(c) || " !\"#$%&'()*+,-./:;<=>?@[\\]^_`{|}~".Contains(c));
        }

        // Price validation
        public static bool IsValidPrice(decimal price)
        {
            return price >= 0 && price <= 999999.99m;
        }

        // Quantity validation
        public static bool IsValidQuantity(int quantity)
        {
            return quantity >= 0 && quantity <= 999999;
        }

        // Name validation (for products, categories, suppliers, etc.)
        public static ValidationResult ValidateName(string name, int minLength = 2, int maxLength = 100)
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(name))
            {
                errors.Add("Name is required.");
                return new ValidationResult(false, errors);
            }

            name = name.Trim();

            if (name.Length < minLength)
                errors.Add($"Name must be at least {minLength} characters long.");

            if (name.Length > maxLength)
                errors.Add($"Name must not exceed {maxLength} characters.");

            // Check for invalid characters
            if (Regex.IsMatch(name, @"[<>""'&]"))
                errors.Add("Name contains invalid characters.");

            return new ValidationResult(errors.Count == 0, errors);
        }

        // Date validation
        public static bool IsValidDate(DateTime date, bool allowFuture = true, bool allowPast = true)
        {
            var now = DateTime.Now;

            if (!allowFuture && date > now)
                return false;

            if (!allowPast && date < now.Date)
                return false;

            // Check reasonable date range (not too far in past or future)
            var minDate = new DateTime(1900, 1, 1);
            var maxDate = new DateTime(2100, 12, 31);

            return date >= minDate && date <= maxDate;
        }

        // Invoice number validation
        public static bool IsValidInvoiceNumber(string invoiceNumber)
        {
            if (string.IsNullOrWhiteSpace(invoiceNumber))
                return false;

            invoiceNumber = invoiceNumber.Trim();

            // Invoice number should be 3-20 characters, alphanumeric with some special characters
            return invoiceNumber.Length >= 3 && 
                   invoiceNumber.Length <= 20 &&
                   Regex.IsMatch(invoiceNumber, @"^[A-Za-z0-9\-_]+$");
        }
    }

    public class ValidationResult
    {
        public bool IsValid { get; }
        public List<string> Errors { get; }

        public ValidationResult(bool isValid, List<string> errors)
        {
            IsValid = isValid;
            Errors = errors ?? new List<string>();
        }

        public ValidationResult(bool isValid, string error = "")
        {
            IsValid = isValid;
            Errors = string.IsNullOrEmpty(error) ? new List<string>() : new List<string> { error };
        }
    }
}
