using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using ElectronicsStore.Application.Interfaces;
using ElectronicsStore.Domain.Entities;

namespace ElectronicsStore.Persistence.Repositories
{
    public class UnitOfWork : IUnitOfWork
    {
        private readonly ElectronicsDbContext _context;
        private IDbContextTransaction? _transaction;

        // Generic repository instances
        private IGenericRepository<Category>? _categories;
        private IGenericRepository<Supplier>? _suppliers;
        private IGenericRepository<Role>? _roles;
        private IGenericRepository<Permission>? _permissions;
        private IGenericRepository<RolePermission>? _rolePermissions;
        private IGenericRepository<User>? _users;
        private IGenericRepository<PurchaseInvoice>? _purchaseInvoices;
        private IGenericRepository<PurchaseInvoiceDetail>? _purchaseInvoiceDetails;
        private IGenericRepository<SalesInvoiceDetail>? _salesInvoiceDetails;
        private IGenericRepository<SalesReturn>? _salesReturns;
        private IGenericRepository<PurchaseReturn>? _purchaseReturns;
        private IGenericRepository<Expense>? _expenses;
        private IGenericRepository<InventoryView>? _inventoryViews;
        private IGenericRepository<InventoryValuationView>? _inventoryValuationViews;
        private IGenericRepository<CogsView>? _cogsViews;

        // Specific repository instances
        private IProductRepository? _products;
        private ISalesRepository? _sales;
        private IInventoryRepository? _inventory;

        public UnitOfWork(ElectronicsDbContext context)
        {
            _context = context;
        }

        // Generic repository properties with lazy initialization
        public IGenericRepository<Category> Categories =>
            _categories ??= new GenericRepository<Category>(_context);

        public IGenericRepository<Supplier> Suppliers =>
            _suppliers ??= new GenericRepository<Supplier>(_context);

        public IGenericRepository<Role> Roles =>
            _roles ??= new GenericRepository<Role>(_context);

        public IGenericRepository<Permission> Permissions =>
            _permissions ??= new GenericRepository<Permission>(_context);

        public IGenericRepository<RolePermission> RolePermissions =>
            _rolePermissions ??= new GenericRepository<RolePermission>(_context);

        public IGenericRepository<User> Users =>
            _users ??= new GenericRepository<User>(_context);

        public IGenericRepository<PurchaseInvoice> PurchaseInvoices =>
            _purchaseInvoices ??= new GenericRepository<PurchaseInvoice>(_context);

        public IGenericRepository<PurchaseInvoiceDetail> PurchaseInvoiceDetails =>
            _purchaseInvoiceDetails ??= new GenericRepository<PurchaseInvoiceDetail>(_context);

        public IGenericRepository<SalesInvoiceDetail> SalesInvoiceDetails =>
            _salesInvoiceDetails ??= new GenericRepository<SalesInvoiceDetail>(_context);

        // Specific repository properties with lazy initialization
        public IProductRepository Products =>
            _products ??= new ProductRepository(_context);

        public ISalesRepository Sales =>
            _sales ??= new SalesRepository(_context);

        public IInventoryRepository Inventory =>
            _inventory ??= new InventoryRepository(_context);

        public IGenericRepository<SalesReturn> SalesReturns => 
            _salesReturns ??= new GenericRepository<SalesReturn>(_context);

        public IGenericRepository<PurchaseReturn> PurchaseReturns => 
            _purchaseReturns ??= new GenericRepository<PurchaseReturn>(_context);

        public IGenericRepository<Expense> Expenses => 
            _expenses ??= new GenericRepository<Expense>(_context);

        public IGenericRepository<InventoryView> InventoryViews =>
            _inventoryViews ??= new GenericRepository<InventoryView>(_context);

        public IGenericRepository<InventoryValuationView> InventoryValuationViews =>
            _inventoryValuationViews ??= new GenericRepository<InventoryValuationView>(_context);

        public IGenericRepository<CogsView> CogsViews =>
            _cogsViews ??= new GenericRepository<CogsView>(_context);

        // Transaction management properties
        public bool HasActiveTransaction => _transaction != null;

        public async Task<int> SaveChangesAsync()
        {
            return await _context.SaveChangesAsync();
        }

        public async Task BeginTransactionAsync()
        {
            if (_transaction != null)
                throw new InvalidOperationException("A transaction is already active.");

            _transaction = await _context.Database.BeginTransactionAsync();
        }

        public async Task CommitTransactionAsync()
        {
            if (_transaction == null)
                throw new InvalidOperationException("No active transaction to commit.");

            try
            {
                await _transaction.CommitAsync();
            }
            finally
            {
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }

        public async Task RollbackTransactionAsync()
        {
            if (_transaction == null)
                throw new InvalidOperationException("No active transaction to rollback.");

            try
            {
                await _transaction.RollbackAsync();
            }
            finally
            {
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }

        // Bulk operations
        public async Task<int> ExecuteSqlRawAsync(string sql, params object[] parameters)
        {
            return await _context.Database.ExecuteSqlRawAsync(sql, parameters);
        }

        public async Task<IEnumerable<T>> SqlQueryAsync<T>(string sql, params object[] parameters) where T : class
        {
            return await _context.Database.SqlQueryRaw<T>(sql, parameters).ToListAsync();
        }

        public void Dispose()
        {
            if (_transaction != null)
            {
                _transaction.Dispose();
                _transaction = null;
            }
            _context.Dispose();
        }
    }
}
