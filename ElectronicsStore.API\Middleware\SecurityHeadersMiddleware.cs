namespace ElectronicsStore.API.Middleware
{
    /// <summary>
    /// Middleware to add security headers to HTTP responses
    /// </summary>
    public class SecurityHeadersMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<SecurityHeadersMiddleware> _logger;

        public SecurityHeadersMiddleware(RequestDelegate next, ILogger<SecurityHeadersMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // Add security headers before processing the request
            AddSecurityHeaders(context);

            await _next(context);

            // Log security-related information
            LogSecurityInfo(context);
        }

        private void AddSecurityHeaders(HttpContext context)
        {
            var headers = context.Response.Headers;

            // X-Content-Type-Options: Prevents MIME type sniffing
            headers.TryAdd("X-Content-Type-Options", "nosniff");

            // X-Frame-Options: Prevents clickjacking attacks
            headers.TryAdd("X-Frame-Options", "DENY");

            // X-XSS-Protection: Enables XSS filtering
            headers.TryAdd("X-XSS-Protection", "1; mode=block");

            // Referrer-Policy: Controls referrer information
            headers.TryAdd("Referrer-Policy", "strict-origin-when-cross-origin");

            // Content-Security-Policy: Prevents XSS and data injection attacks
            var csp = "default-src 'self'; " +
                     "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
                     "style-src 'self' 'unsafe-inline'; " +
                     "img-src 'self' data: https:; " +
                     "font-src 'self'; " +
                     "connect-src 'self'; " +
                     "frame-ancestors 'none'; " +
                     "base-uri 'self'; " +
                     "form-action 'self'";
            headers.TryAdd("Content-Security-Policy", csp);

            // Strict-Transport-Security: Enforces HTTPS
            if (context.Request.IsHttps)
            {
                headers.TryAdd("Strict-Transport-Security", "max-age=31536000; includeSubDomains; preload");
            }

            // Permissions-Policy: Controls browser features
            var permissionsPolicy = "camera=(), " +
                                   "microphone=(), " +
                                   "geolocation=(), " +
                                   "payment=(), " +
                                   "usb=(), " +
                                   "magnetometer=(), " +
                                   "gyroscope=(), " +
                                   "accelerometer=()";
            headers.TryAdd("Permissions-Policy", permissionsPolicy);

            // X-Permitted-Cross-Domain-Policies: Controls cross-domain policies
            headers.TryAdd("X-Permitted-Cross-Domain-Policies", "none");

            // Cache-Control: Controls caching behavior for sensitive endpoints
            if (IsSensitiveEndpoint(context.Request.Path))
            {
                headers.TryAdd("Cache-Control", "no-store, no-cache, must-revalidate, private");
                headers.TryAdd("Pragma", "no-cache");
                headers.TryAdd("Expires", "0");
            }

            // Remove server information headers
            headers.Remove("Server");
            headers.Remove("X-Powered-By");
            headers.Remove("X-AspNet-Version");
            headers.Remove("X-AspNetMvc-Version");
        }

        private bool IsSensitiveEndpoint(PathString path)
        {
            var sensitiveEndpoints = new[]
            {
                "/api/auth",
                "/api/users",
                "/api/performance",
                "/api/admin"
            };

            return sensitiveEndpoints.Any(endpoint => 
                path.StartsWithSegments(endpoint, StringComparison.OrdinalIgnoreCase));
        }

        private void LogSecurityInfo(HttpContext context)
        {
            try
            {
                var request = context.Request;
                var response = context.Response;

                // Log suspicious requests
                if (IsSuspiciousRequest(request))
                {
                    _logger.LogWarning("Suspicious request detected: {Method} {Path} from {IP} with UserAgent: {UserAgent}",
                        request.Method,
                        request.Path,
                        context.Connection.RemoteIpAddress?.ToString(),
                        request.Headers.UserAgent.ToString());
                }

                // Log security header violations
                if (response.StatusCode == 400 || response.StatusCode == 403)
                {
                    _logger.LogWarning("Security-related response: {StatusCode} for {Method} {Path} from {IP}",
                        response.StatusCode,
                        request.Method,
                        request.Path,
                        context.Connection.RemoteIpAddress?.ToString());
                }

                // Log authentication failures
                if (response.StatusCode == 401)
                {
                    _logger.LogWarning("Authentication failure: {Method} {Path} from {IP}",
                        request.Method,
                        request.Path,
                        context.Connection.RemoteIpAddress?.ToString());
                }

                // Log authorization failures
                if (response.StatusCode == 403)
                {
                    var userId = context.User?.FindFirst("UserId")?.Value ?? "Unknown";
                    _logger.LogWarning("Authorization failure: User {UserId} attempted {Method} {Path}",
                        userId,
                        request.Method,
                        request.Path);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error logging security information");
            }
        }

        private bool IsSuspiciousRequest(HttpRequest request)
        {
            var userAgent = request.Headers.UserAgent.ToString().ToLower();
            var path = request.Path.ToString().ToLower();
            var queryString = request.QueryString.ToString().ToLower();

            // Check for common attack patterns
            var suspiciousPatterns = new[]
            {
                "script",
                "javascript:",
                "vbscript:",
                "<script",
                "</script>",
                "onload=",
                "onerror=",
                "onclick=",
                "union select",
                "drop table",
                "insert into",
                "delete from",
                "update set",
                "../",
                "..\\",
                "cmd.exe",
                "powershell",
                "/etc/passwd",
                "/proc/",
                "wp-admin",
                "phpmyadmin",
                ".php",
                ".asp",
                ".jsp"
            };

            // Check user agent for suspicious patterns
            var suspiciousUserAgents = new[]
            {
                "sqlmap",
                "nikto",
                "nmap",
                "masscan",
                "burp",
                "owasp",
                "scanner",
                "bot",
                "crawler"
            };

            return suspiciousPatterns.Any(pattern => 
                       path.Contains(pattern) || queryString.Contains(pattern)) ||
                   suspiciousUserAgents.Any(agent => userAgent.Contains(agent)) ||
                   string.IsNullOrEmpty(userAgent) ||
                   request.Headers.Count > 50; // Too many headers might indicate an attack
        }
    }

    /// <summary>
    /// Extension methods for registering security headers middleware
    /// </summary>
    public static class SecurityHeadersExtensions
    {
        public static IApplicationBuilder UseSecurityHeaders(this IApplicationBuilder app)
        {
            return app.UseMiddleware<SecurityHeadersMiddleware>();
        }
    }
}
