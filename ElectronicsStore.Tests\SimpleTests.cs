using Xunit;
using FluentAssertions;
using ElectronicsStore.Application.DTOs;
using ElectronicsStore.Domain.Entities;

namespace ElectronicsStore.Tests
{
    /// <summary>
    /// Simple tests to verify basic functionality works
    /// </summary>
    public class SimpleTests
    {
        [Fact]
        public void CreateCategoryDto_ShouldWork()
        {
            // Arrange & Act
            var dto = new CreateCategoryDto { Name = "Electronics" };

            // Assert
            dto.Name.Should().Be("Electronics");
        }

        [Fact]
        public void Category_ShouldWork()
        {
            // Arrange & Act
            var category = new Category { Id = 1, Name = "Electronics" };

            // Assert
            category.Id.Should().Be(1);
            category.Name.Should().Be("Electronics");
        }

        [Fact]
        public void Product_ShouldWork()
        {
            // Arrange & Act
            var product = new Product
            {
                Id = 1,
                Name = "Laptop",
                CategoryId = 1,
                DefaultCostPrice = 800.00m,
                DefaultSellingPrice = 1000.00m,
                MinSellingPrice = 900.00m
            };

            // Assert
            product.Id.Should().Be(1);
            product.Name.Should().Be("Laptop");
            product.CategoryId.Should().Be(1);
            product.DefaultSellingPrice.Should().Be(1000.00m);
        }

        [Fact]
        public void User_ShouldWork()
        {
            // Arrange & Act
            var user = new User
            {
                Id = 1,
                Username = "testuser",
                Password = "hashedpassword",
                RoleId = 1
            };

            // Assert
            user.Id.Should().Be(1);
            user.Username.Should().Be("testuser");
            user.Password.Should().Be("hashedpassword");
            user.RoleId.Should().Be(1);
        }

        [Fact]
        public void Role_ShouldWork()
        {
            // Arrange & Act
            var role = new Role { Id = 1, Name = "admin" };

            // Assert
            role.Id.Should().Be(1);
            role.Name.Should().Be("admin");
        }

        [Theory]
        [InlineData("Electronics")]
        [InlineData("Clothing")]
        [InlineData("Books")]
        public void CategoryDto_ShouldAcceptValidNames(string categoryName)
        {
            // Arrange & Act
            var dto = new CategoryDto { Id = 1, Name = categoryName };

            // Assert
            dto.Name.Should().Be(categoryName);
        }

        [Fact]
        public void SalesInvoice_ShouldWork()
        {
            // Arrange & Act
            var invoice = new SalesInvoice
            {
                Id = 1,
                InvoiceDate = DateTime.Now,
                TotalAmount = 1500.00m,
                UserId = 1
            };

            // Assert
            invoice.Id.Should().Be(1);
            invoice.TotalAmount.Should().Be(1500.00m);
            invoice.UserId.Should().Be(1);
        }

        [Fact]
        public void PurchaseInvoice_ShouldWork()
        {
            // Arrange & Act
            var invoice = new PurchaseInvoice
            {
                Id = 1,
                InvoiceDate = DateTime.Now,
                TotalAmount = 800.00m,
                SupplierId = 1,
                UserId = 1
            };

            // Assert
            invoice.Id.Should().Be(1);
            invoice.TotalAmount.Should().Be(800.00m);
            invoice.SupplierId.Should().Be(1);
        }

        [Fact]
        public void Supplier_ShouldWork()
        {
            // Arrange & Act
            var supplier = new Supplier
            {
                Id = 1,
                Name = "Tech Supplier",
                Phone = "1234567890",
                Address = "123 Tech Street"
            };

            // Assert
            supplier.Id.Should().Be(1);
            supplier.Name.Should().Be("Tech Supplier");
            supplier.Phone.Should().Be("1234567890");
        }

        [Fact]
        public void CreateProductDto_ShouldWork()
        {
            // Arrange & Act
            var dto = new CreateProductDto
            {
                Name = "Test Product",
                CategoryId = 1,
                Barcode = "123456789"
            };

            // Assert
            dto.Name.Should().Be("Test Product");
            dto.CategoryId.Should().Be(1);
            dto.Barcode.Should().Be("123456789");
        }

        [Fact]
        public void UpdateCategoryDto_ShouldWork()
        {
            // Arrange & Act
            var dto = new UpdateCategoryDto { Id = 1, Name = "Updated Category" };

            // Assert
            dto.Id.Should().Be(1);
            dto.Name.Should().Be("Updated Category");
        }

        [Theory]
        [InlineData(1, "admin")]
        [InlineData(2, "manager")]
        [InlineData(3, "cashier")]
        public void Role_ShouldAcceptValidData(int id, string name)
        {
            // Arrange & Act
            var role = new Role { Id = id, Name = name };

            // Assert
            role.Id.Should().Be(id);
            role.Name.Should().Be(name);
        }

        [Fact]
        public void DateTime_ShouldWorkInEntities()
        {
            // Arrange & Act
            var product = new Product
            {
                Id = 1,
                Name = "Test Product",
                CreatedAt = DateTime.Now
            };

            // Assert
            product.CreatedAt.Should().BeCloseTo(DateTime.Now, TimeSpan.FromSeconds(1));
        }
    }
}
