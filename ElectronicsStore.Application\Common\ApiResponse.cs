namespace ElectronicsStore.Application.Common
{
    public class ApiResponse<T>
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public T? Data { get; set; }
        public List<string>? Errors { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public string? TraceId { get; set; }

        public ApiResponse()
        {
        }

        public ApiResponse(T data, string message = "Operation completed successfully.")
        {
            Success = true;
            Message = message;
            Data = data;
        }

        public ApiResponse(string message, List<string>? errors = null)
        {
            Success = false;
            Message = message;
            Errors = errors;
        }

        public static ApiResponse<T> SuccessResult(T data, string message = "Operation completed successfully.")
        {
            return new ApiResponse<T>(data, message);
        }

        public static ApiResponse<T> ErrorResult(string message, List<string>? errors = null)
        {
            return new ApiResponse<T>(message, errors);
        }

        public static ApiResponse<T> ErrorResult(string message, string error)
        {
            return new ApiResponse<T>(message, new List<string> { error });
        }
    }

    public class ApiResponse : ApiResponse<object>
    {
        public ApiResponse() : base()
        {
        }

        public ApiResponse(string message, List<string>? errors = null) : base(message, errors)
        {
        }

        public static ApiResponse SuccessResult(string message = "Operation completed successfully.")
        {
            return new ApiResponse
            {
                Success = true,
                Message = message
            };
        }

        public static new ApiResponse ErrorResult(string message, List<string>? errors = null)
        {
            return new ApiResponse(message, errors);
        }

        public static new ApiResponse ErrorResult(string message, string error)
        {
            return new ApiResponse(message, new List<string> { error });
        }
    }

    public class PagedResponse<T> : ApiResponse<IEnumerable<T>>
    {
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalCount { get; set; }
        public int TotalPages { get; set; }
        public bool HasPreviousPage => PageNumber > 1;
        public bool HasNextPage => PageNumber < TotalPages;

        public PagedResponse()
        {
        }

        public PagedResponse(IEnumerable<T> data, int pageNumber, int pageSize, int totalCount)
        {
            Success = true;
            Message = "Data retrieved successfully.";
            Data = data;
            PageNumber = pageNumber;
            PageSize = pageSize;
            TotalCount = totalCount;
            TotalPages = (int)Math.Ceiling((double)totalCount / pageSize);
        }

        public static PagedResponse<T> Create(IEnumerable<T> data, int pageNumber, int pageSize, int totalCount)
        {
            return new PagedResponse<T>(data, pageNumber, pageSize, totalCount);
        }
    }

    public class ValidationResponse : ApiResponse
    {
        public Dictionary<string, List<string>>? ValidationErrors { get; set; }

        public ValidationResponse(Dictionary<string, List<string>> validationErrors)
        {
            Success = false;
            Message = "Validation failed.";
            ValidationErrors = validationErrors;
        }

        public ValidationResponse(string field, string error)
        {
            Success = false;
            Message = "Validation failed.";
            ValidationErrors = new Dictionary<string, List<string>>
            {
                { field, new List<string> { error } }
            };
        }

        public static ValidationResponse Create(Dictionary<string, List<string>> validationErrors)
        {
            return new ValidationResponse(validationErrors);
        }

        public static ValidationResponse Create(string field, string error)
        {
            return new ValidationResponse(field, error);
        }
    }
}
