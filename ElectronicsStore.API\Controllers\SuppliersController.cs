using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ElectronicsStore.Application.DTOs;
using ElectronicsStore.Application.Services;

namespace ElectronicsStore.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class SuppliersController : ControllerBase
    {
        private readonly ISupplierService _supplierService;

        public SuppliersController(ISupplierService supplierService)
        {
            _supplierService = supplierService;
        }

        /// <summary>
        /// Get all suppliers
        /// </summary>
        /// <returns>List of suppliers</returns>
        [HttpGet]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<SupplierDto>>> GetSuppliers()
        {
            var suppliers = await _supplierService.GetAllSuppliersAsync();
            return Ok(suppliers);
        }

        /// <summary>
        /// Get supplier by ID
        /// </summary>
        /// <param name="id">Supplier ID</param>
        /// <returns>Supplier details</returns>
        [HttpGet("{id}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<SupplierDto>> GetSupplier(int id)
        {
            var supplier = await _supplierService.GetSupplierByIdAsync(id);
            if (supplier == null)
                return NotFound($"Supplier with ID {id} not found.");

            return Ok(supplier);
        }

        /// <summary>
        /// Create a new supplier
        /// </summary>
        /// <param name="createSupplierDto">Supplier creation data</param>
        /// <returns>Created supplier</returns>
        [HttpPost]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<SupplierDto>> CreateSupplier(CreateSupplierDto createSupplierDto)
        {
            try
            {
                // Validate model state
                if (!ModelState.IsValid)
                {
                    var errors = ModelState
                        .Where(x => x.Value?.Errors.Count > 0)
                        .ToDictionary(
                            kvp => kvp.Key,
                            kvp => kvp.Value?.Errors.Select(e => e.ErrorMessage).ToArray()
                        );
                    return BadRequest(new { message = "فشل في التحقق من صحة البيانات", errors });
                }

                var supplier = await _supplierService.CreateSupplierAsync(createSupplierDto);
                return CreatedAtAction(nameof(GetSupplier), new { id = supplier.Id }, supplier);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ داخلي في الخادم", details = ex.Message });
            }
        }

        /// <summary>
        /// Update an existing supplier
        /// </summary>
        /// <param name="id">Supplier ID</param>
        /// <param name="updateSupplierDto">Supplier update data</param>
        /// <returns>Updated supplier</returns>
        [HttpPut("{id}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<SupplierDto>> UpdateSupplier(int id, UpdateSupplierDto updateSupplierDto)
        {
            try
            {
                // Validate model state
                if (!ModelState.IsValid)
                {
                    var errors = ModelState
                        .Where(x => x.Value?.Errors.Count > 0)
                        .ToDictionary(
                            kvp => kvp.Key,
                            kvp => kvp.Value?.Errors.Select(e => e.ErrorMessage).ToArray()
                        );
                    return BadRequest(new { message = "فشل في التحقق من صحة البيانات", errors });
                }

                if (id != updateSupplierDto.Id)
                    return BadRequest(new { message = "عدم تطابق المعرف بين المسار والبيانات" });

                var supplier = await _supplierService.UpdateSupplierAsync(updateSupplierDto);
                return Ok(supplier);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ داخلي في الخادم", details = ex.Message });
            }
        }

        /// <summary>
        /// Delete a supplier
        /// </summary>
        /// <param name="id">Supplier ID</param>
        /// <returns>Success status</returns>
        [HttpDelete("{id}")]
        [Authorize(Roles = "admin")]
        public async Task<ActionResult> DeleteSupplier(int id)
        {
            try
            {
                var result = await _supplierService.DeleteSupplierAsync(id);
                if (!result)
                    return NotFound($"Supplier with ID {id} not found.");

                return NoContent();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Search suppliers
        /// </summary>
        /// <param name="searchDto">Search criteria</param>
        /// <returns>List of matching suppliers</returns>
        [HttpGet("search")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<SupplierDto>>> SearchSuppliers([FromQuery] SupplierSearchDto searchDto)
        {
            var suppliers = await _supplierService.SearchSuppliersAsync(searchDto);
            return Ok(suppliers);
        }

        /// <summary>
        /// Get suppliers by name
        /// </summary>
        /// <param name="name">Supplier name to search</param>
        /// <returns>List of matching suppliers</returns>
        [HttpGet("name/{name}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<SupplierDto>>> GetSuppliersByName(string name)
        {
            var suppliers = await _supplierService.GetSuppliersByNameAsync(name);
            return Ok(suppliers);
        }

        /// <summary>
        /// Get top suppliers by product count
        /// </summary>
        /// <param name="count">Number of suppliers to return</param>
        /// <returns>List of top suppliers</returns>
        [HttpGet("top")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<SupplierDto>>> GetTopSuppliers([FromQuery] int count = 10)
        {
            var suppliers = await _supplierService.GetTopSuppliersAsync(count);
            return Ok(suppliers);
        }

        /// <summary>
        /// Get supplier statistics
        /// </summary>
        /// <returns>Supplier statistics</returns>
        [HttpGet("statistics")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<object>> GetSupplierStatistics()
        {
            var statistics = await _supplierService.GetSupplierStatisticsAsync();
            return Ok(statistics);
        }

        /// <summary>
        /// Get products for a specific supplier
        /// </summary>
        /// <param name="id">Supplier ID</param>
        /// <returns>List of supplier products</returns>
        [HttpGet("{id}/products")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<object>>> GetSupplierProducts(int id)
        {
            var products = await _supplierService.GetSupplierProductsAsync(id);
            return Ok(products);
        }

        /// <summary>
        /// Get paged suppliers
        /// </summary>
        /// <param name="pageNumber">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="searchDto">Search criteria (optional)</param>
        /// <returns>Paged list of suppliers</returns>
        [HttpGet("paged")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<object>> GetPagedSuppliers(
            [FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10, [FromQuery] SupplierSearchDto? searchDto = null)
        {
            var (suppliers, totalCount) = await _supplierService.GetPagedSuppliersAsync(pageNumber, pageSize, searchDto);
            
            return Ok(new
            {
                Suppliers = suppliers,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            });
        }

        /// <summary>
        /// Check if supplier name exists
        /// </summary>
        /// <param name="name">Supplier name to check</param>
        /// <param name="excludeId">Supplier ID to exclude from check (for updates)</param>
        /// <returns>Availability status</returns>
        [HttpGet("check-name/{name}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<object>> CheckSupplierName(string name, [FromQuery] int? excludeId = null)
        {
            var exists = await _supplierService.SupplierNameExistsAsync(name, excludeId);
            return Ok(new { Name = name, Exists = exists, Available = !exists });
        }

        /// <summary>
        /// Check if supplier can be deleted
        /// </summary>
        /// <param name="id">Supplier ID</param>
        /// <returns>Deletion status</returns>
        [HttpGet("{id}/can-delete")]
        [Authorize(Roles = "admin")]
        public async Task<ActionResult<object>> CanDeleteSupplier(int id)
        {
            var canDelete = await _supplierService.CanDeleteSupplierAsync(id);
            return Ok(new { SupplierId = id, CanDelete = canDelete });
        }
    }
}
