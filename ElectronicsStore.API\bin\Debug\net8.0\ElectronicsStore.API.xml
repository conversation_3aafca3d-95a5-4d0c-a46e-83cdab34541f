<?xml version="1.0"?>
<doc>
    <assembly>
        <name>ElectronicsStore.API</name>
    </assembly>
    <members>
        <member name="M:ElectronicsStore.API.Controllers.AuthController.Login(ElectronicsStore.Application.DTOs.LoginDto)">
            <summary>
            User login
            </summary>
            <param name="loginDto">Login credentials</param>
            <returns>JWT token and user information</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.AuthController.GetCurrentUser">
            <summary>
            Get current authenticated user information
            </summary>
            <returns>Current user details</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.AuthController.RefreshToken(ElectronicsStore.Application.DTOs.RefreshTokenDto)">
            <summary>
            Refresh JWT token
            </summary>
            <param name="refreshTokenDto">Current token and refresh token</param>
            <returns>New JWT token</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.AuthController.ValidateToken(System.String)">
            <summary>
            Validate token
            </summary>
            <param name="token">JWT token to validate</param>
            <returns>Token validation result</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.CategoriesController.GetCategories">
            <summary>
            Get all categories
            </summary>
            <returns>List of categories</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.CategoriesController.GetCategory(System.Int32)">
            <summary>
            Get category by ID
            </summary>
            <param name="id">Category ID</param>
            <returns>Category details</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.CategoriesController.CreateCategory(ElectronicsStore.Application.DTOs.CreateCategoryDto)">
            <summary>
            Create a new category
            </summary>
            <param name="createCategoryDto">Category creation data</param>
            <returns>Created category</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.CategoriesController.UpdateCategory(System.Int32,ElectronicsStore.Application.DTOs.UpdateCategoryDto)">
            <summary>
            Update an existing category
            </summary>
            <param name="id">Category ID</param>
            <param name="updateCategoryDto">Category update data</param>
            <returns>Updated category</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.CategoriesController.DeleteCategory(System.Int32)">
            <summary>
            Delete a category
            </summary>
            <param name="id">Category ID</param>
            <returns>Success status</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.InventoryController.GetAllMovements">
            <summary>
            Get all inventory movements
            </summary>
            <returns>List of inventory movements</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.InventoryController.GetMovement(System.Int32)">
            <summary>
            Get inventory movement by ID
            </summary>
            <param name="id">Movement ID</param>
            <returns>Movement details</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.InventoryController.CreateMovement(ElectronicsStore.Application.DTOs.CreateInventoryLogDto)">
            <summary>
            Create a new inventory movement
            </summary>
            <param name="createInventoryLogDto">Movement creation data</param>
            <returns>Created movement</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.InventoryController.GetMovementsByProduct(System.Int32)">
            <summary>
            Get movements by product
            </summary>
            <param name="productId">Product ID</param>
            <returns>List of product movements</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.InventoryController.GetMovementsByType(System.String)">
            <summary>
            Get movements by type
            </summary>
            <param name="movementType">Movement type</param>
            <returns>List of movements by type</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.InventoryController.GetRecentMovements(System.Int32)">
            <summary>
            Get recent movements
            </summary>
            <param name="count">Number of movements to return</param>
            <returns>List of recent movements</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.InventoryController.GetCurrentStock(System.Int32)">
            <summary>
            Get current stock for a product
            </summary>
            <param name="productId">Product ID</param>
            <returns>Current stock quantity</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.InventoryController.GetStockSummary">
            <summary>
            Get stock summary for all products
            </summary>
            <returns>Stock summary</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.InventoryController.AdjustStock(ElectronicsStore.Application.DTOs.StockAdjustmentDto)">
            <summary>
            Adjust stock for a product
            </summary>
            <param name="adjustmentDto">Stock adjustment data</param>
            <returns>Success status</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.InventoryController.GetLowStockAlerts(System.Int32)">
            <summary>
            Get low stock alerts
            </summary>
            <param name="threshold">Low stock threshold</param>
            <returns>List of low stock products</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.InventoryController.GetOutOfStockProducts">
            <summary>
            Get out of stock products
            </summary>
            <returns>List of out of stock products</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.InventoryController.GetCriticalStockAlerts(System.Int32)">
            <summary>
            Get critical stock alerts
            </summary>
            <param name="criticalThreshold">Critical stock threshold</param>
            <returns>List of critical stock products</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.InventoryController.GetInventoryValuationReport">
            <summary>
            Get inventory valuation report
            </summary>
            <returns>Inventory valuation report</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.InventoryController.GetTotalInventoryValue">
            <summary>
            Get total inventory value
            </summary>
            <returns>Total inventory value</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.InventoryController.GetMovementsByTypeReport(System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            Get movements by type report
            </summary>
            <param name="fromDate">Start date (optional)</param>
            <param name="toDate">End date (optional)</param>
            <returns>Movements by type report</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.InventoryController.GetDailyMovementsReport(System.DateTime,System.DateTime)">
            <summary>
            Get daily movements report
            </summary>
            <param name="fromDate">Start date</param>
            <param name="toDate">End date</param>
            <returns>Daily movements report</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.InventoryController.TransferStock(ElectronicsStore.Application.DTOs.StockTransferDto)">
            <summary>
            Transfer stock between products
            </summary>
            <param name="transferDto">Stock transfer data</param>
            <returns>Success status</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.InventoryController.SearchMovements(ElectronicsStore.Application.DTOs.InventorySearchDto)">
            <summary>
            Search inventory movements
            </summary>
            <param name="searchDto">Search criteria</param>
            <returns>List of matching movements</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.InventoryController.GetInventoryStatistics(System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            Get inventory statistics
            </summary>
            <param name="fromDate">Start date (optional)</param>
            <param name="toDate">End date (optional)</param>
            <returns>Inventory statistics</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.InventoryController.GetCostAnalysis(System.Int32)">
            <summary>
            Get cost analysis for a product
            </summary>
            <param name="productId">Product ID</param>
            <returns>Cost analysis</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.InventoryController.GetAuditTrail(System.Int32,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            Get audit trail for a product
            </summary>
            <param name="productId">Product ID</param>
            <param name="fromDate">Start date (optional)</param>
            <param name="toDate">End date (optional)</param>
            <returns>Product audit trail</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.InventoryController.GetPagedMovements(System.Int32,System.Int32,ElectronicsStore.Application.DTOs.InventorySearchDto)">
            <summary>
            Get paged inventory movements
            </summary>
            <param name="pageNumber">Page number</param>
            <param name="pageSize">Page size</param>
            <param name="searchDto">Search criteria (optional)</param>
            <returns>Paged list of movements</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ProductsController.GetProducts">
            <summary>
            Get all products
            </summary>
            <returns>List of products</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ProductsController.GetProduct(System.Int32)">
            <summary>
            Get product by ID
            </summary>
            <param name="id">Product ID</param>
            <returns>Product details</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ProductsController.GetProductByBarcode(System.String)">
            <summary>
            Get product by barcode
            </summary>
            <param name="barcode">Product barcode</param>
            <returns>Product details</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ProductsController.SearchProducts(ElectronicsStore.Application.DTOs.ProductSearchDto)">
            <summary>
            Search products
            </summary>
            <param name="searchDto">Search criteria</param>
            <returns>List of matching products</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ProductsController.GetProductsByCategory(System.Int32)">
            <summary>
            Get products by category
            </summary>
            <param name="categoryId">Category ID</param>
            <returns>List of products in category</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ProductsController.CreateProduct(ElectronicsStore.Application.DTOs.CreateProductDto)">
            <summary>
            Create a new product
            </summary>
            <param name="createProductDto">Product creation data</param>
            <returns>Created product</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ProductsController.UpdateProduct(System.Int32,ElectronicsStore.Application.DTOs.UpdateProductDto)">
            <summary>
            Update an existing product
            </summary>
            <param name="id">Product ID</param>
            <param name="updateProductDto">Product update data</param>
            <returns>Updated product</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ProductsController.DeleteProduct(System.Int32)">
            <summary>
            Delete a product
            </summary>
            <param name="id">Product ID</param>
            <returns>Success status</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ProductsController.GetStockSummary">
            <summary>
            Get stock summary
            </summary>
            <returns>Stock summary for all products</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ProductsController.GetLowStockProducts(System.Int32)">
            <summary>
            Get low stock products
            </summary>
            <param name="threshold">Low stock threshold</param>
            <returns>List of low stock products</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ProductsController.GetOutOfStockProducts">
            <summary>
            Get out of stock products
            </summary>
            <returns>List of out of stock products</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ProductsController.AdjustStock(System.Int32,System.Object)">
            <summary>
            Adjust product stock
            </summary>
            <param name="productId">Product ID</param>
            <param name="request">Stock adjustment request containing quantity and reason</param>
            <returns>Success status</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ProductsController.GetTopSellingProducts(System.Int32,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            Get top selling products
            </summary>
            <param name="count">Number of products to return</param>
            <param name="fromDate">Start date (optional)</param>
            <param name="toDate">End date (optional)</param>
            <returns>List of top selling products</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ProductsController.GetProductStatistics">
            <summary>
            Get product statistics
            </summary>
            <returns>Product statistics</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ProductsController.GetPagedProducts(System.Int32,System.Int32,ElectronicsStore.Application.DTOs.ProductSearchDto)">
            <summary>
            Get paged products
            </summary>
            <param name="pageNumber">Page number</param>
            <param name="pageSize">Page size</param>
            <param name="searchDto">Search criteria (optional)</param>
            <returns>Paged list of products</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ProductsController.CheckBarcode(System.String,System.Nullable{System.Int32})">
            <summary>
            Check if barcode exists
            </summary>
            <param name="barcode">Barcode to check</param>
            <param name="excludeId">Product ID to exclude from check (for updates)</param>
            <returns>Availability status</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.PurchasesController.GetPurchaseInvoices">
            <summary>
            Get all purchase invoices
            </summary>
            <returns>List of purchase invoices</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.PurchasesController.GetPurchaseInvoice(System.Int32)">
            <summary>
            Get purchase invoice by ID
            </summary>
            <param name="id">Purchase invoice ID</param>
            <returns>Purchase invoice details</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.PurchasesController.CreatePurchaseInvoice(ElectronicsStore.Application.DTOs.CreatePurchaseInvoiceDto)">
            <summary>
            Create a new purchase invoice
            </summary>
            <param name="createDto">Purchase invoice creation data</param>
            <returns>Created purchase invoice</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.PurchasesController.UpdatePurchaseInvoice(System.Int32,ElectronicsStore.Application.DTOs.UpdatePurchaseInvoiceDto)">
            <summary>
            Update an existing purchase invoice
            </summary>
            <param name="id">Purchase invoice ID</param>
            <param name="updateDto">Purchase invoice update data</param>
            <returns>Updated purchase invoice</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.PurchasesController.DeletePurchaseInvoice(System.Int32)">
            <summary>
            Delete a purchase invoice
            </summary>
            <param name="id">Purchase invoice ID</param>
            <returns>Success status</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.PurchasesController.SearchPurchaseInvoices(ElectronicsStore.Application.DTOs.PurchaseSearchDto)">
            <summary>
            Search purchase invoices
            </summary>
            <param name="searchDto">Search criteria</param>
            <returns>List of matching purchase invoices</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.PurchasesController.GetPurchaseInvoiceByNumber(System.String)">
            <summary>
            Get purchase invoice by invoice number
            </summary>
            <param name="invoiceNumber">Invoice number</param>
            <returns>Purchase invoice details</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.PurchasesController.GetPurchaseInvoicesBySupplier(System.Int32)">
            <summary>
            Get purchase invoices by supplier
            </summary>
            <param name="supplierId">Supplier ID</param>
            <returns>List of purchase invoices for the supplier</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.PurchasesController.GetPurchaseInvoicesByUser(System.Int32)">
            <summary>
            Get purchase invoices by user
            </summary>
            <param name="userId">User ID</param>
            <returns>List of purchase invoices created by the user</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.PurchasesController.GetPurchaseInvoicesByDateRange(System.DateTime,System.DateTime)">
            <summary>
            Get purchase invoices by date range
            </summary>
            <param name="fromDate">Start date</param>
            <param name="toDate">End date</param>
            <returns>List of purchase invoices in the date range</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.PurchasesController.GetTodayPurchases">
            <summary>
            Get today's purchases
            </summary>
            <returns>List of today's purchase invoices</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.PurchasesController.GetTodayPurchasesTotal">
            <summary>
            Get today's purchases total
            </summary>
            <returns>Total amount of today's purchases</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.PurchasesController.GetDailyPurchaseReport(System.DateTime)">
            <summary>
            Get daily purchase report
            </summary>
            <param name="date">Report date</param>
            <returns>Daily purchase report</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.PurchasesController.GetMonthlyPurchaseReport(System.Int32,System.Int32)">
            <summary>
            Get monthly purchase report
            </summary>
            <param name="year">Report year</param>
            <param name="month">Report month</param>
            <returns>Monthly purchase report</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.PurchasesController.GetYearlyPurchaseReport(System.Int32)">
            <summary>
            Get yearly purchase report
            </summary>
            <param name="year">Report year</param>
            <returns>Yearly purchase report</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.PurchasesController.GetPurchaseStatistics">
            <summary>
            Get purchase statistics
            </summary>
            <returns>Purchase statistics</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.PurchasesController.GetTopSuppliers(System.Int32)">
            <summary>
            Get top suppliers by purchase amount
            </summary>
            <param name="count">Number of suppliers to return</param>
            <returns>List of top suppliers</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.PurchasesController.GetTopProducts(System.Int32)">
            <summary>
            Get top products by purchase amount
            </summary>
            <param name="count">Number of products to return</param>
            <returns>List of top products</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.PurchasesController.GetPagedPurchaseInvoices(System.Int32,System.Int32,ElectronicsStore.Application.DTOs.PurchaseSearchDto)">
            <summary>
            Get paged purchase invoices
            </summary>
            <param name="pageNumber">Page number</param>
            <param name="pageSize">Page size</param>
            <param name="searchDto">Search criteria (optional)</param>
            <returns>Paged list of purchase invoices</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.PurchasesController.GetPurchaseInvoiceDetails(System.Int32)">
            <summary>
            Get purchase invoice details
            </summary>
            <param name="invoiceId">Purchase invoice ID</param>
            <returns>List of invoice details</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.PurchasesController.GetPurchaseInvoiceDetail(System.Int32)">
            <summary>
            Get purchase invoice detail by ID
            </summary>
            <param name="detailId">Detail ID</param>
            <returns>Purchase invoice detail</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.PurchasesController.CheckInvoiceNumber(System.String,System.Nullable{System.Int32})">
            <summary>
            Check if invoice number exists
            </summary>
            <param name="invoiceNumber">Invoice number to check</param>
            <param name="excludeId">Invoice ID to exclude from check (for updates)</param>
            <returns>Availability status</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.PurchasesController.CanDeletePurchaseInvoice(System.Int32)">
            <summary>
            Check if purchase invoice can be deleted
            </summary>
            <param name="id">Purchase invoice ID</param>
            <returns>Deletion status</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ReturnsController.CreateSalesReturn(ElectronicsStore.Application.DTOs.CreateSalesReturnDto)">
            <summary>
            Create a new sales return
            </summary>
            <param name="createDto">Sales return creation data</param>
            <returns>Created sales return</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ReturnsController.GetSalesReturn(System.Int32)">
            <summary>
            Get sales return by ID
            </summary>
            <param name="id">Sales return ID</param>
            <returns>Sales return details</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ReturnsController.GetAllSalesReturns">
            <summary>
            Get all sales returns
            </summary>
            <returns>List of sales returns</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ReturnsController.GetSalesReturnsByInvoice(System.Int32)">
            <summary>
            Get sales returns by invoice ID
            </summary>
            <param name="invoiceId">Sales invoice ID</param>
            <returns>List of sales returns for the invoice</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ReturnsController.GetSalesReturnsByProduct(System.Int32)">
            <summary>
            Get sales returns by product ID
            </summary>
            <param name="productId">Product ID</param>
            <returns>List of sales returns for the product</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ReturnsController.GetSalesReturnsByUser(System.Int32)">
            <summary>
            Get sales returns by user ID
            </summary>
            <param name="userId">User ID</param>
            <returns>List of sales returns created by the user</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ReturnsController.DeleteSalesReturn(System.Int32)">
            <summary>
            Delete a sales return
            </summary>
            <param name="id">Sales return ID</param>
            <returns>Success status</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ReturnsController.CreatePurchaseReturn(ElectronicsStore.Application.DTOs.CreatePurchaseReturnDto)">
            <summary>
            Create a new purchase return
            </summary>
            <param name="createDto">Purchase return creation data</param>
            <returns>Created purchase return</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ReturnsController.GetPurchaseReturn(System.Int32)">
            <summary>
            Get purchase return by ID
            </summary>
            <param name="id">Purchase return ID</param>
            <returns>Purchase return details</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ReturnsController.GetAllPurchaseReturns">
            <summary>
            Get all purchase returns
            </summary>
            <returns>List of purchase returns</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ReturnsController.GetPurchaseReturnsByInvoice(System.Int32)">
            <summary>
            Get purchase returns by invoice ID
            </summary>
            <param name="invoiceId">Purchase invoice ID</param>
            <returns>List of purchase returns for the invoice</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ReturnsController.GetPurchaseReturnsByProduct(System.Int32)">
            <summary>
            Get purchase returns by product ID
            </summary>
            <param name="productId">Product ID</param>
            <returns>List of purchase returns for the product</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ReturnsController.GetPurchaseReturnsByUser(System.Int32)">
            <summary>
            Get purchase returns by user ID
            </summary>
            <param name="userId">User ID</param>
            <returns>List of purchase returns created by the user</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ReturnsController.DeletePurchaseReturn(System.Int32)">
            <summary>
            Delete a purchase return
            </summary>
            <param name="id">Purchase return ID</param>
            <returns>Success status</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ReturnsController.GetAllReturns">
            <summary>
            Get all returns (sales and purchase combined)
            </summary>
            <returns>List of all returns</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ReturnsController.SearchReturns(ElectronicsStore.Application.DTOs.ReturnSearchDto)">
            <summary>
            Search returns
            </summary>
            <param name="searchDto">Search criteria</param>
            <returns>List of matching returns</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ReturnsController.GetReturnsByDateRange(System.DateTime,System.DateTime)">
            <summary>
            Get returns by date range
            </summary>
            <param name="fromDate">Start date</param>
            <param name="toDate">End date</param>
            <returns>List of returns in the date range</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ReturnsController.GetTodayReturns">
            <summary>
            Get today's returns
            </summary>
            <returns>List of today's returns</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ReturnsController.GetReturnStatistics">
            <summary>
            Get return statistics
            </summary>
            <returns>Return statistics</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ReturnsController.GetDailyReturnReport(System.DateTime)">
            <summary>
            Get daily return report
            </summary>
            <param name="date">Report date</param>
            <returns>Daily return report</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ReturnsController.GetTopReturnedProducts(System.Int32)">
            <summary>
            Get top returned products
            </summary>
            <param name="count">Number of products to return</param>
            <returns>List of top returned products</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ReturnsController.GetReturnReasonAnalysis">
            <summary>
            Get return reason analysis
            </summary>
            <returns>Return reason analysis</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.ReturnsController.CanReturnSalesItem(System.Int32,System.Int32,System.Int32)">
            <summary>
            Check if sales item can be returned
            </summary>
            <param name="salesInvoiceId">Sales invoice ID</param>
            <param name="productId">Product ID</param>
            <param name="quantity">Quantity to return</param>
            <returns>Validation result</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.RolesController.GetRoles">
            <summary>
            Get all roles
            </summary>
            <returns>List of roles</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.RolesController.GetRole(System.Int32)">
            <summary>
            Get role by ID
            </summary>
            <param name="id">Role ID</param>
            <returns>Role details</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.RolesController.CreateRole(ElectronicsStore.Domain.Entities.Role)">
            <summary>
            Create a new role
            </summary>
            <param name="role">Role data</param>
            <returns>Created role</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.SalesController.GetSalesInvoices">
            <summary>
            Get all sales invoices
            </summary>
            <returns>List of sales invoices</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.SalesController.GetSalesInvoice(System.Int32)">
            <summary>
            Get sales invoice by ID
            </summary>
            <param name="id">Sales invoice ID</param>
            <returns>Sales invoice details</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.SalesController.GetSalesInvoiceByNumber(System.String)">
            <summary>
            Get sales invoice by invoice number
            </summary>
            <param name="invoiceNumber">Invoice number</param>
            <returns>Sales invoice details</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.SalesController.CreateSalesInvoice(ElectronicsStore.Application.DTOs.CreateSalesInvoiceDto)">
            <summary>
            Create a new sales invoice
            </summary>
            <param name="createSalesInvoiceDto">Sales invoice creation data</param>
            <returns>Created sales invoice</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.SalesController.DeleteSalesInvoice(System.Int32)">
            <summary>
            Delete a sales invoice
            </summary>
            <param name="id">Sales invoice ID</param>
            <returns>Success status</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.SalesController.SearchSalesInvoices(ElectronicsStore.Application.DTOs.SalesSearchDto)">
            <summary>
            Search sales invoices
            </summary>
            <param name="searchDto">Search criteria</param>
            <returns>List of matching sales invoices</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.SalesController.GetSalesInvoicesByCustomer(System.String)">
            <summary>
            Get sales invoices by customer
            </summary>
            <param name="customerName">Customer name</param>
            <returns>List of customer's sales invoices</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.SalesController.GetSalesInvoicesByUser(System.Int32)">
            <summary>
            Get sales invoices by user
            </summary>
            <param name="userId">User ID</param>
            <returns>List of user's sales invoices</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.SalesController.GetTodaysSales">
            <summary>
            Get today's sales
            </summary>
            <returns>List of today's sales invoices</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.SalesController.GetSalesStatistics(System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            Get sales statistics
            </summary>
            <param name="fromDate">Start date (optional)</param>
            <param name="toDate">End date (optional)</param>
            <returns>Sales statistics</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.SalesController.GetDailySalesReport(System.DateTime,System.DateTime)">
            <summary>
            Get daily sales report
            </summary>
            <param name="fromDate">Start date</param>
            <param name="toDate">End date</param>
            <returns>Daily sales report</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.SalesController.GetMonthlySalesReport(System.Int32)">
            <summary>
            Get monthly sales report
            </summary>
            <param name="year">Year</param>
            <returns>Monthly sales report</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.SalesController.GetSalesByPaymentMethod(System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            Get sales by payment method
            </summary>
            <param name="fromDate">Start date (optional)</param>
            <param name="toDate">End date (optional)</param>
            <returns>Sales breakdown by payment method</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.SalesController.GetTopCustomers(System.Int32,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            Get top customers
            </summary>
            <param name="count">Number of customers to return</param>
            <param name="fromDate">Start date (optional)</param>
            <param name="toDate">End date (optional)</param>
            <returns>List of top customers</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.SalesController.GetTopSellingProducts(System.Int32,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            Get top selling products
            </summary>
            <param name="count">Number of products to return</param>
            <param name="fromDate">Start date (optional)</param>
            <param name="toDate">End date (optional)</param>
            <returns>List of top selling products</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.SalesController.OverrideSale(System.Int32,System.String)">
            <summary>
            Override a sales invoice
            </summary>
            <param name="id">Sales invoice ID</param>
            <param name="reason">Override reason</param>
            <returns>Success status</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.SalesController.ProcessReturn(System.Int32,System.Object)">
            <summary>
            Process a sales return
            </summary>
            <param name="salesInvoiceId">Sales invoice ID</param>
            <param name="request">Return request data</param>
            <returns>Success status</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.SalesController.GetPagedSalesInvoices(System.Int32,System.Int32,ElectronicsStore.Application.DTOs.SalesSearchDto)">
            <summary>
            Get paged sales invoices
            </summary>
            <param name="pageNumber">Page number</param>
            <param name="pageSize">Page size</param>
            <param name="searchDto">Search criteria (optional)</param>
            <returns>Paged list of sales invoices</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.SuppliersController.GetSuppliers">
            <summary>
            Get all suppliers
            </summary>
            <returns>List of suppliers</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.SuppliersController.GetSupplier(System.Int32)">
            <summary>
            Get supplier by ID
            </summary>
            <param name="id">Supplier ID</param>
            <returns>Supplier details</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.SuppliersController.CreateSupplier(ElectronicsStore.Application.DTOs.CreateSupplierDto)">
            <summary>
            Create a new supplier
            </summary>
            <param name="createSupplierDto">Supplier creation data</param>
            <returns>Created supplier</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.SuppliersController.UpdateSupplier(System.Int32,ElectronicsStore.Application.DTOs.UpdateSupplierDto)">
            <summary>
            Update an existing supplier
            </summary>
            <param name="id">Supplier ID</param>
            <param name="updateSupplierDto">Supplier update data</param>
            <returns>Updated supplier</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.SuppliersController.DeleteSupplier(System.Int32)">
            <summary>
            Delete a supplier
            </summary>
            <param name="id">Supplier ID</param>
            <returns>Success status</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.SuppliersController.SearchSuppliers(ElectronicsStore.Application.DTOs.SupplierSearchDto)">
            <summary>
            Search suppliers
            </summary>
            <param name="searchDto">Search criteria</param>
            <returns>List of matching suppliers</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.SuppliersController.GetSuppliersByName(System.String)">
            <summary>
            Get suppliers by name
            </summary>
            <param name="name">Supplier name to search</param>
            <returns>List of matching suppliers</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.SuppliersController.GetTopSuppliers(System.Int32)">
            <summary>
            Get top suppliers by product count
            </summary>
            <param name="count">Number of suppliers to return</param>
            <returns>List of top suppliers</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.SuppliersController.GetSupplierStatistics">
            <summary>
            Get supplier statistics
            </summary>
            <returns>Supplier statistics</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.SuppliersController.GetSupplierProducts(System.Int32)">
            <summary>
            Get products for a specific supplier
            </summary>
            <param name="id">Supplier ID</param>
            <returns>List of supplier products</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.SuppliersController.GetPagedSuppliers(System.Int32,System.Int32,ElectronicsStore.Application.DTOs.SupplierSearchDto)">
            <summary>
            Get paged suppliers
            </summary>
            <param name="pageNumber">Page number</param>
            <param name="pageSize">Page size</param>
            <param name="searchDto">Search criteria (optional)</param>
            <returns>Paged list of suppliers</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.SuppliersController.CheckSupplierName(System.String,System.Nullable{System.Int32})">
            <summary>
            Check if supplier name exists
            </summary>
            <param name="name">Supplier name to check</param>
            <param name="excludeId">Supplier ID to exclude from check (for updates)</param>
            <returns>Availability status</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.SuppliersController.CanDeleteSupplier(System.Int32)">
            <summary>
            Check if supplier can be deleted
            </summary>
            <param name="id">Supplier ID</param>
            <returns>Deletion status</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.UsersController.GetUsers">
            <summary>
            Get all users
            </summary>
            <returns>List of users</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.UsersController.GetUser(System.Int32)">
            <summary>
            Get user by ID
            </summary>
            <param name="id">User ID</param>
            <returns>User details</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.UsersController.CreateUser(ElectronicsStore.Application.DTOs.CreateUserDto)">
            <summary>
            Create a new user
            </summary>
            <param name="createUserDto">User creation data</param>
            <returns>Created user</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.UsersController.UpdateUser(System.Int32,ElectronicsStore.Application.DTOs.UpdateUserDto)">
            <summary>
            Update an existing user
            </summary>
            <param name="id">User ID</param>
            <param name="updateUserDto">User update data</param>
            <returns>Updated user</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.UsersController.DeleteUser(System.Int32)">
            <summary>
            Delete a user
            </summary>
            <param name="id">User ID</param>
            <returns>Success status</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.UsersController.ChangePassword(ElectronicsStore.Application.DTOs.ChangePasswordDto)">
            <summary>
            Change user password
            </summary>
            <param name="changePasswordDto">Password change data</param>
            <returns>Success status</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.UsersController.CheckUsername(System.String,System.Nullable{System.Int32})">
            <summary>
            Check if username exists
            </summary>
            <param name="username">Username to check</param>
            <param name="excludeId">User ID to exclude from check (for updates)</param>
            <returns>Availability status</returns>
        </member>
    </members>
</doc>
