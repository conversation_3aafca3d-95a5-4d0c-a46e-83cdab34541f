using System.ComponentModel.DataAnnotations;

namespace ElectronicsStore.Application.DTOs
{
    public class ExpenseDto
    {
        public int Id { get; set; }
        public string ExpenseType { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string? Note { get; set; }
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
    }

    public class CreateExpenseDto
    {
        [Required(ErrorMessage = "نوع المصروف مطلوب")]
        [MaxLength(100, ErrorMessage = "نوع المصروف لا يمكن أن يتجاوز 100 حرف")]
        public string ExpenseType { get; set; } = string.Empty;

        [Required(ErrorMessage = "المبلغ مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "المبلغ يجب أن يكون أكبر من صفر")]
        public decimal Amount { get; set; }

        [MaxLength(200, ErrorMessage = "الملاحظة لا يمكن أن تتجاوز 200 حرف")]
        public string? Note { get; set; }

        public int UserId { get; set; }
    }

    public class UpdateExpenseDto
    {
        [Required(ErrorMessage = "نوع المصروف مطلوب")]
        [MaxLength(100, ErrorMessage = "نوع المصروف لا يمكن أن يتجاوز 100 حرف")]
        public string ExpenseType { get; set; } = string.Empty;

        [Required(ErrorMessage = "المبلغ مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "المبلغ يجب أن يكون أكبر من صفر")]
        public decimal Amount { get; set; }

        [MaxLength(200, ErrorMessage = "الملاحظة لا يمكن أن تتجاوز 200 حرف")]
        public string? Note { get; set; }
    }

    public class ExpenseSearchDto
    {
        public string? ExpenseType { get; set; }
        public decimal? MinAmount { get; set; }
        public decimal? MaxAmount { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int? UserId { get; set; }
        public string? Note { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 10;
    }

    public class ExpenseStatisticsDto
    {
        public int TotalExpenses { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal AverageAmount { get; set; }
        public int TodayExpenses { get; set; }
        public decimal TodayAmount { get; set; }
        public int MonthExpenses { get; set; }
        public decimal MonthAmount { get; set; }
        public List<ExpenseTypeStatDto> ExpenseTypeBreakdown { get; set; } = new();
        public List<MonthlyExpenseDto> MonthlyTrend { get; set; } = new();
    }

    public class ExpenseTypeStatDto
    {
        public string ExpenseType { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal AverageAmount { get; set; }
        public decimal Percentage { get; set; }
    }

    public class MonthlyExpenseDto
    {
        public int Year { get; set; }
        public int Month { get; set; }
        public string MonthName { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal AverageAmount { get; set; }
    }

    public class ExpenseReportDto
    {
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public int TotalExpenses { get; set; }
        public decimal TotalAmount { get; set; }
        public List<ExpenseDto> Expenses { get; set; } = new();
        public List<ExpenseTypeStatDto> TypeBreakdown { get; set; } = new();
        public List<UserExpenseDto> UserBreakdown { get; set; } = new();
    }

    public class UserExpenseDto
    {
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal AverageAmount { get; set; }
    }
}
