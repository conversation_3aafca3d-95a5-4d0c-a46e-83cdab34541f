using ElectronicsStore.Domain.Entities;

namespace ElectronicsStore.Application.Interfaces
{
    public interface ISalesRepository : IGenericRepository<SalesInvoice>
    {
        // Sales-specific methods
        Task<SalesInvoice?> GetByInvoiceNumberAsync(string invoiceNumber);
        Task<IEnumerable<SalesInvoice>> GetByCustomerAsync(string customerName);
        Task<IEnumerable<SalesInvoice>> GetByUserAsync(int userId);
        Task<IEnumerable<SalesInvoice>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<SalesInvoice>> GetByPaymentMethodAsync(string paymentMethod);
        
        // Sales with details
        Task<SalesInvoice?> GetWithDetailsAsync(int id);
        Task<IEnumerable<SalesInvoice>> GetWithDetailsAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<SalesInvoice>> GetTodaysSalesAsync();
        
        // Sales analytics
        Task<decimal> GetTotalSalesAmountAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<int> GetTotalSalesCountAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<decimal> GetAverageSaleAmountAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<object>> GetSalesByPaymentMethodAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<object>> GetDailySalesReportAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<object>> GetMonthlySalesReportAsync(int year);
        
        // Top customers and products
        Task<IEnumerable<object>> GetTopCustomersAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<object>> GetTopSellingProductsAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null);
        
        // Override and discount analysis
        Task<IEnumerable<SalesInvoice>> GetOverriddenSalesAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<decimal> GetTotalDiscountsAsync(DateTime? fromDate = null, DateTime? toDate = null);
        
        // Pagination
        Task<(IEnumerable<SalesInvoice> Sales, int TotalCount)> GetPagedSalesAsync(
            int pageNumber, int pageSize, string? searchTerm = null, DateTime? fromDate = null, DateTime? toDate = null);
    }
}
